package com.wunding.learn.certification.service.enums;

import com.wunding.learn.common.context.user.UserThreadContext;

/**
 * <AUTHOR>
 * @date 2023/3/28
 */
public enum CertificationRedisKeyEnum {
    CERTIFICATION_CODE_ENUM("CertificationCodeEnum");

    private String key;

    CertificationRedisKeyEnum(String key){this.key = key;}

    public String getKey() {
        return key + ":" + UserThreadContext.getTenantId();
    }
}
