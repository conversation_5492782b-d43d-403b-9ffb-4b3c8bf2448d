package com.wunding.learn.business.view.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.business.view.service.aspect.DS;
import com.wunding.learn.business.view.service.mapper.evaluation.BusinessEvaluationMapper;
import com.wunding.learn.business.view.service.model.evaluation.Evaluation;
import com.wunding.learn.business.view.service.model.project.ProjectJoinUserEvalByTimeQuery;
import com.wunding.learn.business.view.service.model.project.ProjectJoinUserOtherDataDTO;
import com.wunding.learn.business.view.service.service.BusinessEvaluationService;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @date 2023/6/2
 */
@Service("businessEvaluationService")
@Slf4j
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@DS("evaluation")
public class BusinessEvaluationServiceImpl extends ServiceImpl<BusinessEvaluationMapper, Evaluation> implements
    BusinessEvaluationService {

    @Override
    public List<ProjectJoinUserOtherDataDTO> findProjectJoinUserEvalInfo(ProjectJoinUserEvalByTimeQuery query) {
        if (CollectionUtils.isEmpty(query.getQueryList())) {
            return new ArrayList<>();
        }
        return baseMapper.findProjectJoinUserEvalInfo(query);
    }
}
