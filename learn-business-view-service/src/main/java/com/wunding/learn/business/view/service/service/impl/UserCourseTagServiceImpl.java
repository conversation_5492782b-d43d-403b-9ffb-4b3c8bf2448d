package com.wunding.learn.business.view.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wunding.learn.business.view.service.aspect.DS;
import com.wunding.learn.business.view.service.mapper.course.BusinessViewUserCourseTagMapper;
import com.wunding.learn.business.view.service.model.course.UserCourseTag;
import com.wunding.learn.business.view.service.service.IUserCourseTagService;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.mybatis.service.impl.BaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">guoyong</a>
 * @since 2025/5/21 17:43
 */
@Slf4j
@Service("businessUserCourseTagService")
@DS("course")
public class UserCourseTagServiceImpl extends BaseServiceImpl<BusinessViewUserCourseTagMapper, UserCourseTag> implements
    IUserCourseTagService {

    @Override
    public Long getUserTagHoldNum(String tagId) {
        return baseMapper.selectCount(new LambdaQueryWrapper<UserCourseTag>()
            .eq(UserCourseTag::getTagId, tagId)
            .eq(UserCourseTag::getUserId, UserThreadContext.getUserId())
        );
    }

    @Override
    public Long getTagCollectNum(String tagId) {
        return baseMapper.selectCount(new LambdaQueryWrapper<UserCourseTag>()
            .eq(UserCourseTag::getTagId, tagId)
            .eq(UserCourseTag::getCreateType, 1)
            .eq(UserCourseTag::getUserId, UserThreadContext.getUserId())
        );
    }
}
