package com.wunding.learn.business.view.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.business.view.service.model.StatAnalysisInsideLecturerCategory;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Property;

/**
 * <p> 内部讲师统计-按类别表 Mapper 接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">cjn</a>
 * @since 2022-11-14
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")
})
public interface StatAnalysisInsideLecturerCategoryMapper extends BaseMapper<StatAnalysisInsideLecturerCategory> {

}
