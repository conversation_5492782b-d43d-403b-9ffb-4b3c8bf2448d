<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.business.view.service.mapper.StatAnalysisStudentUploadCoursewareMapper">

        <!-- 开启二级缓存 -->
        <!--
    <cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
    -->

        <!-- 使用缓存 -->
        <cache-ref namespace="com.wunding.learn.business.view.service.mapper.StatAnalysisStudentUploadCoursewareMapper"/>

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.wunding.learn.business.view.service.model.StatAnalysisStudentUploadCourseware">
            <!--@Table stat_analysis_student_upload_courseware-->
                    <id column="id" jdbcType="VARCHAR" property="id"/>
                    <result column="cw_name" jdbcType="VARCHAR"
                            property="cwName"/>
                    <result column="create_time" jdbcType="TIMESTAMP"
                            property="createTime"/>
                    <result column="active_time" jdbcType="TIMESTAMP"
                            property="activeTime"/>
                    <result column="cw_is_available" jdbcType="TINYINT"
                            property="cwIsAvailable"/>
                    <result column="cw_is_del" jdbcType="TINYINT"
                            property="cwIsDel"/>
                    <result column="user_id" jdbcType="VARCHAR"
                            property="userId"/>
                    <result column="user_is_available" jdbcType="TINYINT"
                            property="userIsAvailable"/>
                    <result column="user_is_del" jdbcType="TINYINT"
                            property="userIsDel"/>
                    <result column="login_name" jdbcType="VARCHAR"
                            property="loginName"/>
                    <result column="full_name" jdbcType="VARCHAR"
                            property="fullName"/>
                    <result column="org_id" jdbcType="VARCHAR"
                            property="orgId"/>
                    <result column="org_name" jdbcType="VARCHAR"
                            property="orgName"/>
                    <result column="level_path" jdbcType="VARCHAR"
                            property="levelPath"/>
                    <result column="level_path_name" jdbcType="VARCHAR"
                            property="levelPathName"/>
                    <result column="learn_count" jdbcType="BIGINT"
                            property="learnCount"/>
                    <result column="learned_count" jdbcType="BIGINT"
                            property="learnedCount"/>
                    <result column="comment_count" jdbcType="BIGINT"
                            property="commentCount"/>
                    <result column="overall_score" jdbcType="DECIMAL"
                            property="overallScore"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, cw_name, create_time, active_time, cw_is_available, cw_is_del, user_id, user_is_available, user_is_del, login_name, full_name, org_id, org_name, level_path, level_path_name, learn_count, learned_count, comment_count, overall_score
        </sql>

</mapper>
