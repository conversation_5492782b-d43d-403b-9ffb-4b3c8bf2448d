package com.wunding.learn.business.view.service.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.page.PageMethod;
import com.google.common.base.Splitter;
import com.wunding.learn.business.view.service.client.dto.DashboardDTO;
import com.wunding.learn.business.view.service.client.dto.DashboardDTO.CurrentDataDTO;
import com.wunding.learn.business.view.service.client.dto.LecturerDTO;
import com.wunding.learn.business.view.service.model.StatCourseCurrentData;
import com.wunding.learn.business.view.service.model.StatCourseDataStat;
import com.wunding.learn.business.view.service.model.StatCourseMonthData;
import com.wunding.learn.business.view.service.model.StatDayData;
import com.wunding.learn.business.view.service.model.StatExamMonthData;
import com.wunding.learn.business.view.service.model.StatMonthData;
import com.wunding.learn.business.view.service.model.StatProjectMonthData;
import com.wunding.learn.business.view.service.model.StatSpecialMonthData;
import com.wunding.learn.business.view.service.service.DashboardCommonService;
import com.wunding.learn.business.view.service.service.DashboardService;
import com.wunding.learn.business.view.service.service.IStatCourseCurrentDataService;
import com.wunding.learn.business.view.service.service.IStatCourseDataStatService;
import com.wunding.learn.business.view.service.service.IStatCourseMonthDataService;
import com.wunding.learn.business.view.service.service.IStatDayDataService;
import com.wunding.learn.business.view.service.service.IStatExamMonthDataService;
import com.wunding.learn.business.view.service.service.IStatMonthDataService;
import com.wunding.learn.business.view.service.service.IStatProjectMonthDataService;
import com.wunding.learn.business.view.service.service.IStatSpecialMonthDataService;
import com.wunding.learn.business.view.service.service.BusinessViewProjectService;
import com.wunding.learn.user.api.dto.OrgDTO;
import com.wunding.learn.user.api.dto.UserDTO;
import com.wunding.learn.user.api.service.UserFeign;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 看板
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class DashboardServiceImpl implements DashboardService {

    private final UserFeign userFeign;
    private final BusinessViewProjectService businessViewProjectService;
    private final IStatDayDataService statDayDataService;
    private final IStatCourseCurrentDataService statCourseCurrentDataService;
    private final IStatMonthDataService monthDataService;
    private final IStatExamMonthDataService statExamMonthDataService;
    private final IStatCourseMonthDataService statCourseMonthDataService;
    private final IStatProjectMonthDataService statProjectMonthDataService;
    private final IStatSpecialMonthDataService statSpecialMonthDataService;
    private final IStatCourseDataStatService statCourseDataStatService;
    private final DashboardCommonService dashboardCommonService;

    @Override
    public DashboardDTO dashboard(String orgId) {

        OrgDTO sysOrg = dashboardCommonService.checkManageArea(orgId);
        orgId = sysOrg.getId();

        LocalDate today = LocalDate.now();
        LocalDate yesterday = today.plusDays(-1);
        int yesterdayInt = Integer.parseInt(yesterday.format(DateTimeFormatter.BASIC_ISO_DATE));
        int yesterdayMonth = yesterday.getYear() * 100 + yesterday.getMonthValue();

        DashboardDTO dashboardDTO = new DashboardDTO();
        dashboardDTO.setOrgId(orgId);
        dashboardDTO.setDataDate(today.format(DateTimeFormatter.ISO_LOCAL_DATE).concat(" 00:00:00"));
        // 查询昨日登录用户数和学习人数、学习总时长 、 人均学习时长
        StatDayData dayData = statDayDataService.getOne(
            Wrappers.lambdaQuery(StatDayData.class).eq(StatDayData::getOrgId, orgId)
                .eq(StatDayData::getHandleDate, yesterdayInt));
        if (null != dayData) {
            dashboardDTO.setVisitUserCount(dayData.getVisitUserCount());
            dashboardDTO.setLearnUserCount(dayData.getLearnUserCount());
        }
        StatCourseCurrentData courseCurrentData = statCourseCurrentDataService
            .getOne(Wrappers.lambdaQuery(StatCourseCurrentData.class).eq(StatCourseCurrentData::getId, orgId));
        if (null != courseCurrentData) {
            // 四舍五入
            double learnTimeSum = BigDecimal.valueOf((courseCurrentData.getLearnTimeCount() / 36.0) / 100)
                .setScale(2, RoundingMode.UP)
                .doubleValue();
            dashboardDTO.setLearnTimeSum(learnTimeSum);
            double userAvgLearnTime = BigDecimal.valueOf((courseCurrentData.getAverageLearnTime() / 36.0) / 100)
                .setScale(2, RoundingMode.UP)
                .doubleValue();
            dashboardDTO.setUserAvgLearnTime(userAvgLearnTime);
        }

        // 获取部门的一级子部门
        Map<String, OrgDTO> childMap = dashboardCommonService.getOrgChildMap(sysOrg);
        // 组织学习概览
        dashboardDTO.setOrganLearnList(getCurrentData(childMap));

        // 子部门考试通过率排行，取前10，fix增加月份过滤，避免机构重复
        dashboardDTO.setExamPassRatioRanking(examPassRatioRanking(childMap, yesterdayMonth));

        // 课程、学习项目、专题、考试数据和每月记录数据汇总
        List<Integer> months = dashboardCommonService.lastYearMonth();
        dashboardDTO.setCourseData(dashboardMonthCourse(orgId, months, yesterdayMonth));
        dashboardDTO.setExamData(dashboardMonthExam(orgId, months, yesterdayMonth));
        dashboardDTO.setProjectData(dashboardMonthProject(orgId, months, yesterdayMonth));
        dashboardDTO.setSpecialData(dashboardMonthSpecial(orgId, months, yesterdayMonth));

        // 查询管辖范围下课程排行榜 // 直接查询
        dashboardDTO.setCourseLearnRanking(hotCourseRanking(sysOrg));

        // 查询所有讲师评分排行，直接查询
        dashboardDTO.setLecturerRanking(lecturerRanking());
        return dashboardDTO;
    }

    /**
     * 主看板专题月数据
     *
     * @param orgId  部门id
     * @param months 月列表
     * @return 专题月数据
     */
    private DashboardDTO.SpecialData dashboardMonthSpecial(String orgId, List<Integer> months, int yesterdayMonth) {
        // 构造返回数据的初始化
        DashboardDTO.SpecialData data = new DashboardDTO.SpecialData();
        List<DashboardDTO.SpecialData.SpecialDataItem> items = new ArrayList<>(12);
        for (Integer month : months) {
            DashboardDTO.SpecialData.SpecialDataItem item = new DashboardDTO.SpecialData.SpecialDataItem();
            item.setMonth(month);
            items.add(item);
        }
        data.setItems(items);

        // 数据库查询最近一年的数据
        List<StatSpecialMonthData> list = statSpecialMonthDataService.list(
            Wrappers.lambdaQuery(StatSpecialMonthData.class).ge(StatSpecialMonthData::getHandleDate, months.get(0))
                .eq(StatSpecialMonthData::getOrgId, orgId).orderByAsc(StatSpecialMonthData::getHandleDate));
        // 如果数据库没有数据就直接返回
        if (list.isEmpty()) {
            return data;
        }
        // 当前数据
        list.forEach(o -> {
            // 最近一年12个月的数据
            for (DashboardDTO.SpecialData.SpecialDataItem item : items) {
                if (item.getMonth().equals(o.getHandleDate())) {
                    item.setSpecialCount(o.getSpecialCount());
                    item.setSpecialUserCount(o.getUserCount());
                    break;
                }
            }
            // 当前数据
            if (o.getHandleDate() == yesterdayMonth) {
                data.setSpecialCount(o.getSpecialCount());
                data.setSpecialUserCount(o.getUserCount());
            }
        });
        return data;
    }

    /**
     * 主看板学习项目月数据
     *
     * @param orgId  部门id
     * @param months 12个月
     * @return 月数据
     */
    private DashboardDTO.ProjectData dashboardMonthProject(String orgId, List<Integer> months, int yesterdayMonth) {
        // 构造返回数据的初始化
        DashboardDTO.ProjectData data = new DashboardDTO.ProjectData();
        List<DashboardDTO.ProjectData.ProjectDataItem> items = new ArrayList<>(12);
        for (Integer month : months) {
            DashboardDTO.ProjectData.ProjectDataItem item = new DashboardDTO.ProjectData.ProjectDataItem();
            item.setMonth(month);
            items.add(item);
        }
        data.setItems(items);

        // 数据库查询最近一年的数据，其实是查13个月的数据，因为还要显示最新的数据
        List<StatProjectMonthData> list = statProjectMonthDataService.list(
            Wrappers.lambdaQuery(StatProjectMonthData.class).ge(StatProjectMonthData::getHandleDate, months.get(0))
                .eq(StatProjectMonthData::getOrgId, orgId).orderByAsc(StatProjectMonthData::getHandleDate));
        // 如果数据库没有数据就直接返回
        if (list.isEmpty()) {
            return data;
        }
        // 当前数据
        list.forEach(o -> {
            // 最近一年12个月的数据
            for (DashboardDTO.ProjectData.ProjectDataItem item : items) {
                if (item.getMonth().equals(o.getHandleDate())) {
                    item.setProjectCount(o.getProjectCount());
                    item.setProjectUserCount(o.getUserCount());
                    break;
                }
            }
            // 当前数据
            if (o.getHandleDate() == yesterdayMonth) {
                data.setProjectCount(o.getProjectCount());
                data.setProjectUserCount(o.getUserCount());
            }
        });
        return data;
    }

    /**
     * 人气课程排行榜，根据访问次数
     *
     * @param sysOrg 部门
     */
    private List<DashboardDTO.CourseRankingItem> hotCourseRanking(OrgDTO sysOrg) {
        Page<StatCourseDataStat> page = statCourseDataStatService.page(new Page<>(1, 10),
            Wrappers.lambdaQuery(StatCourseDataStat.class)
                .likeRight(StatCourseDataStat::getOrgLevelPath, sysOrg.getLevelPath())
                .orderByDesc(StatCourseDataStat::getLearnCount));
        List<DashboardDTO.CourseRankingItem> courseRankingItems = new ArrayList<>(10);
        DashboardDTO.CourseRankingItem courseRankingItem;
        for (StatCourseDataStat courseDataStat : page.getRecords()) {
            courseRankingItem = new DashboardDTO.CourseRankingItem();
            courseRankingItem.setOrgId(courseDataStat.getOrgId());
            courseRankingItem.setCourseName(courseDataStat.getName());
            courseRankingItem.setLearnUserCount(courseDataStat.getLearnCount().intValue());
            courseRankingItem.setOrganName(
                dashboardCommonService.convertOrgName(sysOrg, courseDataStat.getOrgId(),
                    courseDataStat.getOrgFullName()));
            courseRankingItems.add(courseRankingItem);
        }
        return courseRankingItems;
    }

    /**
     * 讲师评分排行，直接查询数据库 全部讲师，不区分当前查询的部门
     *
     * @return 讲师排行列表
     */
    private List<DashboardDTO.LecturerRankingItem> lecturerRanking() {
        List<DashboardDTO.LecturerRankingItem> lecturerRankingItems = new ArrayList<>(10);
        DashboardDTO.LecturerRankingItem lecturerRankingItem;
        PageMethod.startPage(1, 10);
        List<LecturerDTO> lecturers = businessViewProjectService.dashboardLecturerRanking();
        for (LecturerDTO lecturer : lecturers) {
            lecturerRankingItem = new DashboardDTO.LecturerRankingItem();
            lecturerRankingItem.setLecturerId(lecturer.getId());
            if (lecturer.getType() == 0) {
                UserDTO userById = userFeign.getUserById(lecturer.getUserId());
                if (null == userById) {
                    lecturerRankingItem.setOrgName("");
                } else {
                    Iterator<String> split =
                        Splitter.on("/").trimResults().omitEmptyStrings().split(userById.getLevelPathName()).iterator();
                    int index = 0;
                    while (index < 2 && split.hasNext()) {
                        lecturerRankingItem.setOrgName(split.next());
                        index++;
                    }
                }
            } else {
                lecturerRankingItem.setOrgName("外部");
            }
            lecturerRankingItem.setLecturerName(lecturer.getName());
            lecturerRankingItem.setScore(lecturer.getCommonScore());
            lecturerRankingItems.add(lecturerRankingItem);
        }
        return lecturerRankingItems;
    }

    /**
     * 组织考试通过率排行
     *
     * @param childMap       子部门
     * @param yesterdayMonth 查询月份
     */
    private List<DashboardDTO.ExamPassRatioRankingItem> examPassRatioRanking(Map<String, OrgDTO> childMap,
        int yesterdayMonth) {
        Set<String> currentDataOrg;
        Iterator<String> iterator;
        String tempOrgId;
        // 增加月份过滤，避免机构重复
        Page<StatExamMonthData> examPassRatioPage = statExamMonthDataService.page(new Page<>(1, 10),
            Wrappers.lambdaQuery(StatExamMonthData.class).eq(StatExamMonthData::getHandleDate, yesterdayMonth)
                .in(StatExamMonthData::getOrgId, childMap.keySet()).orderByDesc(StatExamMonthData::getExamPassRatio));
        List<DashboardDTO.ExamPassRatioRankingItem> examPassRatioRankingItems = new ArrayList<>(10);
        DashboardDTO.ExamPassRatioRankingItem examPassRatioRankingItem;
        currentDataOrg = new HashSet<>(childMap.keySet());
        for (StatExamMonthData data : examPassRatioPage.getRecords()) {
            examPassRatioRankingItem = new DashboardDTO.ExamPassRatioRankingItem();
            examPassRatioRankingItem.setOrgId(data.getOrgId());
            examPassRatioRankingItem.setOrgName(childMap.get(data.getOrgId()).getOrgName());
            examPassRatioRankingItem.setExamPassRatio(data.getExamPassRatio() / 100d);
            examPassRatioRankingItems.add(examPassRatioRankingItem);
            currentDataOrg.remove(data.getOrgId());
        }
        // 数据不够10条时，补够
        iterator = currentDataOrg.iterator();
        while (iterator.hasNext() && examPassRatioRankingItems.size() < 10) {
            tempOrgId = iterator.next();
            examPassRatioRankingItem = new DashboardDTO.ExamPassRatioRankingItem();
            examPassRatioRankingItem.setOrgId(tempOrgId);
            examPassRatioRankingItem.setOrgName(childMap.get(tempOrgId).getOrgName());
            examPassRatioRankingItem.setExamPassRatio(0.0);
            examPassRatioRankingItems.add(examPassRatioRankingItem);
        }
        return examPassRatioRankingItems;
    }

    /**
     * 主看板的组织学习概览
     *
     * @param childMap 子部门列表
     */
    private List<CurrentDataDTO> getCurrentData(Map<String, OrgDTO> childMap) {
        // 组织学习概览，学习当前查询部门的子级部门，按照学习人数由大到小排列。取前10
        Page<StatCourseCurrentData> page = statCourseCurrentDataService.page(new Page<>(1, 10),
            Wrappers.lambdaQuery(StatCourseCurrentData.class).in(StatCourseCurrentData::getId, childMap.keySet())
                .orderByDesc(StatCourseCurrentData::getLearnUserCount));
        List<CurrentDataDTO> currentData = new ArrayList<>(10);
        CurrentDataDTO currentDataVO;
        // 全部的部门ID，会把直接查询出来的过滤掉，去补充没有数据的部门
        Set<String> currentDataOrg = new HashSet<>(childMap.keySet());
        // 存放最终结果的部门id
        Set<String> resultOrg = new HashSet<>();
        // 有些组织没有数据，需要给他们初始化
        for (StatCourseCurrentData data : page.getRecords()) {
            currentDataVO = new CurrentDataDTO();
            currentDataVO.setOrgId(data.getId());
            currentDataVO.setOrgName(childMap.get(data.getId()).getOrgName());
            double userAvgLearnTime = BigDecimal.valueOf((data.getAverageLearnTime() / 36.0) / 100)
                .setScale(2, RoundingMode.UP)
                .doubleValue();
            currentDataVO.setUserAvgLearnTime(userAvgLearnTime);
            currentDataVO.setLearnUserCount(data.getLearnUserCount());
            currentDataVO.setLearnTimeSum((data.getLearnTimeCount() / 36.0) / 100);
            currentDataVO.setLoginUserCount(0);
            currentDataVO.setExamPassRatio(0.0);
            currentData.add(currentDataVO);
            currentDataOrg.remove(data.getId());
            resultOrg.add(data.getId());
        }
        // 数据不够10条时，补够
        Iterator<String> iterator = currentDataOrg.iterator();
        String tempOrgId;
        while (iterator.hasNext() && currentData.size() < 10) {
            tempOrgId = iterator.next();
            currentDataVO = new CurrentDataDTO();
            currentDataVO.setOrgId(tempOrgId);
            currentDataVO.setOrgName(childMap.get(tempOrgId).getOrgName());
            currentDataVO.setLearnTimeSum(0.0);
            currentDataVO.setUserAvgLearnTime(0.0);
            currentDataVO.setLearnUserCount(0);
            currentDataVO.setLoginUserCount(0);
            currentDataVO.setExamPassRatio(0.0);
            currentData.add(currentDataVO);
            resultOrg.add(tempOrgId);
        }

        // 填充登录信息，激活人数
        // 学习看板的激活人数不过滤月份
        List<StatMonthData> monthDataList = monthDataService.getMonthData(resultOrg);
        Map<String, Integer> loginUserMap = monthDataList.stream()
            .collect(Collectors.toMap(StatMonthData::getOrgId, StatMonthData::getLoginUserCount));
        // 结果数据填充总激活人数
        if (!monthDataList.isEmpty()) {
            // 双重for 循环换成map映射获取 空间换时间
            for (CurrentDataDTO dataVO : currentData) {
                Optional<Integer> loginUserOpt = Optional.ofNullable(loginUserMap.get(dataVO.getOrgId()));
                dataVO.setLoginUserCount(loginUserOpt.orElse(0));
            }
        }
        return currentData;
    }

    /**
     * 主看板考试月统计数据
     *
     * @param orgId 部门id
     * @return 考试月数据
     */
    private DashboardDTO.ExamData dashboardMonthExam(String orgId, List<Integer> months, int yesterdayMonth) {
        // 构造返回数据的初始化
        DashboardDTO.ExamData examData = new DashboardDTO.ExamData();
        List<DashboardDTO.ExamData.ExamDataItem> items = new ArrayList<>(12);
        for (Integer month : months) {
            DashboardDTO.ExamData.ExamDataItem item = new DashboardDTO.ExamData.ExamDataItem();
            item.setMonth(month);
            items.add(item);
        }
        examData.setItems(items);
        // 查询数据
        List<StatExamMonthData> list = statExamMonthDataService.list(
            Wrappers.lambdaQuery(StatExamMonthData.class).eq(StatExamMonthData::getOrgId, orgId)
                .ge(StatExamMonthData::getHandleDate, months.get(0)).orderByAsc(StatExamMonthData::getHandleDate));
        // 没有数据直接返回
        if (list.isEmpty()) {
            return examData;
        }
        // 当前数据
        list.forEach(o -> {
            // 最近一年12个月的数据
            for (DashboardDTO.ExamData.ExamDataItem item : items) {
                if (item.getMonth().equals(o.getHandleDate())) {
                    item.setExamCount(o.getExamCount());
                    item.setExamPassRatio(o.getExamPassRatio() / 100.0);
                    break;
                }
            }
            // 当前数据
            if (o.getHandleDate() == yesterdayMonth) {
                examData.setExamCount(o.getExamCount());
                // 补充完成率换算
                examData.setExamPassRatio(o.getExamPassRatio() / 100.0);
            }
        });
        return examData;
    }

    /**
     * 课程最近一年的月数据
     *
     * @param orgId 查询的部门id
     * @return {@link DashboardDTO.CourseData}
     */
    private DashboardDTO.CourseData dashboardMonthCourse(String orgId, List<Integer> months, int yesterdayMonth) {
        // 构造返回数据的初始化
        DashboardDTO.CourseData data = new DashboardDTO.CourseData();
        List<DashboardDTO.CourseData.CourseDataItem> items = new ArrayList<>(12);
        for (Integer month : months) {
            DashboardDTO.CourseData.CourseDataItem item = new DashboardDTO.CourseData.CourseDataItem();
            item.setMonth(month);
            items.add(item);
        }
        data.setItems(items);

        // 数据库查询最近一年的数据，其实是查13个月的数据，因为还要显示最新的数据
        List<StatCourseMonthData> list = statCourseMonthDataService.list(
            Wrappers.lambdaQuery(StatCourseMonthData.class).ge(StatCourseMonthData::getHandleDate, months.get(0))
                .eq(StatCourseMonthData::getOrgId, orgId).orderByAsc(StatCourseMonthData::getHandleDate));
        // 如果数据库没有数据就直接返回
        if (list.isEmpty()) {
            return data;
        }
        // 当前数据
        list.forEach(o -> {
            // 最近一年12个月的数据
            for (DashboardDTO.CourseData.CourseDataItem item : items) {
                if (item.getMonth().equals(o.getHandleDate())) {
                    item.setCourseCount(o.getCourseCount());
                    item.setLearnUserCount(o.getLearnCount());
                    break;
                }
            }
            // 当前数据
            if (o.getHandleDate() == yesterdayMonth) {
                data.setCourseCount(o.getCourseCount());
                data.setLearnUserCount(o.getLearnCount());
            }
        });
        return data;
    }

}
