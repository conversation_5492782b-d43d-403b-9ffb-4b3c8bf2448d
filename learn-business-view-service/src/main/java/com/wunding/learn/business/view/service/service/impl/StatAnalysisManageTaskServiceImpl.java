package com.wunding.learn.business.view.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.wunding.learn.business.view.service.admin.dto.StatAnalysisManageTaskDTO;
import com.wunding.learn.business.view.service.admin.dto.StatHotListDTO;
import com.wunding.learn.business.view.service.admin.dto.StatHotTaskDTO;
import com.wunding.learn.business.view.service.admin.query.HotRankQuery;
import com.wunding.learn.business.view.service.admin.query.ManageTaskQuery;
import com.wunding.learn.business.view.service.admin.query.StatHotQuery;
import com.wunding.learn.business.view.service.mapper.StatAnalysisManageTaskMapper;
import com.wunding.learn.business.view.service.model.StatAnalysisManageTask;
import com.wunding.learn.business.view.service.model.forum.PostDTO;
import com.wunding.learn.business.view.service.service.IStatAnalysisManageTaskService;
import com.wunding.learn.business.view.service.service.BusinessViewPostService;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.enums.other.ManageTaskTypeEnum;
import com.wunding.learn.common.enums.other.SystemConfigCodeEnum;
import com.wunding.learn.common.i18n.util.I18nUtil;
import com.wunding.learn.user.api.enums.UserOrgTypeEnum;
import com.wunding.learn.user.api.service.ParaFeign;
import com.wunding.learn.user.api.service.RouterFeign;
import com.wunding.learn.user.api.service.UserFeign;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <p> 学习任务中心发布数统计 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">ylq</a>
 * @since 2023-06-08
 */
@Slf4j
@Service("statAnalysisManageTaskService")
public class StatAnalysisManageTaskServiceImpl extends
    ServiceImpl<StatAnalysisManageTaskMapper, StatAnalysisManageTask> implements IStatAnalysisManageTaskService {

    @Resource
    private UserFeign userFeign;
    @Resource
    private ParaFeign paraFeign;
    @Resource
    private RouterFeign routerFeign;
    @Resource
    private BusinessViewPostService businessViewPostService;

    @Override
    public List<StatHotTaskDTO> hotRank(String taskType) {
        HotRankQuery hotRankQuery = new HotRankQuery();
        String userId = UserThreadContext.getUserId();
        hotRankQuery.setCurrentUserId(userId);
        List<String> levelPathList = userFeign.getLevelByParams(userId, UserOrgTypeEnum.USERMANAGEAREA.getValue());
        hotRankQuery.setLevelPathList(levelPathList);
        hotRankQuery.setType(0);
        hotRankQuery.setTaskType(taskType);
        List<StatHotTaskDTO> limit = baseMapper.selectHotRankList(hotRankQuery).stream().limit(8)
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(limit)) {
            return new ArrayList<>();
        }
        return limit;
    }

    @Override
    public PageInfo<StatAnalysisManageTaskDTO> manageTaskPageInfo(ManageTaskQuery query) {
        List<String> typeList = new ArrayList<>();
        List<String> routerIdList = routerFeign.getRouterIdList();
        for (String routerId : routerIdList) {
            ManageTaskTypeEnum e = ManageTaskTypeEnum.getByRouterId(routerId);
            if (e != null) {
                typeList.add(e.getTaskType());
            }
        }
        query.setTypeList(typeList);

        String userId = UserThreadContext.getUserId();
        query.setUserId(userId);
        List<String> levelPathList = userFeign.getLevelByParams(userId, UserOrgTypeEnum.USERMANAGEAREA.getValue());
        query.setLevelPathList(levelPathList);
        PageInfo<StatAnalysisManageTaskDTO> pageInfo = PageMethod.startPage(query.getPageNo(), query.getPageSize())
            .doSelectPageInfo(() -> baseMapper.getManageTaskList(query));
        if (CollectionUtils.isEmpty(pageInfo.getList())) {
            return new PageInfo<>();
        }
        for (StatAnalysisManageTaskDTO statAnalysisManageTaskDTO : pageInfo.getList()) {
            statAnalysisManageTaskDTO.setTaskTypeName(
                I18nUtil.getDefaultMessage(
                    Objects.requireNonNull(ManageTaskTypeEnum.getItem(statAnalysisManageTaskDTO.getTaskType()))
                        .getName()));
        }
        return pageInfo;
    }

    @Override
    public List<StatHotListDTO> hotListData(StatHotQuery statHotQuery) {
        String taskType = statHotQuery.getType();
        HotRankQuery hotRankQuery = new HotRankQuery();
        String userId = UserThreadContext.getUserId();
        List<String> levelPathList = userFeign.getLevelByParams(userId, UserOrgTypeEnum.USERMANAGEAREA.getValue());
        hotRankQuery.setLevelPathList(levelPathList);
        hotRankQuery.setType(1);
        hotRankQuery.setTaskType(taskType);
        hotRankQuery.setQueryType(statHotQuery.getQueryType());
        if (Objects.equals(taskType, "live")) {
            hotRankQuery.setIsPublish(GeneralJudgeEnum.CONFIRM.getValue());
        }

        if (Objects.equals(taskType, "post")) {
            String publishDays = paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_471.getCode());
            hotRankQuery.setPublishDays(Optional.ofNullable(publishDays).map(Integer::valueOf).orElse(null));
            String commentNum = paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_481.getCode());
            hotRankQuery.setCommentNum(Optional.ofNullable(commentNum).map(Integer::valueOf).orElse(0));
        }

        List<StatAnalysisManageTask> limit = baseMapper.selectHotList(hotRankQuery);
        if (CollectionUtils.isEmpty(limit)) {
            return new ArrayList<>();
        }

        Map<String, PostDTO> postMap = new HashMap<>();
        if (Objects.equals(taskType, "post")) {
            List<String> postIdList = limit.stream().map(StatAnalysisManageTask::getId).toList();
            postMap = businessViewPostService.getPostDetailByIdList(postIdList).stream()
                .collect(Collectors.toMap(PostDTO::getPostId,
                    Function.identity(), (key1, key2) -> key1));
        }
        ArrayList<StatHotListDTO> statHotTaskDto = new ArrayList<>();
        for (StatAnalysisManageTask statAnalysisManageTask : limit) {
            StatHotListDTO statHotTaskDTO = new StatHotListDTO();
            statHotTaskDTO.setTaskName(statAnalysisManageTask.getTaskName());
            if (Objects.equals(taskType, "post")) {
                Optional.ofNullable(postMap.get(statAnalysisManageTask.getId())).ifPresent(post -> {
                    statHotTaskDTO.setForumSectionName(post.getForumSectionName());
                    statHotTaskDTO.setForumViewNum(post.getViewNum());
                    statHotTaskDTO.setForumCreateByName(post.getForumCreateByName());
                    statHotTaskDTO.setForumCreateTime(post.getCreateTime());
                });
                statHotTaskDTO.setNum(statAnalysisManageTask.getReplyNum());
            } else {
                statHotTaskDTO.setNum(statAnalysisManageTask.getJoinUserNum());
            }

            statHotTaskDto.add(statHotTaskDTO);
        }
        return statHotTaskDto;
    }
}
