package com.wunding.learn.business.view.service.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.business.view.service.admin.dto.CoursewareCommentDTO;
import com.wunding.learn.business.view.service.aspect.DS;
import com.wunding.learn.business.view.service.mapper.comment.BusinessResourceCommentMapper;
import com.wunding.learn.business.view.service.model.comment.ResourceComment;
import com.wunding.learn.business.view.service.model.course.Course;
import com.wunding.learn.business.view.service.model.course.CourseDTO;
import com.wunding.learn.business.view.service.service.CommentService;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;

/**
 * 评论服务实现
 *
 * <AUTHOR>
 * @date 2022/10/17
 */
@Service
@DS("comment")
public class CommentServiceImpl extends ServiceImpl<BusinessResourceCommentMapper, ResourceComment> implements
        CommentService {

    @Override
    public Map<String, CourseDTO> getCourseStat() {
        List<CourseDTO> courseStatList = baseMapper.selectCourseStat();
        return courseStatList.stream()
                .collect(Collectors.toMap(Course::getId, item -> item, (key1, key2) -> key1));
    }

    @Override
    public List<CoursewareCommentDTO> getCoursewareComment() {
        return baseMapper.getCoursewareComment();
    }
}
