package com.wunding.learn.business.view.service.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.business.view.service.aspect.DS;
import com.wunding.learn.business.view.service.mapper.forum.BusinessPostMapper;
import com.wunding.learn.business.view.service.model.forum.Post;
import com.wunding.learn.business.view.service.model.forum.PostDTO;
import com.wunding.learn.business.view.service.service.BusinessViewPostService;
import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * 话题数据源
 *
 * <AUTHOR>
 * @date 2022/10/14
 */
@Service
@DS("forum")
public class BusinessViewPostServiceImpl extends ServiceImpl<BusinessPostMapper, Post> implements
    BusinessViewPostService {

    @Override
    public List<PostDTO> allPostCommentStat() {
        return baseMapper.allPostCommentStat();
    }

    @Override
    public List<PostDTO> getPostDetailByIdList(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return List.of();
        }
        return baseMapper.getPostDetailByIdList(idList);
    }
}
