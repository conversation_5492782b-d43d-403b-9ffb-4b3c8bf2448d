package com.wunding.learn.business.view.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.wunding.learn.business.view.service.admin.dto.ExamStatePartDTO;
import com.wunding.learn.business.view.service.admin.query.ExamStatQueryDetail;
import com.wunding.learn.business.view.service.mapper.StatExamStatePartMapper;
import com.wunding.learn.business.view.service.model.StatExamStatePart;
import com.wunding.learn.business.view.service.service.IStatExamStatePartService;
import com.wunding.learn.common.constant.other.DelEnum;
import com.wunding.learn.common.util.bean.SpringUtil;
import com.wunding.learn.file.api.component.ExportComponent;
import com.wunding.learn.file.api.constant.ExportBizType;
import com.wunding.learn.file.api.constant.ExportFileNameEnum;
import com.wunding.learn.file.api.dto.AbstractExportDataDTO;
import com.wunding.learn.file.api.dto.IExportDataDTO;
import com.wunding.learn.user.api.dto.OrgShowDTO;
import com.wunding.learn.user.api.service.OrgFeign;
import com.wunding.learn.user.api.service.UserFeign;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <p> 考试情况统计-参与人数表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">ydq</a>
 * @since 2022-11-01
 */
@Slf4j @Service("statExamStatePartService") public class StatExamStatePartServiceImpl
    extends ServiceImpl<StatExamStatePartMapper, StatExamStatePart> implements IStatExamStatePartService {

    private static final String DELETE_TEXT = "(deleted)";

    @Resource
    private OrgFeign orgFeign;

    @Resource
    private UserFeign userFeign;

    @Override public PageInfo<ExamStatePartDTO> list(ExamStatQueryDetail query) {
        //查询
        if (StringUtils.isNotBlank(query.getUserOrLoginNameLike())) {
            query.setUserIds(Arrays.asList(query.getUserOrLoginNameLike().split(",")));
            query.setUserOrLoginNameLike(null);
        }

        PageInfo<ExamStatePartDTO> selectPageInfo = PageMethod.startPage(query.getPageNo(), query.getPageSize())
            .doSelectPageInfo(() -> baseMapper.selectDistinctByPage(query));
        List<ExamStatePartDTO> list = selectPageInfo.getList();
        if (CollectionUtils.isEmpty(list)) {
            return selectPageInfo;
        }
        Set<String> userIdSet = list.stream().map(ExamStatePartDTO::getId)
            .collect(Collectors.toSet());
        Map<String, Integer> userIsDelStatusMap = userFeign.getUserIsDelStatus(userIdSet);
        Set<String> orgIds = list.stream().map(ExamStatePartDTO::getOrgId)
            .collect(Collectors.toSet());
        Map<String, OrgShowDTO> orgShowDTOMap = orgFeign.getOrgShowDTO(orgIds);
        list.forEach(dto -> {
            if (dto.getExamScore().compareTo(BigDecimal.ZERO) < 0) {
                dto.setExamScore(null);
            }
            Optional.ofNullable(orgShowDTOMap.get(dto.getOrgId())).ifPresent(orgShowDTO -> {
                dto.setOrgName(orgShowDTO.getOrgShortName());
                dto.setOrgPath(orgShowDTO.getLevelPathName());
            });
            if (DelEnum.DELETED.getValue() == userIsDelStatusMap.get(dto.getId())) {
                dto.setFullName(dto.getFullName() + DELETE_TEXT);
            }
        });
        return selectPageInfo;
    }

    @Resource private ExportComponent exportComponent;

    @Override @Async public void exportData(ExamStatQueryDetail query) {
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<IStatExamStatePartService, ExamStatePartDTO>(query) {

            @Override protected IStatExamStatePartService getBean() {
                return SpringUtil.getBean("statExamStatePartService", IStatExamStatePartService.class);
            }

            @Override protected PageInfo<ExamStatePartDTO> getPageInfo() {
                return getBean().list((ExamStatQueryDetail)queryDTO);
            }

            @Override public ExportBizType getType() {
                return ExportBizType.ExamStateStatPartAnalysis;
            }

            @Override public String getFileName() {
                return ExportFileNameEnum.ExamStateStatPartAnalysis.getType();
            }
        };

        exportComponent.exportRecord(exportDataDTO);
    }

}
