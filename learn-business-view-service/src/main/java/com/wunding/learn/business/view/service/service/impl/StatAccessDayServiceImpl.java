package com.wunding.learn.business.view.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.business.view.service.mapper.StatAccessDayMapper;
import com.wunding.learn.business.view.service.model.StatAccessDay;
import com.wunding.learn.business.view.service.service.IStatAccessDayService;
import java.util.Date;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p> 日统计表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">chh</a>
 * @since 2022-10-08
 */
@Slf4j
@Service("statAccessDayService")
public class StatAccessDayServiceImpl extends ServiceImpl<StatAccessDayMapper, StatAccessDay> implements
    IStatAccessDayService {

    @Override
    public Integer getAccessMonthNum(String orgId, Date lastMonth) {
        return Optional.ofNullable(baseMapper.getAccessMonthNum(orgId, lastMonth)).orElse(0);
    }
}
