package com.wunding.learn.business.view.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.business.view.service.aspect.DS;
import com.wunding.learn.business.view.service.mapper.lecturer.BusinessLecturerMapper;
import com.wunding.learn.business.view.service.model.lecturer.Lecturer;
import com.wunding.learn.business.view.service.service.BusinessViewLecturerService;
import com.wunding.learn.common.dto.SysTagResourceRelation;
import com.wunding.learn.common.dto.SysTemTagDTO;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * <p> 讲师表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">李毅慧</a>
 * @since 2022-05-11
 */
@Service("businessViewLecturerService")
@DS("lecturer")
public class BusinessViewLecturerServiceImpl extends ServiceImpl<BusinessLecturerMapper, Lecturer> implements
    BusinessViewLecturerService {

    @Override
    public String getLevelName(String levelId) {
        return baseMapper.getLevelName(levelId);
    }

    @Override
    public Integer getExternalLecturer(Integer queryType) {
        return baseMapper.getExternalLecturer(queryType);
    }

    @Override
    public Long getRelateKnowledgeNum(String tagId) {
        return baseMapper.getRelateKnowledgeNum(tagId);
    }

    @Override
    public List<SysTemTagDTO> getLecturerTagList() {
        return baseMapper.getLecturerTagList();
    }

    @Override
    public List<SysTagResourceRelation> getLecturerTagRelation() {
        return baseMapper.getLecturerTagRelation();
    }
}


