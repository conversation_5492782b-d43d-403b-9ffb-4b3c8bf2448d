package com.wunding.learn.business.view.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.business.view.service.mapper.StatAnalysisResourceExampleMapper;
import com.wunding.learn.business.view.service.model.StatAnalysisResourceExample;
import com.wunding.learn.business.view.service.service.IStatAnalysisResourceExampleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>  服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">axr</a>
 * @since 2023-06-06
 */
@Slf4j
@Service("statAnalysisResourceExampleService")
public class StatAnalysisResourceExampleServiceImpl extends
    ServiceImpl<StatAnalysisResourceExampleMapper, StatAnalysisResourceExample> implements
    IStatAnalysisResourceExampleService {

}
