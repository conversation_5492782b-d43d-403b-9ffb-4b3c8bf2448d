package com.wunding.learn.business.view.service.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.business.view.service.aspect.DS;
import com.wunding.learn.business.view.service.mapper.exam.BusinessExamMapper;
import com.wunding.learn.business.view.service.model.StatAnalysisResource;
import com.wunding.learn.business.view.service.model.exam.Exam;
import com.wunding.learn.business.view.service.model.exam.ExamDTO;
import com.wunding.learn.business.view.service.service.BusinessViewExamService;
import com.wunding.learn.common.enums.bussinessview.StatCardTypeEnum;
import com.wunding.learn.user.api.dto.UserDTO;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import org.springframework.stereotype.Service;

/**
 * 考试服务实现
 *
 * <AUTHOR>
 * @date 2022/10/17
 */
@Service("businessViewExamService")
@DS("exam")
public class BusinessViewExamServiceImpl extends ServiceImpl<BusinessExamMapper, Exam> implements
    BusinessViewExamService {

    @Override
    public List<ExamDTO> findAllExam() {
        return baseMapper.findAllExam();
    }

    @Override
    public Long getQuestionTotal() {
        return baseMapper.getQuestionTotal();
    }

    @Override
    public String getQuestionName(String questionId) {
        return baseMapper.getQuestionName(questionId);
    }

    @Override
    public Integer getInProgressExamCount(String userId, Set<String> managerAreaOrgIds) {
        return baseMapper.getInProgressExamCount(userId, managerAreaOrgIds);
    }

    @Override
    public List<StatAnalysisResource> statisticsLib(List<UserDTO> allAdministrator) {
        List<StatAnalysisResource> resourceList = new ArrayList<>();
        for (UserDTO admin : allAdministrator) {
            StatAnalysisResource resource = new StatAnalysisResource();
            resource.setUserId(admin.getId());
            // 考题库题数
            resource.setType(StatCardTypeEnum.EXAM_LIB_QUESTION.getValue());
            resource.setTotal(baseMapper.getLibQuestionCount(admin.getId(), admin.getCurrentOrgIdList(), 0));
            resource.setYesterdayIncrease(
                baseMapper.getLibQuestionCount(admin.getId(), admin.getCurrentOrgIdList(), 1));
            resource.setMonthIncrease(baseMapper.getLibQuestionCount(admin.getId(), admin.getCurrentOrgIdList(), 2));
            resource.setLastMonthIncrease(
                baseMapper.getLibQuestionCount(admin.getId(), admin.getCurrentOrgIdList(), 3));
            resourceList.add(resource);
            // 试卷库数
            resource = new StatAnalysisResource();
            resource.setUserId(admin.getId());
            resource.setType(StatCardTypeEnum.EXAM_LIB.getValue());
            resource.setTotal(baseMapper.getExamLibCount(admin.getId(), admin.getCurrentOrgIdList(), 0));
            resource.setYesterdayIncrease(baseMapper.getExamLibCount(admin.getId(), admin.getCurrentOrgIdList(), 1));
            resource.setMonthIncrease(baseMapper.getExamLibCount(admin.getId(), admin.getCurrentOrgIdList(), 2));
            resource.setLastMonthIncrease(baseMapper.getExamLibCount(admin.getId(), admin.getCurrentOrgIdList(), 3));
            resourceList.add(resource);
        }
        return resourceList;
    }

    @Override
    public Integer getInProgressExamCompetitionCount(String currentUserId, Set<String> managerAreaOrgIds) {
        return baseMapper.getInProgressExamCompetitionCount(currentUserId, managerAreaOrgIds);
    }
}
