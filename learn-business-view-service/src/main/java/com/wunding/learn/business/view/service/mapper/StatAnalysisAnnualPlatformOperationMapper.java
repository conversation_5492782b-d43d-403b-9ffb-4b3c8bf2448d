package com.wunding.learn.business.view.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.business.view.service.model.StatAnalysisAnnualPlatformOperation;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Property;

/**
 * <p> 年度平台运营数据表 Mapper 接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">Herilo</a>
 * @since 2022-12-30
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")
})
public interface StatAnalysisAnnualPlatformOperationMapper extends BaseMapper<StatAnalysisAnnualPlatformOperation> {

}
