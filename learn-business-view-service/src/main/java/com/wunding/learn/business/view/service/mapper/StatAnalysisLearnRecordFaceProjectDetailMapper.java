package com.wunding.learn.business.view.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.business.view.service.admin.dto.LearnRecordFaceProjectDTO;
import com.wunding.learn.business.view.service.admin.query.LearnRecordBaseQuery;
import com.wunding.learn.business.view.service.model.StatAnalysisLearnRecordFaceProjectDetail;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import java.util.List;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;

@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")
})
public interface StatAnalysisLearnRecordFaceProjectDetailMapper extends BaseMapper<StatAnalysisLearnRecordFaceProjectDetail> {

    /**
     * 学员档案 项目详情
     *
     * @param learnRecordQuery 查询条件
     * @return {@link List}<{@link LearnRecordFaceProjectDTO}>
     */
    List<LearnRecordFaceProjectDTO> findListData(@Param("params") LearnRecordBaseQuery learnRecordQuery);
}
