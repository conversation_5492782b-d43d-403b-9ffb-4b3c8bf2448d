package com.wunding.learn.business.view.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.business.view.service.aspect.DS;
import com.wunding.learn.business.view.service.mapper.market.BusinessVoteMapper;
import com.wunding.learn.business.view.service.model.market.Vote;
import com.wunding.learn.business.view.service.service.BusinessViewVoteService;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p> 投票表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">cjn</a>
 * @since 2022-12-08
 */
@Slf4j
@DS("market")
@Service("businessViewVoteService")
public class BusinessViewVoteServiceImpl extends ServiceImpl<BusinessVoteMapper, Vote> implements
    BusinessViewVoteService {

    @Override
    public Integer getInProgressVote(String currentUserId, Set<String> manageSet) {
        return baseMapper.getInProgressVote(currentUserId, manageSet);
    }
}
