package com.wunding.learn.business.view.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.business.view.service.mapper.StatSearchKeyMapper;
import com.wunding.learn.business.view.service.model.StatSearchKey;
import com.wunding.learn.business.view.service.service.IStatSearchKeyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p> 搜索关键词统计 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">chh</a>
    * @since 2022-10-13
 */
@Slf4j
@Service("statSearchKeyService")
public class StatSearchKeyServiceImpl extends ServiceImpl<StatSearchKeyMapper, StatSearchKey> implements IStatSearchKeyService {

}
