package com.wunding.learn.business.view.service.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.business.view.service.aspect.DS;
import com.wunding.learn.business.view.service.mapper.live.BusinessLiveMapper;
import com.wunding.learn.business.view.service.model.live.Live;
import com.wunding.learn.business.view.service.service.LiveService;
import org.springframework.stereotype.Service;

/**
 * 直播服务实现
 *
 * <AUTHOR>
 * @date 2022/10/18
 */
@Service
@DS("live")
public class LiveServiceImpl extends ServiceImpl<BusinessLiveMapper, Live> implements LiveService {

}
