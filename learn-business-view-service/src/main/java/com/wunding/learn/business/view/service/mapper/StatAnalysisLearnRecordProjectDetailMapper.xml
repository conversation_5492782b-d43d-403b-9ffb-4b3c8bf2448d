<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.business.view.service.mapper.StatAnalysisLearnRecordProjectDetailMapper">

        <!-- 开启二级缓存 -->
        <!--
    <cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
    -->

        <!-- 使用缓存 -->
        <cache-ref namespace="com.wunding.learn.business.view.service.mapper.StatAnalysisLearnRecordProjectDetailMapper"/>

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.wunding.learn.business.view.service.model.StatAnalysisLearnRecordProjectDetail">
            <!--@Table stat_analysis_learn_record_project_detail-->
                    <id column="id" jdbcType="BIGINT" property="id"/>
                    <result column="user_id" jdbcType="VARCHAR"
                            property="userId"/>
                    <result column="project_id" jdbcType="VARCHAR"
                            property="projectId"/>
                    <result column="project_no" jdbcType="VARCHAR"
                            property="projectNo"/>
                    <result column="project_name" jdbcType="VARCHAR"
                            property="projectName"/>
                    <result column="project_type" jdbcType="TINYINT"
                            property="projectType"/>
                    <result column="project_method" jdbcType="VARCHAR"
                            property="projectMethod"/>
                    <result column="address" jdbcType="VARCHAR"
                            property="address"/>
                    <result column="start_time" jdbcType="TIMESTAMP"
                            property="startTime"/>
                    <result column="end_time" jdbcType="TIMESTAMP"
                            property="endTime"/>
                    <result column="search_time" jdbcType="TIMESTAMP"
                            property="searchTime"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, user_id, project_id, project_no, project_name, project_type, project_method, address, start_time, end_time, search_time
        </sql>

        <select id="findListData"
          resultType="com.wunding.learn.business.view.service.admin.dto.LearnRecordProjectDTO"  useCache="false">
                select *
                from stat_analysis_learn_record_project_detail
                where user_id = #{params.userId}
                <if test="params.type != null and params.type == 0">
                        and search_time >= date_format(#{params.dayTime}, '%Y-%m-%d 00:00:00')
                        and date_format(#{params.dayTime}, '%Y-%m-%d 23:59:59') >= search_time
                </if>
                <if test="params.type != null and params.type == 1">
                        and search_time >= date_format(#{params.monthTime}, '%Y-%m-01 00:00:00')
                        and date_add(date_format(#{params.monthTime}, '%Y-%m-01 00:00:00'), interval 1 month) > search_time
                </if>
        </select>

</mapper>
