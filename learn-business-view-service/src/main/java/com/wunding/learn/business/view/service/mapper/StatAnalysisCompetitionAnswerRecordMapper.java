package com.wunding.learn.business.view.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.business.view.service.admin.dto.CompetitionAnswerRecordStatDTO;
import com.wunding.learn.business.view.service.admin.dto.ExamCompetitionRankingAdminDTO;
import com.wunding.learn.business.view.service.admin.query.CompetitionAnswerRecordStatQuery;
import com.wunding.learn.business.view.service.admin.query.CompetitionUserRankAdminQuery;
import com.wunding.learn.business.view.service.client.dto.ExamCompetitionRankingDTO;
import com.wunding.learn.business.view.service.client.query.ExamCompetitionRankingQuery;
import com.wunding.learn.business.view.service.model.StatAnalysisCompetitionAnswerRecord;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import java.util.List;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;

/**
 * 竞赛用户答题统计Mapper
 *
 * <AUTHOR>
 * @date 2023年09月25
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")
})
public interface StatAnalysisCompetitionAnswerRecordMapper extends BaseMapper<StatAnalysisCompetitionAnswerRecord> {

    /**
     * 查询答题统计列表信息
     */
    List<CompetitionAnswerRecordStatDTO> selectAnswerRecordStat(@Param("params") CompetitionAnswerRecordStatQuery query);

    /**
     * 查询pk赛排行列表信息，根据配置决定排名规则
     */
    List<ExamCompetitionRankingDTO> queryRankingsList(@Param("params") ExamCompetitionRankingQuery query, @Param("rankRule") String rankRule);

    /**
     * 查询我的排名
     * @param competitionId 竞赛Id
     * @param userId 用户Id
     * @param rankRule 排名规则字段
     */
    ExamCompetitionRankingDTO queryMyRank(@Param("competitionId") String competitionId, @Param("userId") String userId,
        @Param("rankRule") String rankRule);

    /**
     * 查询管理端答题排名
     * @param query
     * @param rankRule
     */
    List<ExamCompetitionRankingAdminDTO> queryAdminRankingsList(@Param("params") CompetitionUserRankAdminQuery query, @Param("rankRule") String rankRule);
}
