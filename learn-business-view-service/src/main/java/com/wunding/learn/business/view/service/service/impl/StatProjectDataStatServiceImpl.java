package com.wunding.learn.business.view.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.business.view.service.mapper.StatProjectDataStatMapper;
import com.wunding.learn.business.view.service.model.StatProjectDataStat;
import com.wunding.learn.business.view.service.service.IStatProjectDataStatService;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p> 学习项目数据统计表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">chh</a>
 * @since 2022-10-09
 */
@Slf4j
@Service("statProjectDataStatService")
public class StatProjectDataStatServiceImpl extends
    ServiceImpl<StatProjectDataStatMapper, StatProjectDataStat> implements IStatProjectDataStatService {

    @Override
    public List<StatProjectDataStat> getProjectDataList(String levelPath) {
        return baseMapper.selectProjectDataList(levelPath);
    }
}
