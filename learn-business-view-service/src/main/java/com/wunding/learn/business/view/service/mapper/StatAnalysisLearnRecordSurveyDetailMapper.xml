<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.business.view.service.mapper.StatAnalysisLearnRecordSurveyDetailMapper">

        <!-- 开启二级缓存 -->
        <!--
    <cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
    -->

        <!-- 使用缓存 -->
        <cache-ref namespace="com.wunding.learn.business.view.service.mapper.StatAnalysisLearnRecordSurveyDetailMapper"/>

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.wunding.learn.business.view.service.model.StatAnalysisLearnRecordSurveyDetail">
            <!--@Table stat_analysis_learn_record_survey_detail-->
                    <id column="id" jdbcType="BIGINT" property="id"/>
                    <result column="user_id" jdbcType="VARCHAR"
                            property="userId"/>
                    <result column="survey_id" jdbcType="VARCHAR"
                            property="surveyId"/>
                    <result column="survey_name" jdbcType="VARCHAR"
                            property="surveyName"/>
                    <result column="question_num" jdbcType="VARCHAR"
                            property="questionNum"/>
                    <result column="start_time" jdbcType="TIMESTAMP"
                            property="startTime"/>
                    <result column="end_time" jdbcType="TIMESTAMP"
                            property="endTime"/>
                    <result column="finish_time" jdbcType="TIMESTAMP"
                            property="finishTime"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, user_id, survey_id, survey_name, question_num, start_time, end_time, finish_time
        </sql>

        <select id="findListData"
          resultType="com.wunding.learn.business.view.service.admin.dto.LearnRecordSurveyDTO" useCache="false">
                select *
                from stat_analysis_learn_record_survey_detail
                where user_id = #{params.userId}
                <if test="params.type != null and params.type == 0">
                        and finish_time >= date_format(#{params.dayTime}, '%Y-%m-%d 00:00:00')
                        and date_format(#{params.dayTime}, '%Y-%m-%d 23:59:59') >= finish_time
                </if>
                <if test="params.type != null and params.type == 1">
                        and finish_time >= date_format(#{params.monthTime}, '%Y-%m-01 00:00:00')
                        and date_add(date_format(#{params.monthTime}, '%Y-%m-01 00:00:00'), interval 1 month) > finish_time
                </if>
        </select>

</mapper>
