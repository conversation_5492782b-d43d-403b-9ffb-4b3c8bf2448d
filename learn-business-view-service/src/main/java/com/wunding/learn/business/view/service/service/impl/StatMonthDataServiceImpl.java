package com.wunding.learn.business.view.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.business.view.service.mapper.StatMonthDataMapper;
import com.wunding.learn.business.view.service.model.StatMonthData;
import com.wunding.learn.business.view.service.service.IStatMonthDataService;
import java.util.List;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p> 累计月统计数据表，此表中的数据是累计数据，不是单月数据 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">chh</a>
 * @since 2022-10-09
 */
@Slf4j
@Service("statMonthDataService")
public class StatMonthDataServiceImpl extends ServiceImpl<StatMonthDataMapper, StatMonthData> implements
    IStatMonthDataService {

    @Override
    public List<StatMonthData> getMonthData(Set<String> resultOrg) {
        return baseMapper.getMonthData(resultOrg);
    }
}
