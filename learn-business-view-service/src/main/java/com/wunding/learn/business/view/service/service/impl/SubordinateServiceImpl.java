package com.wunding.learn.business.view.service.service.impl;

import com.github.pagehelper.PageInfo;
import com.wunding.learn.business.view.service.service.IStatLearnRecordDayService;
import com.wunding.learn.business.view.service.service.IStatLearnRecordMonthService;
import com.wunding.learn.business.view.service.service.ISubordinateService;
import com.wunding.learn.common.constant.other.CommonConstants;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.enums.bussinessview.SubordinateFileTypeEnum;
import com.wunding.learn.common.enums.language.LanguageEnum;
import com.wunding.learn.common.util.date.DateUtil;
import com.wunding.learn.user.api.dto.SubordinateFileDTO;
import com.wunding.learn.user.api.query.SubordinateFileQuery;
import com.wunding.learn.user.api.service.UserFeign;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * @Author: aixinrong
 * @Date: 2022/12/8 11:08
 */
@Slf4j
@Service("subordinateServiceImpl")
public class SubordinateServiceImpl implements ISubordinateService {

    @Resource
    private UserFeign userFeign;

    @Resource
    private IStatLearnRecordDayService statLearnRecordDayService;

    @Resource
    private IStatLearnRecordMonthService statLearnRecordMonthService;

    @Override
    public PageInfo<SubordinateFileDTO> subordinateFile(SubordinateFileQuery subordinateFileQuery) {
        subordinateFileQuery.setCurrentUserId(UserThreadContext.getUserId());
        PageInfo<SubordinateFileDTO> pageInfo = userFeign.subordinateFile(subordinateFileQuery);
        Set<String> userIds = pageInfo.getList().stream().map(SubordinateFileDTO::getId).filter(StringUtils::isNotBlank)
            .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(userIds)) {
            return PageInfo.emptyPageInfo();
        }
        Integer type = subordinateFileQuery.getType();
        List<SubordinateFileDTO> list = new ArrayList<>();
        String startTime = subordinateFileQuery.getStartTime();
        String endTime = subordinateFileQuery.getEndTime();
        if (SubordinateFileTypeEnum.DAY.getValue().equals(type)) {
            list = statLearnRecordDayService.getSubordinateFileByDay(userIds, startTime, endTime);
        } else if (SubordinateFileTypeEnum.MONTH.getValue().equals(type)) {
            list = statLearnRecordMonthService.getSubordinateFileByMonth(userIds, startTime, endTime);
        } else if (SubordinateFileTypeEnum.YEAR.getValue().equals(type)) {
            list = statLearnRecordMonthService.getSubordinateFileByYear(userIds);
        }

        Map<String, SubordinateFileDTO> userMap = list.stream()
            .collect(Collectors.toMap(SubordinateFileDTO::getId, dto -> dto));

        for (SubordinateFileDTO dto : pageInfo.getList()) {
            SubordinateFileDTO userInfo = userMap.get(dto.getId());
            Optional.ofNullable(userInfo).ifPresent(user -> {
                dto.setExamNumber(user.getExamNumber());
                dto.setCourseNumber(user.getCourseNumber());
                dto.setProjectNumber(user.getProjectNumber());
                dto.setLearnTime(user.getLearnTime());
            });
            dto.setLearningTime(DateUtil.convertTimeRules3(dto.getLearnTime(), subordinateFileQuery.getLang()));
            if (dto.getJoinDate() != null && dto.getJoinDate().before(new Date())) {
                dto.setWorkingTime(DateUtil.getFormatTimeStr(dto.getJoinDate(), subordinateFileQuery.getLang()));
            } else {
                String dayUnit = LanguageEnum.ENGLISH.getLang().equals(subordinateFileQuery.getLang())
                    ? CommonConstants.LANG.get(CommonConstants.CONVERT_TIME_DAY) : CommonConstants.CONVERT_TIME_DAY;
                dto.setWorkingTime("0".concat(dayUnit));
            }
        }
        return pageInfo;
    }

}
