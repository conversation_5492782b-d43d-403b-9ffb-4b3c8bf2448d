package com.wunding.learn.business.view.service.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.wunding.learn.business.view.service.admin.dto.ExamStatDTO;
import com.wunding.learn.business.view.service.admin.query.ExamStatQuery;
import com.wunding.learn.business.view.service.mapper.StatAnalysisExamStateMapper;
import com.wunding.learn.business.view.service.model.StatAnalysisExamState;
import com.wunding.learn.business.view.service.service.IStatExamStateService;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.util.bean.SpringUtil;
import com.wunding.learn.file.api.component.ExportComponent;
import com.wunding.learn.file.api.constant.ExportBizType;
import com.wunding.learn.file.api.constant.ExportFileNameEnum;
import com.wunding.learn.file.api.dto.AbstractExportDataDTO;
import com.wunding.learn.file.api.dto.IExportDataDTO;
import com.wunding.learn.user.api.service.OrgFeign;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.Map;
import java.util.Set;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <p> 考试情况统计表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">ydq</a>
 * @since 2022-11-01
 */
@Slf4j
@Service("statExamStateService")
public class StatExamStateServiceImpl extends ServiceImpl<StatAnalysisExamStateMapper, StatAnalysisExamState> implements
    IStatExamStateService {

    @Resource
    private OrgFeign orgFeign;

    @Override
    public PageInfo<ExamStatDTO> list(ExamStatQuery query) {
        String userId = UserThreadContext.getUserId();
        Set<String> userManageAreaOrgId = orgFeign.findUserManageAreaLevelPath(userId);
        // 构造查询条件
        Date startTime = query.getStartTime();
        Date endTime = null;
        if (startTime != null) {
            endTime = Date.from(query.getStartTime().toInstant().plus(1, ChronoUnit.DAYS));
        }
        LambdaQueryWrapper<StatAnalysisExamState> wrapper = new LambdaQueryWrapper<StatAnalysisExamState>()
            .and(!CollectionUtils.isEmpty(userManageAreaOrgId), q -> {
                for (String levelPath : userManageAreaOrgId) {
                    q.or().likeRight(StatAnalysisExamState::getLevelPath, levelPath);
                }
            })
            .like(StringUtils.isNotBlank(query.getExamName()), StatAnalysisExamState::getExamName, query.getExamName())
            .ge(null != query.getStartTime(), StatAnalysisExamState::getStartTime, startTime)
            .lt(null != query.getStartTime(), StatAnalysisExamState::getStartTime, endTime)
            .eq(StringUtils.isNotBlank(query.getOrgId()),StatAnalysisExamState::getOrgId,query.getOrgId())
            .orderByDesc(StatAnalysisExamState::getStartTime);
        //查询
        PageInfo<StatAnalysisExamState> selectPageInfo = PageMethod.startPage(query.getPageNo(), query.getPageSize())
            .doSelectPageInfo(() -> baseMapper.selectList(wrapper));
        selectPageInfo.getList().forEach(a -> {
            a.setPassRate(a.getPassRate() == null ? 0 : a.getPassRate() / 100);
            a.setFirstPassRate(a.getFirstPassRate() == null ? 0 : a.getFirstPassRate() / 100);
        });
        PageInfo<ExamStatDTO> page = new PageInfo<>();
        BeanUtils.copyProperties(selectPageInfo, page);
        return page;
    }

    @Resource
    private ExportComponent exportComponent;

    @Override
    @Async
    public void exportData(ExamStatQuery query) {
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<IStatExamStateService, ExamStatDTO>(query) {

            @Override
            protected IStatExamStateService getBean() {
                return SpringUtil.getBean("statExamStateService", IStatExamStateService.class);
            }

            @Override
            protected PageInfo<ExamStatDTO> getPageInfo() {
                return getBean().list((ExamStatQuery) queryDTO);
            }

            @Override
            protected void afterCreateBeanMap(Map<String, Object> map) {
                Object passRate = map.get("passRate");
                map.put("passRate", passRate.toString() + "%");

                Object firstPassRate = map.get("firstPassRate");
                map.put("firstPassRate", firstPassRate.toString() + "%");
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.ExamStateStatAnalysis;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.ExamStateStatAnalysis.getType();
            }
        };

        exportComponent.exportRecord(exportDataDTO);
    }

}
