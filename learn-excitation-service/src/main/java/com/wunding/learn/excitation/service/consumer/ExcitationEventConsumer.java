package com.wunding.learn.excitation.service.consumer;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.rabbitmq.client.Channel;
import com.wunding.learn.common.constant.mq.MqConst;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.enums.exam.IsTrainEnum;
import com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum;
import com.wunding.learn.common.enums.excitation.ExcitationEventEnum;
import com.wunding.learn.common.enums.excitation.ExcitationTypeEnum;
import com.wunding.learn.common.enums.other.ExcitationOperationEnum;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.enums.other.SystemConfigCodeEnum;
import com.wunding.learn.common.mq.dto.ExcitationMQEventDTO;
import com.wunding.learn.common.mq.event.excitation.ExcitationMQEvent;
import com.wunding.learn.common.mq.util.ConsumerAckUtil;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.course.api.dto.CourseInfoDTO;
import com.wunding.learn.course.api.service.CourseFeign;
import com.wunding.learn.course.api.service.CourseWareFeign;
import com.wunding.learn.excitation.service.bo.ExcitationEventBO;
import com.wunding.learn.excitation.service.enums.ExcitationEventTypeEnum;
import com.wunding.learn.excitation.service.mapper.ExcitationConfigGlobalMapper;
import com.wunding.learn.excitation.service.mapper.ExcitationConfigUserMapper;
import com.wunding.learn.excitation.service.mapper.ExcitationEventMapper;
import com.wunding.learn.excitation.service.mapper.ExcitationTradeRecordMapper;
import com.wunding.learn.excitation.service.mapper.UserCreditMapper;
import com.wunding.learn.excitation.service.mapper.UserExcitationRecordMapper;
import com.wunding.learn.excitation.service.mapper.UserExperienceMapper;
import com.wunding.learn.excitation.service.mapper.UserGoldCoinMapper;
import com.wunding.learn.excitation.service.mapper.UserIntegralMapper;
import com.wunding.learn.excitation.service.mapper.UserLearnTimeMapper;
import com.wunding.learn.excitation.service.model.ExcitationConfigGlobal;
import com.wunding.learn.excitation.service.model.ExcitationConfigUser;
import com.wunding.learn.excitation.service.model.ExcitationTradeRecord;
import com.wunding.learn.excitation.service.model.UserCredit;
import com.wunding.learn.excitation.service.model.UserExcitationRecord;
import com.wunding.learn.excitation.service.model.UserExperience;
import com.wunding.learn.excitation.service.model.UserGoldCoin;
import com.wunding.learn.excitation.service.model.UserIntegral;
import com.wunding.learn.excitation.service.model.UserLearnTime;
import com.wunding.learn.excitation.service.query.ExcitationEventBOQuery;
import com.wunding.learn.user.api.dto.UserDTO;
import com.wunding.learn.user.api.service.ParaFeign;
import com.wunding.learn.user.api.service.UserFeign;
import jakarta.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2022/11/1
 */
@Component
@Slf4j
public class ExcitationEventConsumer {

    /**
     * 消息队列
     */
    public static final String EXCITATION_EVENT_CONSUMER_QUEUE = "excitationEventConsumerQueue";

    private static final String LOCK_TITLE = "ExcitationEventConsumer_redisLock_";

    @Resource
    private ExcitationEventMapper excitationEventMapper;
    @Resource
    private ExcitationConfigUserMapper excitationConfigUserMapper;
    @Resource
    private ExcitationConfigGlobalMapper excitationConfigGlobalMapper;
    @Resource
    private UserExcitationRecordMapper userExcitationRecordMapper;
    @Resource
    private UserExperienceMapper userExperienceMapper;
    @Resource
    private ExcitationTradeRecordMapper excitationTradeRecordMapper;
    @Resource
    private UserCreditMapper userCreditMapper;
    @Resource
    private UserGoldCoinMapper userGoldCoinMapper;
    @Resource
    private UserIntegralMapper userIntegralMapper;
    @Resource
    private UserLearnTimeMapper userLearnTimeMapper;
    @Resource
    private CourseWareFeign courseWareFeign;
    @Resource
    private UserFeign userFeign;
    @Resource
    private ParaFeign paraFeign;
    @Resource
    private CourseFeign courseFeign;
    @Resource
    @SuppressWarnings("rawtypes")
    private RedisTemplate<String,Object> redisTemplate;

    /**
     * 如果是课件激励，则把激励目标由课程ID更换为课件ID,激励类型由课程激励更换为课件激励
     *
     * @param excitationMQEventDTO 激励事件对象
     * @param category             激励事件所属分类（此参数用于过滤）
     * @param configUsers          激励配置列表
     */
    private static void fixConfigUserData(ExcitationMQEventDTO excitationMQEventDTO, String category,
        List<ExcitationConfigUser> configUsers) {
        if (category.equals(ExcitationEventCategoryEnum.COURSE_WARE.getCode())) {
            configUsers.forEach(configUser -> configUser.setTargetId(excitationMQEventDTO.getTargetId()));
        }
    }

    @RabbitListener(
        // 设置一个并发，保证数据执行顺序
        bindings = @QueueBinding(
            value = @Queue(
                value = EXCITATION_EVENT_CONSUMER_QUEUE
            ),
            exchange = @Exchange(value = MqConst.EXCHANGE_TOPIC, type = ExchangeTypes.TOPIC),
            key = MqConst.USER_EXCITATION_ROUTING_KEY
        ),
        id = "excitationEventHandler"
    )
    public void excitationEventHandler(
        @Payload ExcitationMQEvent excitationMQEvent,
        @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag, Channel channel) throws IOException {
        UserThreadContext.setTenantId(excitationMQEvent.getTenantId());
        ExcitationMQEventDTO excitationMQEventDto = excitationMQEvent.getExcitationMQEventDTO();
        log.info("用户id:{},excitationEventHandler excitationMQEvent: {}", excitationMQEventDto.getUserId(),
            JsonUtil.objToJson(excitationMQEvent));
        log.info("excitationEventHandler excitationMQEvent: {}", JsonUtil.objToJson(excitationMQEvent));
        try {

            boolean lock = redisTemplateAcquire(excitationMQEventDto.getUserId(), 60);
            if (!lock) {
                log.info("用户:{},excitationEventHandler 获取锁失败，结束...", excitationMQEventDto.getUserId());
                ConsumerAckUtil.basicNack(excitationMQEvent, channel, deliveryTag, false,true);
                return;
            }

            String eventId = excitationMQEventDto.getEventId();
            buildBizAttr(excitationMQEventDto);

            UserDTO userDTO = userFeign.getUserById(excitationMQEventDto.getUserId());
            // 获取本事件详情
            ExcitationEventBO excitationEvent = excitationEventMapper.queryEventBO(new ExcitationEventBOQuery()
                .setEventId(eventId).setSystemType(userDTO.getSystemType()));
            if (Objects.isNull(excitationEvent)) {
                log.info("用户：[{}],事件:{}不存在,结束...", userDTO, eventId);
                ConsumerAckUtil.basicAck(excitationMQEvent, channel, deliveryTag, false);
                // 事件不存在
                return;
            }
            String category = excitationEvent.getCategory();

            // 获取配置规则
            List<ExcitationConfig> excitationConfigs = getExcitationConfigList(excitationMQEventDto, category);
            if (CollectionUtils.isEmpty(excitationConfigs)) {
                log.info("用户：[{}],无对应事件规则配置，结束...", userDTO);
                ConsumerAckUtil.basicAck(excitationMQEvent, channel, deliveryTag, false);
                return;
            }
            log.info("共计" + excitationConfigs.size() + "条规则配置");

            //根据系统参数设置,是否是关闭可兑换区分配置 SYSTEM_CONFIG_CODE_912
            Integer isClose = Integer
                .valueOf(paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_912.getCode()));

            for (ExcitationConfig config : excitationConfigs) {
                if (!enableExecuteExcitationConfig(excitationEvent, config, excitationMQEventDto)) {
                    // 不执行当前规则
                    continue;
                }
                //SYSTEM_CONFIG_CODE_912 为关闭可兑换区分，强制修改激励都为不可兑换
                if (Objects.equals(isClose, GeneralJudgeEnum.CONFIRM.getValue())) {
                    config.setIsExchange(GeneralJudgeEnum.NEGATIVE.getValue());
                }

                // 开始执行
                executeBody(config, excitationEvent, excitationMQEventDto);
            }
            ConsumerAckUtil.basicAck(excitationMQEvent, channel, deliveryTag, false);
        } catch (Exception e) {
            log.error("激励消费失败,event:" + JsonUtil.objToJson(excitationMQEvent) + ",error:", e);
            ConsumerAckUtil.basicAck(excitationMQEvent, channel, deliveryTag, true);
        }finally {
            redisTemplateRelease(excitationMQEventDto.getUserId());
            UserThreadContext.remove();
        }
    }

    /**
     * 使用redisTemplate实现分布式锁 , 注意不会续期，一次性锁，非必要不用此方法,没有线程看护，超时不续期
     * @param lockName key
     * @return 是否获取到锁
     */
    public boolean redisTemplateAcquire(String lockName,long waitTime) {
        int count = 0;
        String key = LOCK_TITLE + ":" + UserThreadContext.getTenantId() + ":" + lockName;
        String value = StringUtil.newId()+"_"+getHostnameAndIp() + "_"+Thread.currentThread().threadId();

        ValueOperations<String, Object> valueOperations = redisTemplate.opsForValue();
        Boolean setIfAbsent = valueOperations.setIfAbsent(key, value,waitTime+30, TimeUnit.SECONDS);
        try {
            Thread.sleep(1);
        } catch (InterruptedException e) {
            log.warn("======lock error======",e);
            Thread.currentThread().interrupt();
            return false;
        }
        // 重新读一次确保写成功的是当前线程写入的内容
        Object o1 = valueOperations.get(key);
        while (Boolean.FALSE.equals(setIfAbsent) || !Objects.equals(value, o1) ){
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                log.warn("======lock error======",e);
                Thread.currentThread().interrupt();
                return false;
            }
            count++;
            if(count/100 > waitTime){
                return false;
            }
            setIfAbsent = valueOperations.setIfAbsent(key, value,waitTime+30,TimeUnit.SECONDS);
            o1 = valueOperations.get(key);
        }

        return true;
    }

    /**
     * 和redisTemplateAcquire方法对应，必须放在finally里
     * @param lockName key
     */
    public void redisTemplateRelease(String lockName){
        String key = LOCK_TITLE + ":" + UserThreadContext.getTenantId() + ":" + lockName;
        redisTemplate.delete(key);
    }

    private String getHostnameAndIp(){
        InetAddress ia = null;
        try {
            ia = InetAddress.getLocalHost();
        } catch (UnknownHostException e) {
            log.error("getHostnameAndIp error",e);
            return "";
        }
        //获取计算机主机名
        String host = ia.getHostName();
        //获取计算机IP
        String ip= ia.getHostAddress();
        return host + "_" + ip;
    }

    /**
     * 执行体
     *
     * @param config 激励配置
     */
    public void executeBody(ExcitationConfig config, ExcitationEventBO excitationEvent,
        ExcitationMQEventDTO excitationMQEventDTO) {
        String userId = excitationMQEventDTO.getUserId();
        String targetId = excitationMQEventDTO.getTargetId();
        String targetName = excitationMQEventDTO.getTargetName();
        // 操作后的激励数据量
        BigDecimal currentNum;
        String eventId = config.getEventId();
        // 学完课件（按课件时长）
        if (ExcitationEventEnum.finishCourseWareByTime.name().equals(eventId)) {
            Integer playTime = courseWareFeign.getCourseWarePlayTime(targetId);
            log.info("========>targetId:{},playTime:{}", targetId, playTime);
            BigDecimal secondsToHours = convertSecondsToHours(playTime);
            log.info("========>targetId:{},secondsToHours:{}", targetId, secondsToHours);
            if (secondsToHours.compareTo(BigDecimal.ZERO) <= 0) {
                secondsToHours = new BigDecimal("0.1");
            }
            config.setExcitationNum(secondsToHours);
        }
        // 加入user_excitation_record 用户激励分值总记录表
        UserExcitationRecord userExcitationRecord = new UserExcitationRecord()
            .setId(StringUtil.newId())
            .setUserId(userId)
            .setExcitationCategory(excitationEvent.getCategory())
            .setExcitationRecordId(config.getId())
            .setExcitationTypeId(config.getTypeId())
            .setOperateNum(config.getExcitationNum())
            .setOperateType(ExcitationOperationEnum.INCREASE.getValue())
            .setRemark(buildMark(excitationEvent, config, targetName))
            // 针对全局性 激励，例如添加案例激励事件 无法从config中获取到targetId，设置目标id
            .setTargetId(Objects.toString(config.getTargetId(), targetId));

        // 所有的激励都可以获得同等经验
        if (!config.getTypeId().equals(ExcitationTypeEnum.LEARN_TIME.getCode())) {
            // 学时不添加用户经验值
            UserExperience userExperience = userExperienceMapper.selectById(userId);
            if (Objects.isNull(userExperience)) {
                UserExperience experienceSave = new UserExperience().setUserId(userId).setNum(config.getExcitationNum())
                    .setOriginNum(BigDecimal.ZERO).setRemainNum(config.getExcitationNum());
                insert(experienceSave, userId);
            } else {
                UserExperience experienceUpdate = userExperience
                    .setNum(userExperience.getNum().add(config.getExcitationNum()))
                    .setRemainNum(userExperience.getRemainNum().add(config.getExcitationNum()));
                userExperienceMapper.updateById(experienceUpdate);
            }
        }

        log.info("开始增加激励...");

        long currentTime = System.currentTimeMillis();
        log.info("用户:{},config:{},计算当前激励开始", userId);
        // 加入: 用户积分/学分/学时/金币
        switch (ExcitationTypeEnum.get(config.getTypeId())) {
            case INTEGRAL:
                // 进行积分计算,返回剩余积分
                currentNum = computeIntegral(config, userId);
                break;
            case GOLD_COIN:
                // 进行金币计算,返回剩余金币
                currentNum = computeGoldCoin(config, userId);
                break;
            case CREDIT:
                // 进行学分计算,返回学分
                currentNum = computeCredit(config, userId);
                break;
            case LEARN_TIME:
                // 进行学时计算,返回学时
                currentNum = computeLearnTime(config, userId);
                break;
            default:
                currentNum = new BigDecimal(-1);
        }
        log.info("用户:{},config:{},currentNum:{},计算当前激励结束,耗时:{}", userId, config, currentNum,
            (System.currentTimeMillis() - currentTime));

        long insertRecordTime = System.currentTimeMillis();
        log.info("用户:{},添加激励交易记录开始", userId);
        userExcitationRecord.setCurrentNum(currentNum);
        userExcitationRecordMapper.insert(userExcitationRecord);

        // 加入excitation_trade_record 激励交易记录表
        if (config.getExcitationNum().compareTo(BigDecimal.ZERO) <= 0 ||
            !ExcitationTypeEnum.isTradeExcitationType(config.getTypeId())) {
            return;
        }
        ExcitationTradeRecord excitationTradeRecord = new ExcitationTradeRecord().setId(StringUtil.newId())
            .setExcitationId(config.getTypeId())
            .setUserId(userId)
            .setCurrentNum(currentNum)
            .setEventId(config.getEventId())
            .setOperateNum(config.getExcitationNum())
            .setOperateType(ExcitationOperationEnum.INCREASE.getValue())
            .setTargetId(targetId)
            .setTargetName(targetName)
            .setTradeType(excitationEvent.getCategory())
            .setBizId(excitationMQEventDTO.getBizId())
            .setBizType(excitationMQEventDTO.getBizType())
            .setIsExchange(config.getIsExchange())
            .setSummary(buildMark(excitationEvent, config, targetName));
        excitationTradeRecordMapper.insert(excitationTradeRecord);
        log.info("用户:{},record:{},添加激励交易记录结束,耗时:{}", userId, excitationTradeRecord,
            (System.currentTimeMillis() - insertRecordTime));
    }

    private void insert(UserExperience experienceSave, String userId) {
        try {
            userExperienceMapper.insert(experienceSave);
        } catch (Exception e) {
            try {
                Thread.sleep(100);
            } catch (Exception ex) {
                log.error("发生异常", ex);
                Thread.currentThread().interrupt();
            }
            //可能出现脏读缓存没有刷新,先查一遍数据库再进行操作
            UserExperience dbData = userExperienceMapper.selectById(userId);
            if (Objects.isNull(dbData)) {
                userExperienceMapper.insert(experienceSave);
            }
        }
    }

    /**
     * 创建备注
     */
    private String buildMark(ExcitationEventBO excitationEvent, ExcitationConfig config, String targetName) {
        StringBuilder buffer = new StringBuilder();
        buffer.append(excitationEvent.getName());
        if (StringUtils.isNotBlank(targetName)) {
            buffer.append("(").append(targetName).append(")");
        }
        buffer.append(ExcitationOperationEnum.INCREASE.getSymbol())
            .append(config.getExcitationNum())
            .append(ExcitationTypeEnum.getNameByCode(config.typeId));
        return buffer.toString();
    }

    /**
     * 增加积分
     */
    public BigDecimal computeIntegral(ExcitationConfig config, String userId) {
        UserIntegral data = new UserIntegral();
        // 查找积分数据
        UserIntegral dbData = userIntegralMapper.selectById(userId);
        if (Objects.isNull(dbData)) {
            // 新增并返回
            data.setUserId(userId)
                .setOriginNum(BigDecimal.ZERO)
                .setNum(config.getExcitationNum())
                .setAvailableNum(config.getExcitationNum());
            if (Objects.equals(config.getIsExchange(), GeneralJudgeEnum.CONFIRM.getValue())) {
                data.setConvertibleNum(config.getExcitationNum());
            }
            userIntegralMapper.insert(data);
            return data.getAvailableNum();
        }
        // 修改积分数据
        data.setUserId(userId)
            .setNum(dbData.getNum().add(config.getExcitationNum()))
            .setAvailableNum(dbData.getAvailableNum().add(config.getExcitationNum()));
        if (Objects.equals(config.getIsExchange(), GeneralJudgeEnum.CONFIRM.getValue())) {
            data.setConvertibleNum(dbData.getConvertibleNum().add(config.getExcitationNum()));
        }
        userIntegralMapper.updateById(data);
        return data.getAvailableNum();
    }

    /**
     * 增加金币
     */
    public BigDecimal computeGoldCoin(ExcitationConfig config, String userId) {

        UserGoldCoin data = new UserGoldCoin();
        // 查找积分数据
        UserGoldCoin dbUserData = userGoldCoinMapper.selectById(userId);
        if (Objects.isNull(dbUserData)) {
            // 新增并返回
            data.setUserId(userId)
                .setOriginNum(BigDecimal.ZERO)
                .setNum(config.getExcitationNum())
                .setRemainNum(config.getExcitationNum());
            if (Objects.equals(config.getIsExchange(), GeneralJudgeEnum.CONFIRM.getValue())) {
                data.setConvertibleNum(config.getExcitationNum());
            }
            userGoldCoinMapper.insert(data);
            return data.getRemainNum();
        }
        // 修改积分数据
        data.setUserId(userId)
            .setNum(dbUserData.getNum().add(config.getExcitationNum()))
            .setRemainNum(dbUserData.getRemainNum().add(config.getExcitationNum()));
        if (Objects.equals(config.getIsExchange(), GeneralJudgeEnum.CONFIRM.getValue())) {
            data.setConvertibleNum(dbUserData.getConvertibleNum().add(config.getExcitationNum()));
        }
        userGoldCoinMapper.updateById(data);
        return data.getRemainNum();
    }

    /**
     * 增加学时
     */
    public BigDecimal computeCredit(ExcitationConfig config, String userId) {

        UserCredit data = new UserCredit();
        // 查找积分数据
        UserCredit dbData = userCreditMapper.selectById(userId);
        if (Objects.isNull(dbData)) {
            // 新增并返回
            data.setUserId(userId)
                .setOriginNum(BigDecimal.ZERO)
                .setNum(config.getExcitationNum())
                .setRemainNum(config.getExcitationNum());
            if (Objects.equals(config.getIsExchange(), GeneralJudgeEnum.CONFIRM.getValue())) {
                data.setConvertibleNum(config.getExcitationNum());
            }
            userCreditMapper.insert(data);
            return data.getRemainNum();
        }
        // 修改积分数据
        data.setUserId(userId)
            .setNum(dbData.getNum().add(config.getExcitationNum()))
            .setRemainNum(dbData.getRemainNum().add(config.getExcitationNum()));
        if (Objects.equals(config.getIsExchange(), GeneralJudgeEnum.CONFIRM.getValue())) {
            data.setConvertibleNum(dbData.getConvertibleNum().add(config.getExcitationNum()));
        }
        userCreditMapper.updateById(data);
        return data.getRemainNum();
    }

    /**
     * 增加学分
     */
    public BigDecimal computeLearnTime(ExcitationConfig config, String userId) {

        UserLearnTime data = new UserLearnTime();
        // 查找积分数据
        UserLearnTime dbData = userLearnTimeMapper.selectById(userId);
        if (Objects.isNull(dbData)) {
            // 新增并返回
            data.setUserId(userId)
                .setOriginNum(BigDecimal.ZERO)
                .setNum(config.getExcitationNum())
                .setAvailableNum(config.getExcitationNum());
            if (Objects.equals(config.getIsExchange(), GeneralJudgeEnum.CONFIRM.getValue())) {
                data.setConvertibleNum(config.getExcitationNum());
            }
            userLearnTimeMapper.insert(data);
            return data.getAvailableNum();
        }
        // 修改积分数据
        data.setUserId(userId)
            .setNum(dbData.getNum().add(config.getExcitationNum()))
            .setAvailableNum(dbData.getAvailableNum().add(config.getExcitationNum()));
        if (Objects.equals(config.getIsExchange(), GeneralJudgeEnum.CONFIRM.getValue())) {
            data.setConvertibleNum(dbData.getConvertibleNum().add(config.getExcitationNum()));
        }
        userLearnTimeMapper.updateById(data);

        return data.getAvailableNum();
    }

    /**
     * 获取激励事件配置
     */
    public List<ExcitationConfig> getExcitationConfigList(ExcitationMQEventDTO excitationMQEventDTO, String category) {
        String eventId = excitationMQEventDTO.getEventId();
        UserDTO userDTO = userFeign.getSimpleUserById(excitationMQEventDTO.getUserId());
        boolean enableUserConfig = ExcitationEventCategoryEnum.isEnableUserConfig(category);
        // 是否课件激励事件
        boolean isCourseWare = StringUtils
            .equals(excitationMQEventDTO.getCategory(), ExcitationEventCategoryEnum.COURSE_WARE.getCode());
        String courseId = null;
        if (isCourseWare) {
            courseId = courseWareFeign.getCourseId(excitationMQEventDTO.getTargetId());
        }
        if (enableUserConfig) {
            // 获取在课程中配置了课件的激励事件
            List<ExcitationConfigUser> excitationConfigUsers = new ArrayList<>();

            // 此段代码的目的是根据课件的课程去查激励事件，即本代码块处理的均为课件激励事件
            if (StringUtils.isNotBlank(courseId)) {
                List<ExcitationConfigUser> configUsers = excitationConfigUserMapper.selectList(
                    new LambdaQueryWrapper<ExcitationConfigUser>()
                        .eq(ExcitationConfigUser::getEventId, eventId)
                        .eq(ExcitationConfigUser::getTargetId, courseId)
                        .eq(ExcitationConfigUser::getExcitationCategory, ExcitationEventCategoryEnum.COURSE.getCode())
                        .eq(ExcitationConfigUser::getIsAvailable, GeneralJudgeEnum.CONFIRM.getValue())
                        .eq(ExcitationConfigUser::getSystemType, userDTO.getSystemType())
                );

                // 如果是课件激励，则把激励目标由课程ID更换为课件ID,激励类型由课程激励更换为课件激励
                fixConfigUserData(excitationMQEventDTO, category, configUsers);

                excitationConfigUsers.addAll(configUsers);
            }

            // 查找自定义配置(所有激励配置，可能包含课程、课件激励)
            List<ExcitationConfigUser> excitationConfigUsersAll = excitationConfigUserMapper.selectList(
                new LambdaQueryWrapper<ExcitationConfigUser>()
                    .eq(ExcitationConfigUser::getEventId, eventId)
                    .eq(ExcitationConfigUser::getTargetId, excitationMQEventDTO.getTargetId())
                    .eq(ExcitationConfigUser::getIsAvailable, GeneralJudgeEnum.CONFIRM.getValue())
                    .eq(ExcitationConfigUser::getSystemType, userDTO.getSystemType())
                    .orderByAsc(ExcitationConfigUser::getCreateTime)
            );

            // 代码优化：原来写法可能导致课程、课件的重复激励 ———— 具体看Git历史记录
            excitationConfigUsersAll.forEach(c -> {
                if (excitationConfigUsers.stream().noneMatch(ecu -> ecu.getId().equals(c.getId()))) {
                    excitationConfigUsers.add(c);
                }
            });

            return excitationConfigUsers.stream().map(excitationConfigUser -> {
                ExcitationConfig excitationConfig = new ExcitationConfig();
                BeanUtils.copyProperties(excitationConfigUser, excitationConfig);
                // 增加激励兑换业务支持，如果规则不支持，但是业务支持，则支持兑换
                log.info("规则的isExchange属性为：{{}}", excitationConfig.getIsExchange());
                if (Objects.equals(excitationMQEventDTO.getIsExchange(), GeneralJudgeEnum.CONFIRM.getValue())) {
                    excitationConfig.setIsExchange(GeneralJudgeEnum.CONFIRM.getValue());
                    log.info("规则的isExchange属性经业务增强后为：{{}}", excitationConfig.getIsExchange());
                }
                return excitationConfig;
            }).toList();
        }
        List<ExcitationConfigGlobal> excitationConfigGlobals = excitationConfigGlobalMapper.selectList(
            new LambdaQueryWrapper<ExcitationConfigGlobal>()
                .eq(ExcitationConfigGlobal::getEventId, eventId)
                .eq(ExcitationConfigGlobal::getIsAvailable, GeneralJudgeEnum.CONFIRM.getValue())
                .eq(ExcitationConfigGlobal::getSystemType, userDTO.getSystemType())
                .orderByAsc(ExcitationConfigGlobal::getCreateTime)
        );
        return excitationConfigGlobals.stream().map(excitationConfigGlobal -> {
            ExcitationConfig excitationConfig = new ExcitationConfig();
            BeanUtils.copyProperties(excitationConfigGlobal, excitationConfig);
            // 增加激励兑换业务支持，如果规则不支持，但是业务支持，则支持兑换
            log.info("规则的isExchange属性为：{{}}", excitationConfig.getIsExchange());
            if (Objects.equals(excitationConfigGlobal.getIsExchange(), GeneralJudgeEnum.NEGATIVE.getValue())
                && Objects.equals(excitationMQEventDTO.getIsExchange(), GeneralJudgeEnum.CONFIRM.getValue())) {
                excitationConfig.setIsExchange(GeneralJudgeEnum.CONFIRM.getValue());
                log.info("规则的isExchange属性经业务增强后为：{{}}", excitationConfig.getIsExchange());
            }
            return excitationConfig;
        }).toList();
    }

    /**
     * 校验是否可以执行
     */
    public boolean enableExecuteExcitationConfig(ExcitationEventBO excitationEvent, ExcitationConfig config,
        ExcitationMQEventDTO excitationMQEventDTO) throws ParseException {
        String userId = excitationMQEventDTO.getUserId();
        log.info("校验是否可以执行激励配置,用户:{},excitationEvent:{},config:{}...", userId, excitationEvent, config);

        // 是否达到每日上限
        if (!checkDailyBound(excitationEvent, config, userId)) {
            log.info("用户:{},excitationEvent:{},config:{},已达到每日上限，校验不通过...", userId, excitationEvent,
                config);
            return false;
        }
        // 是否达到每内容上限
        if (!checkSameEventBound(excitationEvent, config, userId)) {
            log.info("用户:{},excitationEvent:{},config:{},已达到每内容上限，校验不通过...", userId, excitationEvent,
                config);
            return false;
        }
        // 是否在阈值范围内
        if (!checkInValueZone(excitationEvent, config, excitationMQEventDTO)) {
            log.info("用户:{},excitationEvent:{},config:{},该计数事件不在阈值范围内，校验不通过...", userId,
                excitationEvent, config);
            return false;
        }

        // 是否是闯关类型的课程、考试、调研
        if (Objects.equals(excitationMQEventDTO.getIsTrain(), IsTrainEnum.PROMOTED_GAME.getValue())) {
            log.info("课程、考试、调研的isTrain类型不支持激励，校验不通过...");
            return false;
        }
        // 是否是闯关类型的课程
        if (Objects.equals(excitationMQEventDTO.getBizType(), ExcitationEventCategoryEnum.COURSE.getCode())) {
            String courseId = excitationMQEventDTO.getBizId();
            CourseInfoDTO course = courseFeign.getById(courseId);
            if (!Objects.isNull(course) && Objects.equals(course.getIsTrain(), IsTrainEnum.PROMOTED_GAME.getValue())) {
                log.info("课程的isTrain类型不支持激励，校验不通过...");
                return false;
            }
        }
        //校验课件所属课程是否可以激励
        if (isCourseWareExcitationEvent(excitationMQEventDTO.getEventId()) && !checkCanWareExcitation(
            excitationMQEventDTO.getBizId())) {
            log.info("课件所属课程的isTrain类型不支持激励，校验不通过...");
            return false;
        }

        log.info("校验通过...");
        return true;
    }

    /**
     * <p>  查询课件是否触发激励（闯关直接创建的课程的课件无法触发激励）
     *
     * <AUTHOR>
     * @since 2024/8/15
     */
    private boolean checkCanWareExcitation(String courseWareId) {
        String courseId = courseWareFeign.getCourseId(courseWareId);
        if (StringUtils.isBlank(courseId)) {
            //课件所属课程不存在也放行
            return true;
        }
        CourseInfoDTO course = courseFeign.getById(courseId);
        if (Objects.isNull(course)) {
            //课件所属课程不存在也放行
            return true;
        }
        return !Objects.equals(course.getIsTrain(), IsTrainEnum.PROMOTED_GAME.getValue());
    }

    private boolean isCourseWareExcitationEvent(String eventId) {
        return Objects.equals(eventId, ExcitationEventEnum.commentCourseWare.name())
            || Objects.equals(eventId, ExcitationEventEnum.replyCourseWareComment.name());
    }

    /**
     * 验证事件的值是否在阈值范围内
     */
    public boolean checkInValueZone(ExcitationEventBO excitationEvent, ExcitationConfig config,
        ExcitationMQEventDTO excitationMQEventDTO) {
        BigDecimal value = excitationMQEventDTO.getValue();
        // 两种情况 1.是计数类型，但是无value 2.非计数类型无value
        // 非计数类型直接跳过本校验
        if (!Objects.equals(excitationEvent.getType(), ExcitationEventTypeEnum.COUNT.getValue())) {
            return true;
        }

        // 此处完全是计数类型，那么要求必须有value，如果没有value，那么就直接不执行
        if (Objects.isNull(value)) {
            // 打印日志
            log.error("计数类型的事件未传值value...");
            return false;
        }

        // 判断value是否在允许范围内
        return value.compareTo(new BigDecimal(config.getMinBound())) >= 0
            && value.compareTo(new BigDecimal(config.getMaxBound())) < 0;
    }


    /**
     * 校验事件类型/其他类型的没内容获取上限(次数)
     */
    public boolean checkSameEventBound(ExcitationEventBO excitationEvent, ExcitationConfig config, String userId) {
        // 如果是计数，则直接不校验，成功，结束; 计数类型不校验每内容获取上限(次数)
        if (Objects.equals(excitationEvent.getType(), ExcitationEventTypeEnum.COUNT.getValue())) {
            return true;
        }

        Long count = userExcitationRecordMapper.selectCount(new LambdaQueryWrapper<UserExcitationRecord>()
            .eq(UserExcitationRecord::getUserId, userId)
            // ExcitationRecordId已经绑定了资源id和激励事件id(eventId)
            .eq(UserExcitationRecord::getExcitationRecordId, config.getId()));
        log.info("checkSameEventBound====userId={},config.id={},count={}", userId, config.getId(), count);
        // 如果获得次数小于设定次数，则可以继续获得积分，返回真
        return count.compareTo(config.getSameEventBound()) < 0;
    }

    /**
     * 检查每日上限
     *
     * @return true:没有达到上限  false:达到了上限
     */
    public boolean checkDailyBound(ExcitationEventBO excitationEvent, ExcitationConfig config, String userId)
        throws ParseException {
        Long bound = Optional.of(excitationEvent.getBound()).orElse(0L);
        Date date = new Date();
        // 如果未设置每日同类型事件获取上限,直接返回校验通过
        if (bound.compareTo(0L) <= 0) {
            return true;
        }

        Long count = excitationTradeRecordMapper.selectCount(new LambdaQueryWrapper<ExcitationTradeRecord>()
            .eq(ExcitationTradeRecord::getUserId, userId)
            .eq(ExcitationTradeRecord::getEventId, config.getEventId())
            .between(ExcitationTradeRecord::getCreateTime, new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(
                    DateFormatUtils.format(date, "yyyy-MM-dd 00:00:00")),
                new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(
                    DateFormatUtils.format(date, "yyyy-MM-dd 23:59:59")))
        );
        // 如果获得次数小于设定次数，则可以继续获得积分，返回真
        return count < bound;
    }

    /**
     * 增加业务属性支持
     */
    private void buildBizAttr(ExcitationMQEventDTO eventDTO) {
        if (Objects.isNull(eventDTO)) {
            return;
        }
        if (Objects.isNull(eventDTO.getBizId())) {
            eventDTO.setBizId(eventDTO.getTargetId());
        }
        if (Objects.isNull(eventDTO.getBizType())) {
            eventDTO.setBizId(eventDTO.getCategory());
        }
        if (Objects.isNull(eventDTO.getIsExchange())) {
            eventDTO.setIsExchange(GeneralJudgeEnum.NEGATIVE.getValue());
        }
    }

    /**
     * 秒钟转换成小时
     * @param seconds 秒
     * @return
     */
    public static BigDecimal convertSecondsToHours(int seconds) {
        // 将秒数转换为小时（3600 秒 = 1 小时）
        BigDecimal secondsBD = new BigDecimal(seconds); // 将秒数转为 BigDecimal
        BigDecimal divisor = new BigDecimal(3600);      // 每小时的秒数
        // 保留 1 位小数，四舍五入
        return secondsBD.divide(divisor, 1, RoundingMode.HALF_UP);
    }

    /**
     * 激励事件配置内部类
     */
    @Data
    @Accessors(chain = true)
    public class ExcitationConfig {

        /**
         * 配置ID
         */
        private String id;

        /**
         * 事件ID
         */
        private String eventId;

        /**
         * 激励类型ID
         */
        private String typeId;

        /**
         * 每日同类型事件获取上限(次数)
         */
        private Long sameEventBound;

        /**
         * 阈值下限
         */
        private Long minBound;

        /**
         * 阈值上限
         */
        private Long maxBound;

        /**
         * 激励数量
         */
        private BigDecimal excitationNum;

        /**
         * 是否允许兑换
         */
        private Integer isExchange;

        /**
         * 激励对象的id
         */
        private String targetId;
    }
}
