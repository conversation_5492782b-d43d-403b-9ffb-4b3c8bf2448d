package com.wunding.learn.excitation.service.service.impl;

import static com.wunding.learn.common.util.string.StringUtil.newId;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.wunding.learn.common.aop.log.annotation.Log;
import com.wunding.learn.common.aop.log.annotation.Log.Type;
import com.wunding.learn.common.constant.excitation.ExcitationErrorNoEnum;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.util.bean.SpringUtil;
import com.wunding.learn.common.util.string.TranslateUtil;
import com.wunding.learn.excitation.service.admin.dto.TitleDTO;
import com.wunding.learn.excitation.service.admin.query.TitleQuery;
import com.wunding.learn.excitation.service.dao.TitleDao;
import com.wunding.learn.excitation.service.mapper.TitleMapper;
import com.wunding.learn.excitation.service.model.Title;
import com.wunding.learn.excitation.service.service.ITitleService;
import com.wunding.learn.file.api.component.ExportComponent;
import com.wunding.learn.file.api.constant.ExportBizType;
import com.wunding.learn.file.api.constant.ExportFileNameEnum;
import com.wunding.learn.file.api.dto.AbstractExportDataDTO;
import com.wunding.learn.file.api.dto.IExportDataDTO;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * <p> 头衔表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">chh</a>
 * @since 2022-09-28
 */
@Slf4j
@Service("titleService")
public class TitleServiceImpl extends ServiceImpl<TitleMapper, Title> implements ITitleService {

    @Resource
    private ExportComponent exportComponent;
    @Resource(name = "titleDao")
    private TitleDao titleDao;

    @Override
    public PageInfo<TitleDTO> list(TitleQuery titleQuery) {
        return PageMethod.startPage(titleQuery.getPageNo(), titleQuery.getPageSize())
            .doSelectPageInfo(() -> baseMapper.listData(titleQuery));
    }

    @Override
    public List<TitleDTO> getAllTitle() {
        return baseMapper.listData(new TitleQuery());
    }

    @Override
    public void createOrUpdate(TitleDTO titleDTO) {
        checkTitle(titleDTO);
        if (StringUtils.isNotBlank(titleDTO.getId())) {
            update(titleDTO);
            return;
        }
        create(titleDTO);
    }

    @Override
    @Log(type = Log.Type.CREATE, targetId = "#titleDTO.id", targetName = "#titleDTO.name", targetType = Log.TargetType.TITLE)
    public void createLog(TitleDTO titleDTO) {
        // 此处是空方法
    }

    @Override
    @Log(type = Type.UPDATE, targetId = "#titleDTO.id", targetName = "#titleDTO.name", targetType = Log.TargetType.TITLE)
    public void updateLog(TitleDTO titleDTO) {
        // 此处是空方法
    }

    @Override
    public void delete(String ids) {
        List<String> titleIds = TranslateUtil.translateBySplit(ids, String.class);
        List<Title> titles = listByIds(titleIds);
        titles.forEach(t -> titleDao.delTitle(t));
    }

    @Override
    @Log(type = Type.DELETE, targetId = "#title.id", targetName = "#title.name", targetType = Log.TargetType.TITLE)
    public void delLog(Title title) {
        // 此处是空方法
    }

    private void update(TitleDTO titleDTO) {
        Title updateBean = new Title();
        BeanUtils.copyProperties(titleDTO, updateBean);
        titleDao.updateTitle(updateBean);
    }

    private void create(TitleDTO titleDTO) {
        Title newBean = new Title();
        titleDTO.setId(newId());
        BeanUtils.copyProperties(titleDTO, newBean);
        newBean.setIsAvailable(GeneralJudgeEnum.CONFIRM.getValue());
        newBean.setSortNo(GeneralJudgeEnum.CONFIRM.getValue());
        titleDao.saveTitle(newBean);
    }

    private void checkTitle(TitleDTO titleDTO) {
        if (titleDTO.getMinValue() > titleDTO.getMaxValue()) {
            throw new BusinessException(ExcitationErrorNoEnum.ERR_EXPERIENCE_VALUE_FAULT);
        }
        LambdaQueryWrapper<Title> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Title::getName, titleDTO.getName());
        queryWrapper.ne(StringUtils.isNotBlank(titleDTO.getId()), Title::getId, titleDTO.getId());
        if (count(queryWrapper) > 0) {
            throw new BusinessException(ExcitationErrorNoEnum.ERR_TITLE_NAME_EXIST);
        }
        queryWrapper.clear();
        queryWrapper.ne(StringUtils.isNotBlank(titleDTO.getId()), Title::getId, titleDTO.getId());
        List<Title> titleList = list(queryWrapper);
        for (Title title : titleList) {
            // 经验值区间不可以有交集
            if (Objects.equals(title.getMinValue(), titleDTO.getMinValue()) ||
                Objects.equals(title.getMaxValue(), titleDTO.getMaxValue()) ||
                title.getMinValue() > titleDTO.getMinValue() && title.getMinValue() <= titleDTO.getMaxValue() ||
                title.getMinValue() < titleDTO.getMinValue() && title.getMaxValue() >= titleDTO.getMinValue()) {
                throw new BusinessException(ExcitationErrorNoEnum.ERR_EXPERIENCE_VALUE_EXIST);
            }
        }
    }

    @Override
    @Async
    public void exportData(TitleQuery titleQuery) {
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<ITitleService, TitleDTO>(
            titleQuery) {

            @Override
            protected ITitleService getBean() {
                return SpringUtil.getBean("titleService", ITitleService.class);
            }

            @Override
            protected PageInfo<TitleDTO> getPageInfo() {
                return getBean().list((TitleQuery) queryDTO);
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.Title;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.Title.getType();
            }

            @Override
            protected void afterCreateBeanMap(Map<String, Object> map) {
                Object minValue = map.get("minValue");
                Object maxValue = map.get("maxValue");
                map.put("minToMaxValue", minValue + "~" + maxValue);
            }
        };

        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    public String getTitleByExperience(BigDecimal experience) {
        return baseMapper.getTitleByExperience(experience);
    }
}
