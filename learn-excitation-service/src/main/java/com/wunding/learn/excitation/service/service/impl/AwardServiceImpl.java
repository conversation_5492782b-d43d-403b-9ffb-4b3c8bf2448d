package com.wunding.learn.excitation.service.service.impl;

import static com.wunding.learn.common.util.string.StringUtil.newId;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.wunding.learn.common.constant.excitation.ExcitationErrorNoEnum;
import com.wunding.learn.common.constant.other.AvailableEnum;
import com.wunding.learn.common.constant.other.ErrorNoEnum;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.enums.excitation.ExcitationTypeEnum;
import com.wunding.learn.common.enums.excitation.OtherEventCategoryEnum;
import com.wunding.learn.common.enums.file.ImageTypeEnum;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.enums.other.SystemConfigCodeEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.util.bean.BeanListUtils;
import com.wunding.learn.common.util.bean.SpringUtil;
import com.wunding.learn.common.util.date.DateUtil;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.common.util.string.TranslateUtil;
import com.wunding.learn.common.viewlimit.service.IResourceViewLimitService;
import com.wunding.learn.excitation.service.admin.dto.AwardDTO;
import com.wunding.learn.excitation.service.admin.dto.AwardIntegrateSaveDTO;
import com.wunding.learn.excitation.service.admin.dto.AwardListDTO;
import com.wunding.learn.excitation.service.admin.dto.AwardPageDTO;
import com.wunding.learn.excitation.service.admin.dto.AwardPreviewDTO;
import com.wunding.learn.excitation.service.admin.dto.AwardSaveDTO;
import com.wunding.learn.excitation.service.admin.dto.LotteryRecordPageDTO;
import com.wunding.learn.excitation.service.admin.query.AwardListQuery;
import com.wunding.learn.excitation.service.admin.query.AwardQuery;
import com.wunding.learn.excitation.service.admin.query.LotteryRecordPageQuery;
import com.wunding.learn.excitation.service.client.dto.AwardClientDTO;
import com.wunding.learn.excitation.service.client.dto.AwardClientInFoDTO;
import com.wunding.learn.excitation.service.client.dto.AwardListClientDTO;
import com.wunding.learn.excitation.service.client.dto.AwardUserInFoClientDTO;
import com.wunding.learn.excitation.service.client.dto.CommodityListDTO;
import com.wunding.learn.excitation.service.client.dto.DrawPrizeClientDTO;
import com.wunding.learn.excitation.service.client.dto.ExcitationEventObjectDTO;
import com.wunding.learn.excitation.service.client.dto.GameLotteryRecordContactDTO;
import com.wunding.learn.excitation.service.client.dto.GameOptionDTO;
import com.wunding.learn.excitation.service.client.dto.MyAwardRecordClientDTO;
import com.wunding.learn.excitation.service.client.query.CommodityQuery;
import com.wunding.learn.excitation.service.client.query.ExcitationApiQuery;
import com.wunding.learn.excitation.service.component.AwardViewLimitComponent;
import com.wunding.learn.excitation.service.dao.AwardDao;
import com.wunding.learn.excitation.service.mapper.AwardMapper;
import com.wunding.learn.excitation.service.mapper.AwardRedeemRecordMapper;
import com.wunding.learn.excitation.service.mapper.GameOptionMapper;
import com.wunding.learn.excitation.service.model.Award;
import com.wunding.learn.excitation.service.model.AwardRedeemRecord;
import com.wunding.learn.excitation.service.model.Game;
import com.wunding.learn.excitation.service.model.GameLotteryRecord;
import com.wunding.learn.excitation.service.model.GameOption;
import com.wunding.learn.excitation.service.service.IAwardService;
import com.wunding.learn.excitation.service.service.IExcitationEventService;
import com.wunding.learn.excitation.service.service.IGameLotteryRecordService;
import com.wunding.learn.excitation.service.service.IGameOptionService;
import com.wunding.learn.excitation.service.service.IUserIntegralService;
import com.wunding.learn.excitation.service.util.LotteryUtil;
import com.wunding.learn.file.api.component.ExportComponent;
import com.wunding.learn.file.api.constant.ExportBizType;
import com.wunding.learn.file.api.constant.ExportFileNameEnum;
import com.wunding.learn.file.api.dto.IExportDataDTO;
import com.wunding.learn.file.api.dto.NamePath;
import com.wunding.learn.file.api.service.FileFeign;
import com.wunding.learn.user.api.query.ProgrammedIdQuery;
import com.wunding.learn.user.api.service.OrgFeign;
import com.wunding.learn.user.api.service.ParaFeign;
import com.wunding.learn.user.api.service.UserFeign;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * <p> 奖品信息表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi</a>
 * @since 2022-08-08
 */
@Slf4j
@Service("awardService")
public class AwardServiceImpl extends ServiceImpl<AwardMapper, Award> implements IAwardService {

    @Resource
    private FileFeign fileFeign;


    @Resource
    private AwardViewLimitComponent awardViewLimitComponent;

    @Resource
    private GameOptionMapper gameOptionMapper;

    @Resource
    private ExportComponent exportComponent;


    @Resource
    private IGameOptionService gameOptionService;

    @Resource
    private IGameLotteryRecordService gameLotteryRecordService;


    @Resource
    private IExcitationEventService excitationEventService;

    @Resource
    private IUserIntegralService userIntegralService;

    @Resource
    private OrgFeign orgFeign;
    @Resource
    private UserFeign userFeign;

    @Resource
    private AwardRedeemRecordMapper awardRedeemRecordMapper;

    @Resource(name = "awardDao")
    private AwardDao awardDao;

    @Resource
    private IResourceViewLimitService resourceViewLimitService;

    @Resource
    private ParaFeign paraFeign;

    // 获取抽奖参数配置
    private static List<GameOptionDTO> getGameOptionDtoList(List<GameOptionDTO> awardOptions) {
        List<GameOptionDTO> crtList = new ArrayList<>();
        for (GameOptionDTO ao : awardOptions) {
            if (ao.getAttr() == 1) {
                // 设置了每个奖品限制次数
                if (ao.getOptTimes() > 0) {
                    // 每个奖品的限制次数
                    int optTimes = ao.getOptTimes();
                    // 每个人每个奖品的中奖次数
                    int winAll = ao.getWinAll();
                    if (winAll < optTimes) {
                        crtList.add(ao);
                    }
                } else {
                    crtList.add(ao);
                }
            } else {
                crtList.add(ao);
            }
        }
        return crtList;
    }

    @Override
    public PageInfo<AwardPageDTO> getAwardDtoPage(AwardQuery awardQuery) {
        Set<String> managerAreaOrgIds = orgFeign.findUserManageAreaLevelPath(UserThreadContext.getUserId());
        awardQuery.setManagerAreaOrgIds(managerAreaOrgIds);
        awardQuery.setCurrentOrgId(UserThreadContext.getOrgId());
        awardQuery.setCurrentUserId(UserThreadContext.getUserId());
        PageInfo<AwardPageDTO> pageInfo = PageMethod.startPage(awardQuery.getPageNo(), awardQuery.getPageSize())
            .doSelectPageInfo(() -> baseMapper.getAwardManageList(awardQuery));
        pageInfo.getList().forEach(dto -> dto.setExchangedNum(dto.getTotal().subtract(dto.getAvailableNum())));
        return pageInfo;
    }

    @Override
    public List<AwardListDTO> getAwardDTOList(AwardListQuery awardListQuery) {
        List<Award> awardList = list(new LambdaQueryWrapper<Award>()
            .eq(Award::getIsOnSale, GeneralJudgeEnum.CONFIRM.getValue())
            .like(StringUtils.isNotBlank(awardListQuery.getName()), Award::getName, awardListQuery.getName())
            .orderByDesc(Award::getCreateTime));
        return awardList.stream().map(award -> {
            AwardListDTO dto = new AwardListDTO();
            BeanUtils.copyProperties(award, dto);
            dto.setConsumeNum(dto.getTotal().subtract(dto.getAvailableNum()));
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public AwardDTO getAwardById(String id) {
        Award award = getById(id);
        AwardDTO dto = new AwardDTO();
        BeanUtils.copyProperties(award, dto);
        dto.setCoverImg(fileFeign.getImageFileNamePath(id, ImageTypeEnum.AwardImgIcon.name()));
        dto.setDetailImg(fileFeign.getImageFileNamePath(id, ImageTypeEnum.AwardImgFirst.name()));

        dto.setLimit(awardViewLimitComponent.getViewLimitBaseInfo(id));
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveAward(AwardSaveDTO saveDTO) {
        // 当下发范围为仅创建人可见,不用鉴权
        if (saveDTO.getViewType() != 1) {
            //对调整的下发范围进行鉴权
            ProgrammedIdQuery programmedIdQuery = new ProgrammedIdQuery();
            programmedIdQuery.setNewProgrammeId(saveDTO.getProgrammeId());
            boolean checkViewLimit = userFeign.checkUserManageArea(programmedIdQuery);
            if (!checkViewLimit) {
                throw new BusinessException(ErrorNoEnum.ERR_VIEW_LIMIT);
            }
        }
        // 保存奖品信息
        String awardId = newId();
        Award award = new Award();
        award.setId(awardId);
        award.setAvailableNum(award.getTotal());
        // 原始逻辑，部门ID先跟着创建人走，客户id和是否游戏奖品未使用
        award.setOrgId(UserThreadContext.getOrgId());
        BeanUtils.copyProperties(saveDTO, award);
        award.setAvailableNum(award.getTotal());
        awardDao.saveAward(award);

        // 保存封面图片
        fileFeign.saveImage(awardId, ImageTypeEnum.AwardImgIcon.name(), saveDTO.getCoverImg().getName(),
            saveDTO.getCoverImg().getPath());
        // 保存详情图
        fileFeign.saveImage(awardId, ImageTypeEnum.AwardImgFirst.name(), saveDTO.getDetailImg().getName(),
            saveDTO.getDetailImg().getPath());

        // 保存下发范围
        awardViewLimitComponent.handleNewViewLimit(saveDTO.getProgrammeId(), awardId);
    }

    @Override
    public void saveIntegrateAward(AwardIntegrateSaveDTO saveDTO) {
        // 保存奖品信息
        String awardId = newId();
        Award award = new Award();
        award.setId(awardId);
        award.setAvailableNum(award.getTotal());
        // 原始逻辑，部门ID先跟着创建人走，客户id和是否游戏奖品未使用
        award.setOrgId(UserThreadContext.getOrgId());
        BeanUtils.copyProperties(saveDTO, award);
        award.setAvailableNum(award.getTotal());
        award.setConsumeNum(BigDecimal.valueOf(0));
        awardDao.saveAward(award);

        // 保存封面图片
        fileFeign.saveImage(awardId, ImageTypeEnum.AwardImgIcon.name(), saveDTO.getCoverImg().getName(),
            saveDTO.getCoverImg().getPath());

        // 保存下发范围
        if (Optional.ofNullable(saveDTO.getProgrammeId()).isPresent()) {
            awardViewLimitComponent.handleNewViewLimit(saveDTO.getProgrammeId(), awardId);
        }
    }

    @Override
    public void updateAward(String id, AwardSaveDTO saveDTO) {
        // 当下发范围为仅创建人可见,不用鉴权
        if (saveDTO.getViewType() != 1) {
            //查询考试的历史下发方案
            Long programmeId = resourceViewLimitService.getViewLimitIdByResourceId(id);
            //下发范围方案调整时,对调整的下发范围进行鉴权
            if (!programmeId.equals(saveDTO.getProgrammeId())) {
                ProgrammedIdQuery programmedIdQuery = new ProgrammedIdQuery();
                programmedIdQuery.setNewProgrammeId(saveDTO.getProgrammeId());
                programmedIdQuery.setOldProgrammeId(programmeId);
                boolean checkViewLimit = userFeign.checkUserManageArea(programmedIdQuery);
                if (!checkViewLimit) {
                    throw new BusinessException(ErrorNoEnum.ERR_VIEW_LIMIT);
                }
            }
        }
        // 获取DB数据
        Award dbAward = getById(id);
        if (!Objects.equals(saveDTO.getIsOnSale(), dbAward.getIsOnSale())
            && Objects.equals(saveDTO.getIsOnSale(), GeneralJudgeEnum.NEGATIVE.getValue())) {
            disableCheck(Collections.singletonList(id), "下架");
        }

        // 更新奖品数据
        Award award = new Award();
        award.setId(id);
        BeanUtils.copyProperties(saveDTO, award);

        // 原奖品已兑换数量
        BigDecimal consumeNum = dbAward.getTotal().subtract(dbAward.getAvailableNum());

        // 修改后奖品剩余数量
        BigDecimal availableNum = award.getTotal().subtract(consumeNum);


        // 新库存数验证（库存数不可小于已兑换数）
        if (availableNum.compareTo(BigDecimal.ZERO) < 0) {
            throw new BusinessException(ExcitationErrorNoEnum.ERR_AWARD_MIN_TOTAL_ERROR, null,
                consumeNum.intValue() + "");
        }


        award.setAvailableNum(availableNum);
        award.setOrgId(UserThreadContext.getOrgId());
        awardDao.updateAward(award);

        // 更新奖品封面图
        updateImages(saveDTO.getCoverImg(), id, ImageTypeEnum.AwardImgIcon);
        // 更新奖品详情图
        updateImages(saveDTO.getDetailImg(), id, ImageTypeEnum.AwardImgFirst);

        // 更新奖品下发范围
        // 保存下发范围
        if (Optional.ofNullable(saveDTO.getProgrammeId()).isPresent()) {
            awardViewLimitComponent.handleNewViewLimit(saveDTO.getProgrammeId(), id);
        }
    }

    @Override
    public void updateAwardIntegrate(String id, AwardIntegrateSaveDTO saveDTO) {
        // 获取DB数据
        Award dbAward = getById(id);
        if (!Objects.equals(saveDTO.getIsOnSale(), dbAward.getIsOnSale())
            && Objects.equals(saveDTO.getIsOnSale(), GeneralJudgeEnum.NEGATIVE.getValue())) {
            disableCheck(Collections.singletonList(id), "下架");
        }
        // 已经兑换的数量
        BigDecimal exchanged = dbAward.getTotal().subtract(dbAward.getAvailableNum());
        // 处理奖品的数量
        if (saveDTO.getTotal().compareTo(exchanged) < 0){
            throw new BusinessException(ExcitationErrorNoEnum.ERR_GAME_OPTION_AWARD_TOTAL_NOT_LESS_THAN_EXCHANGED);
        }

        // 更新奖品数据
        Award award = new Award();
        award.setId(id);
        BeanUtils.copyProperties(saveDTO, award);
        award.setAvailableNum(award.getTotal().subtract(exchanged));
        award.setOrgId(UserThreadContext.getOrgId());
        awardDao.updateAward(award);

        // 更新奖品封面图
        updateImages(saveDTO.getCoverImg(), id, ImageTypeEnum.AwardImgIcon);

        // 更新奖品下发范围
        if (Optional.ofNullable(saveDTO.getProgrammeId()).isPresent()) {
            awardViewLimitComponent.handleNewViewLimit(saveDTO.getProgrammeId(), id);
        }
    }

    @Override
    public AwardClientDTO getAwardList() {
        String userId = UserThreadContext.getUserId();
        AwardClientDTO awardClientDTO = new AwardClientDTO();
        List<AwardListClientDTO> list = getBaseMapper().getAwardClientList();
        if (!CollectionUtils.isEmpty(list)) {
            awardClientDTO.setAwardList(list);
        }

        Game a = getBaseMapper().getUserSpace();
        if (a != null) {
            // 获取每天的抽奖记录数
            Map<String, Object> pMap = new HashMap<>(3);
            Date pDate = new Date();
            Calendar calendar = new GregorianCalendar();
            calendar.setTime(pDate);
            calendar.add(Calendar.DATE, 1);
            pMap.put("startTime", DateUtil.getYmdStr(new Date()));
            pMap.put("endTime", DateUtil.getYmdStr(calendar.getTime()));
            pMap.put("uid", UserThreadContext.getUserId());
            awardClientDTO.setVersion(a.getUpdateTime().getTime());
            int todayTimes = getBaseMapper().getMonthAwardDrawCount(pMap);
            BigDecimal credit = userIntegralService.getUserIntegral(userId);
            int beanTimes =
                getCanPlayGameTimesHavingBeaning(a.getMaxCountEveryday(), todayTimes,
                    credit.intValue(), a.getConsumeNum().intValue());

            awardClientDTO.setDrawCount(beanTimes);
            awardClientDTO.setEveryDayDrawCount(a.getMaxCountEveryday());
            awardClientDTO.setPayIntegral(a.getConsumeNum());
            awardClientDTO.setDesc(a.getContent());
            awardClientDTO.setVersion(a.getUpdateTime().getTime());
        } else {
            awardClientDTO.setDrawCount(-1);
            awardClientDTO.setEveryDayDrawCount(0);
            awardClientDTO.setPayIntegral(new BigDecimal(0));
            awardClientDTO.setDesc("");
            awardClientDTO.setVersion(null);
        }
        return awardClientDTO;
    }

    private int getCanPlayGameTimesHavingBeaning(Integer countAll, int havBean, int scoreAll, int everyScore) {
        if (countAll > havBean && everyScore <= scoreAll && countAll != 0 && scoreAll > 0 && everyScore > 0) {
            int n = scoreAll / everyScore;
            return Math.min(n, countAll - havBean);
        }
        return 0;
    }


    @Override
    public DrawPrizeClientDTO getDrawPrize(Long version) {
        DrawPrizeClientDTO drawPrizeClientDTO = new DrawPrizeClientDTO();
        String userId = UserThreadContext.getUserId();
        Date date = new Date(version);
        //根据系统参数设置,是否是关闭可兑换区分配置 SYSTEM_CONFIG_CODE_912,关闭了的话可以直接使用总积分
        Integer isClose = Integer
            .valueOf(paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_912.getCode()));
        AwardClientInFoDTO award = getBaseMapper().getAwardInfo(userId, date,isClose);
        // 1、检测version版本是否正确
        if (null == award || award.getIsVersion() == 0) {
            throw new BusinessException(ExcitationErrorNoEnum.ERR_GAME_OR_VERSION_NULL);
        } else {
            // 2、检测是否积分不足
            if (award.getCurrIntegral() == null || award.getCurrIntegral() < 0) {
                throw new BusinessException(ExcitationErrorNoEnum.ERR_INTEGRAL_NOT_ENOUGH);
            } else {
                // 3、检测是否还有抽奖次数
                if (award.getIsDarw() == null || award.getIsDarw() == 0) {
                    throw new BusinessException(ExcitationErrorNoEnum.ERR_GET_AWARD_ZERO);
                } else {
                    // 4、抽奖
                    execDrawPrize(award, userId, drawPrizeClientDTO);
                }
            }
        }
        return drawPrizeClientDTO;
    }

    // 4、抽奖
    private void execDrawPrize(AwardClientInFoDTO award, String userId, DrawPrizeClientDTO drawPrizeClientDTO) {
        if (award.getOpTions() != null && !award.getOpTions().isEmpty()) {
            // 4、抽奖

            List<GameOptionDTO> awardOptions = award.getOpTions();
            List<GameOptionDTO> crtList = getGameOptionDtoList(awardOptions);

            // 1表示抽中了奖品，0非奖品，所以需要把奖品数量-1
            GameOptionDTO option = null;
            try{
                option = LotteryUtil.lottery(crtList);
            }catch (Exception e){
                log.error("execDrawPrize 抽奖算法报错,error:",e);
            }
            if(Objects.isNull(option)) {
                throw new BusinessException(ExcitationErrorNoEnum.ERR_GAME_OPTION_AWARD_PRIZES_IS_ZERO);
            }
            if (option.getAttr() == 1) {
                gameOptionService.updateAwardOptions(option.getId());
                updateAward(option.getAwardId());
            }

            // 统一抽奖记录表和奖品兑换表的主键id,将两张表关联起来
            String id = newId();
            // 写入抽奖记录
            GameLotteryRecord awardRecord = new GameLotteryRecord();
            awardRecord.setId(id);
            awardRecord.setCreateBy(userId);
            awardRecord.setCreateTime(new Date());
            awardRecord.setAwardName(option.getAwardName());
            awardRecord.setAwardName(StringUtils.isNotBlank(option.getAwardName()) ? option.getAwardName() : "");
            awardRecord.setAwardLevel(option.getAttr());
            awardRecord.setOptionId(option.getId());
            awardRecord.setAwardId(StringUtils.isNotBlank(option.getAwardId()) ? option.getAwardId() : "");
            gameLotteryRecordService.save(awardRecord);

            // 也写入兑换记录
            AwardRedeemRecord awardRedeemRecord = new AwardRedeemRecord();
            awardRedeemRecord.setId(id);
            awardRedeemRecord
                .setAwardId(StringUtils.isNotBlank(option.getAwardId()) ? option.getAwardId() : "");
            awardRedeemRecord.setCreateBy(userId);
            awardRedeemRecord.setCreateTime(new Date());
            awardRedeemRecord.setConsumeNum(new BigDecimal(1));
            awardRedeemRecord.setRedeemNum(new BigDecimal(1));
            awardRedeemRecord.setStatus(0);
            awardRedeemRecord.setDrawBy(userId);
            awardRedeemRecord.setDrawTime(new Date());
            awardRedeemRecord.setExcitationType(ExcitationTypeEnum.INTEGRAL.getCode());
            awardRedeemRecordMapper.insert(awardRedeemRecord);

            // 积分消耗
            ExcitationEventObjectDTO excitationEventObject = new ExcitationEventObjectDTO(userId,
                awardRecord.getId(),
                OtherEventCategoryEnum.integralDraw.name(), award.getConsumeNum(),
                ExcitationTypeEnum.INTEGRAL.getCode(), 1, userId);
            excitationEventObject.setRemark("积分抽奖");
            excitationEventService.otherEventHandler(excitationEventObject);

            drawPrizeClientDTO.setPrizeId(option.getId());
        }
    }

    @Override
    public PageInfo<MyAwardRecordClientDTO> getAwardDraw(ExcitationApiQuery query) {
        PageInfo<MyAwardRecordClientDTO> pageInfo = new PageInfo<>();
        String userId = UserThreadContext.getUserId();
        LotteryRecordPageQuery lotteryRecordPageQuery = new LotteryRecordPageQuery();
        lotteryRecordPageQuery.setPageNo(query.getPageNo());
        lotteryRecordPageQuery.setPageSize(query.getPageSize());
        lotteryRecordPageQuery.setUserIdStr(userId);
        PageInfo<LotteryRecordPageDTO> lotteryRecordPage = gameLotteryRecordService.getLotteryRecordPage(
            lotteryRecordPageQuery);
        List<LotteryRecordPageDTO> list = lotteryRecordPage.getList();
        List<MyAwardRecordClientDTO> myAwardRecordClientDTOList;
        if (!CollectionUtils.isEmpty(list)) {
            BeanUtils.copyProperties(lotteryRecordPage, pageInfo);
            myAwardRecordClientDTOList = BeanListUtils.copyListProperties(list, MyAwardRecordClientDTO::new);
            pageInfo.setList(myAwardRecordClientDTOList);
            List<String> collect = myAwardRecordClientDTOList.stream().map(MyAwardRecordClientDTO::getAwardId)
                .collect(Collectors.toList());
            Map<String, String> imageUrlsByIds = fileFeign.getImageUrlsByIds(collect,
                ImageTypeEnum.AwardImgFirst.name());
            for (MyAwardRecordClientDTO dto : myAwardRecordClientDTOList) {
                String imgPath = imageUrlsByIds.get(dto.getAwardId());
                if (StringUtils.isNotBlank(imgPath)) {
                    dto.setImage(imgPath);
                }
            }
        }
        return pageInfo;
    }

    @Override
    public AwardUserInFoClientDTO getAwardUserInfo() {
        String userId = UserThreadContext.getUserId();
        return getBaseMapper().getAwardUserInfo(userId);
    }

    @Override
    public PageInfo<CommodityListDTO> getMallCommodityList(ExcitationApiQuery query) {
        String userId = UserThreadContext.getUserId();
        // 商品列表
        CommodityQuery commodityQuery = new CommodityQuery();
        commodityQuery.setUserId(userId);
        PageInfo<CommodityListDTO> award = PageMethod.startPage(query.getPageNo(),
                query.getPageSize())
            .doSelectPageInfo(() -> baseMapper.getMallRecommendCommodityList(commodityQuery, userId));
        List<CommodityListDTO> list = award.getList();
        if (!CollectionUtils.isEmpty(list)) {
            List<String> collect = list.stream().map(CommodityListDTO::getId).collect(Collectors.toList());
            // 封面图片
            Map<String, String> thumbsMaps = fileFeign.getImageUrlsByIds(collect,
                ImageTypeEnum.AwardImgIcon.name());
            // 详情图片
            Map<String, String> imageMaps = fileFeign.getImageUrlsByIds(collect,
                ImageTypeEnum.AwardImgFirst.name());
            for (CommodityListDTO dto : list) {
                String thumbs = thumbsMaps.get(dto.getId());
                if (StringUtils.isNotBlank(thumbs)) {
                    dto.setThumbs(thumbs);
                }
                String image = imageMaps.get(dto.getId());
                if (StringUtils.isNotBlank(image)) {
                    dto.setImage(image);
                }
            }
        }
        return award;
    }

    @Override
    public List<CommodityListDTO> getMallCommodityHeadLineAwardList(ExcitationApiQuery headLineQuery) {
        // 推荐商品
        String userId = UserThreadContext.getUserId();
        CommodityQuery headLineAwardQuery = new CommodityQuery();
        headLineAwardQuery.setUserId(userId);
        headLineAwardQuery.setIsHeadLine(1);
        PageInfo<CommodityListDTO> headLineAward = PageMethod.startPage(headLineQuery.getPageNo(),
                headLineAwardQuery.getPageSize())
            .doSelectPageInfo(() -> baseMapper.getMallRecommendCommodityList(headLineAwardQuery, userId));
        List<CommodityListDTO> headLineAwardList = headLineAward.getList();
        if (!CollectionUtils.isEmpty(headLineAwardList)) {
            List<String> collect = headLineAwardList.stream().map(CommodityListDTO::getId).collect(Collectors.toList());
            // 封面图片
            Map<String, String> thumbsMaps = fileFeign.getImageUrlsByIds(collect,
                ImageTypeEnum.AwardImgIcon.name());
            // 详情图片
            Map<String, String> imageMaps = fileFeign.getImageUrlsByIds(collect,
                ImageTypeEnum.AwardImgFirst.name());
            for (CommodityListDTO dto : headLineAwardList) {
                String thumbs = thumbsMaps.get(dto.getId());
                if (StringUtils.isNotBlank(thumbs)) {
                    dto.setThumbs(thumbs);
                }
                String image = imageMaps.get(dto.getId());
                if (StringUtils.isNotBlank(image)) {
                    dto.setImage(image);
                }
            }
            return headLineAwardList;
        }
        return new ArrayList<>();
    }

    @Override
    public CommodityListDTO getCommodityInfo(String id) {
        Award byId = getById(id);
        CommodityListDTO commodityListDTO = new CommodityListDTO();
        BeanUtils.copyProperties(byId, commodityListDTO);
        String thumbs = fileFeign.getImageUrl(id, ImageTypeEnum.AwardImgIcon.name());
        String image = fileFeign.getImageUrl(id, ImageTypeEnum.AwardImgFirst.name());
        if (StringUtils.isNotBlank(thumbs)) {
            commodityListDTO.setThumbs(thumbs);
        }
        if (StringUtils.isNotBlank(image)) {
            commodityListDTO.setImage(image);
        }
        return commodityListDTO;
    }

    @Override
    public Award getMallCommodityInfo(String id) {
        LambdaQueryWrapper<Award> query = new LambdaQueryWrapper<>();
        query.eq(Award::getId, id);
        query.eq(Award::getIsOnSale, AvailableEnum.AVAILABLE.getValue());
        query.eq(Award::getIsGame, 0);
        query.le(Award::getBeginTime, new Date());
        query.ge(Award::getEndTime, new Date());
        return getOne(query);
    }

    @Override
    public void updateAwardDrawContact(GameLotteryRecordContactDTO dto) {
        //修改中奖地址
        GameLotteryRecord gameLotteryRecord = new GameLotteryRecord();
        BeanUtils.copyProperties(dto, gameLotteryRecord);
        gameLotteryRecord.setUpdateBy(UserThreadContext.getUserId());
        gameLotteryRecord.setUpdateTime(new Date());
        gameLotteryRecordService.updateById(gameLotteryRecord);
        //修改兑换地址
        AwardRedeemRecord awardRedeemRecord = new AwardRedeemRecord();
        awardRedeemRecord.setId(dto.getId());
        awardRedeemRecord.setAddress(dto.getAddress());
        awardRedeemRecord.setPhone(dto.getContactPhone());
        awardRedeemRecord.setRemark(dto.getRemark());
        awardRedeemRecordMapper.updateById(awardRedeemRecord);


    }

    @Override
    public void updateAward(String awardId) {
        Award byId = getById(awardId);
        LambdaUpdateWrapper<Award> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.set(Award::getAvailableNum, byId.getAvailableNum().subtract(new BigDecimal(1)));
        queryWrapper.eq(Award::getId, awardId);
        update(queryWrapper);
    }


    @Override
    public void delete(String ids) {
        List<String> idList = TranslateUtil.translateBySplit(ids, String.class);
        disableCheck(idList, "删除");
        List<Award> awards = listByIds(idList);
        awards.forEach(award -> awardDao.delAward(award));
        if (!CollectionUtils.isEmpty(idList)) {
            idList.forEach(id -> awardViewLimitComponent.delViewLimit(id));
        }

    }

    @Override
    public void onSale(String ids, Integer saleStatus) {
        List<String> idList = TranslateUtil.translateBySplit(ids, String.class);
        if (Objects.equals(saleStatus, GeneralJudgeEnum.NEGATIVE.getValue())) {
            disableCheck(idList, "下架");
        }
        List<Award> awards = listByIds(idList);
        awards.forEach(award -> awardDao
            .updateAward(new Award().setId(award.getId()).setName(award.getName()).setIsOnSale(saleStatus)));
    }


    @Override
    public AwardPreviewDTO previewAward(String id) {
        Award award = getById(id);
        if (Objects.isNull(award)) {
            throw new BusinessException(ExcitationErrorNoEnum.ERR_AWARD_NO_EXIST);
        }

        AwardPreviewDTO dto = new AwardPreviewDTO();
        BeanUtils.copyProperties(award, dto);
        dto.setDetailImg(fileFeign.getImageFileNamePath(id, ImageTypeEnum.AwardImgFirst.name()));
        return dto;
    }

    /**
     * 判断是否更新图片
     *
     * @param newNamePath 图片路径
     * @param awardId     奖品Id
     * @param imageType   图像类型
     */
    public void updateImages(NamePath newNamePath, String awardId, ImageTypeEnum imageType) {
        NamePath image = fileFeign.getImageFileNamePath(awardId, imageType.name());
        String oldPath = image.getPath();
        String newPath = newNamePath.getPath();
        // 图片地址变化处理
        if (!org.apache.commons.lang3.StringUtils.equals(oldPath, newPath)) {
            // 删除原来的
            fileFeign.deleteImageByImageId(image.getId());
            // 添加新的图片
            fileFeign.saveImage(awardId, imageType.name(), newNamePath.getName(), newPath);
        }
    }

    /**
     * 奖品下架/删除检测
     */
    private void disableCheck(List<String> idList, String actionName) {
        Long awardOptionCount = gameOptionMapper
            .selectCount(new LambdaQueryWrapper<GameOption>().in(GameOption::getAwardId, idList));
        if (Objects.isNull(awardOptionCount) || awardOptionCount.compareTo(0L) == 0) {
            return;
        }
        throw new BusinessException(ExcitationErrorNoEnum.ERR_AWARD_BEEN_USED_BY_GAME_OPTION, null, actionName);
    }

    @Override
    @Async
    public void exportData(AwardQuery awardQuery) {
        IExportDataDTO exportDataDTO = new IExportDataDTO() {
            @Override
            public List<Map<String, Object>> getData(Integer pageNo, Integer pageSize) {
                IAwardService awardService = SpringUtil.getBean("awardService", IAwardService.class);
                awardQuery.setExport(true);
                awardQuery.setPageNo(pageNo);
                awardQuery.setPageSize(pageSize);
                List<Map<String, Object>> awardListExportDTOS = new ArrayList<>();
                if (Optional.ofNullable(awardService).isPresent()) {
                    PageInfo<AwardPageDTO> pageInfo = awardService.getAwardDtoPage(awardQuery);
                    for (AwardPageDTO awardPageDTO : pageInfo.getList()) {
                        Map<String, Object> beanMap = JsonUtil.parseObjectToMap(awardPageDTO);
                        awardListExportDTOS.add(beanMap);
                    }
                }
                return awardListExportDTOS;
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.Award;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.Award.getType();
            }

        };

        exportComponent.exportRecord(exportDataDTO);
    }
}
