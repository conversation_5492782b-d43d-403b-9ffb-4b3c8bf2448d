package com.wunding.learn.excitation.service.imports;

import com.wunding.learn.common.enums.other.ExcitationOperationEnum;
import com.wunding.learn.common.util.excel.AbstractExcelTemplate;
import com.wunding.learn.common.util.excel.ExcelCheckMessage;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.excitation.service.admin.dto.UserGoldCoinDataImportDTO;
import com.wunding.learn.excitation.service.service.IUserGoldCoinService;
import com.wunding.learn.user.api.dto.UserDTO;
import com.wunding.learn.user.api.service.UserFeign;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * <AUTHOR>
 * @date 2022/11/17
 */
public class UserGoldCoinTemplate extends AbstractExcelTemplate {

    private static final String[] IMPORT_TITLES = {
        "姓名", "手机号", "账号", "机构名称", "金币数量", "说明"
    };

    private static final String DATA_TEXT = "数据与手机号不匹配";

    RedisTemplate<String, Object> redisTemplate;
    UserFeign userFeign;
    IUserGoldCoinService userGoldCoinService;
    private Integer isExchange;

    public UserGoldCoinTemplate(RedisTemplate<String, Object> redisTemplate, UserFeign userFeign,
        IUserGoldCoinService userGoldCoinService, Integer isExchange) {
        super(IMPORT_TITLES);
        this.redisTemplate = redisTemplate;
        this.userFeign = userFeign;
        this.userGoldCoinService = userGoldCoinService;
        this.isExchange = isExchange;
    }

    @Override
    protected ExcelCheckMessage doCheck(ExcelCheckMessage excelCheckMessage) {
        // 是否通过校验
        boolean isPassCheck = true;
//        redisTemplate.delete(UserRedisKeyEnum)
        ArrayList<UserGoldCoinDataImportDTO> dataList = new ArrayList<>();
        UserGoldCoinDataImportDTO goldCoinDataImportDTO;
        List<String> messages = excelCheckMessage.getMessage();
        String[][] excel = excelCheckMessage.getExcel();

        Set<String> phones = Arrays.stream(excel).map(e -> e[1].trim()).collect(Collectors.toSet());
        Map<String, UserDTO> userDTOMap = userFeign.getUserMapByPhones(phones);
        int rowNum;
        for (int i = 1; i < excel.length; i++) {
            rowNum = i + 1;
            goldCoinDataImportDTO = new UserGoldCoinDataImportDTO();
            BigDecimal goldCoin = BigDecimal.ZERO;
            // 校验手机号
            UserDTO userDTO = userDTOMap.get(excel[i][1]);
            if (Objects.isNull(userDTO)) {
                messages.add("第" + rowNum + "行的" + IMPORT_TITLES[1] + "不存在");
                isPassCheck = false;
            }
            // 校验金币数量是否是数字
            try {
                goldCoin = new BigDecimal(excel[i][4].trim());
            } catch (Exception e) {
                e.printStackTrace();
                messages.add("第" + rowNum + "行的" + IMPORT_TITLES[4] + "数据不正确，只能够是数字");
                isPassCheck = false;
            }
            // 校验姓名与手机号
            isPassCheck = passCheck(StringUtils.isNotBlank(excel[i][0].trim()) && userDTO != null
                    && !Objects.equals(excel[i][0].trim(), userDTO.getFullName()), messages, rowNum, 0, DATA_TEXT, isPassCheck);
            // 校验账号与手机号
            isPassCheck = passCheck(
                StringUtils.isNotBlank(excel[i][2].trim()) && userDTO != null && !Objects.equals(excel[i][2].trim(),
                    userDTO.getLoginName()), messages, rowNum, 2, DATA_TEXT, isPassCheck);

            // 校验机构名称与手机号
            isPassCheck = passCheck(
                StringUtils.isNotBlank(excel[i][3].trim()) && userDTO != null && !Objects.equals(excel[i][3].trim(),
                    userDTO.getMemberOrgName()), messages, rowNum, 3, DATA_TEXT, isPassCheck);

            // 如果校验不通过
            if (!isPassCheck) {
                continue;
            }

            String userId = userDTO.getId();
            goldCoinDataImportDTO.setUserId(userId);

            try {
                goldCoin = new BigDecimal(excel[i][4].trim());
            } catch (Exception e) {
                e.printStackTrace();
                messages.add("第" + rowNum + "行的" + IMPORT_TITLES[4] + "数据不正确，只能够是数字");
            }
            goldCoinDataImportDTO.setNum(goldCoin);
            goldCoinDataImportDTO.setAvailableNum(goldCoin);
            goldCoinDataImportDTO.setOperateNum(goldCoin);
            //导入都按加法算，可支持负数
            goldCoinDataImportDTO.setOperateType(ExcitationOperationEnum.INCREASE);
            //填充是否可兑换(默认填充可兑换)，与系统配置参数一致
            goldCoinDataImportDTO.setIsExchange(isExchange);
            dataList.add(goldCoinDataImportDTO);
        }
        excelCheckMessage.setMessage(messages);
        excelCheckMessage.setObjects(dataList);
        return excelCheckMessage;
    }

    private static boolean passCheck(boolean excel, List<String> messages, int rowNum, int x, String dataText,
        boolean isPassCheck) {
        if (excel) {
            messages.add("第" + rowNum + "行的" + IMPORT_TITLES[x] + dataText);
            isPassCheck = false;
        }
        return isPassCheck;
    }
}
