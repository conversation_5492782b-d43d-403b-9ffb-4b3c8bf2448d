package com.wunding.learn.excitation.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.wunding.learn.common.bean.Result;
import com.wunding.learn.common.constant.excitation.ExcitationErrorNoEnum;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.dto.ImportResultDTO;
import com.wunding.learn.common.enums.excitation.ExcitationEventEnum;
import com.wunding.learn.common.enums.excitation.ExcitationTypeEnum;
import com.wunding.learn.common.enums.excitation.OtherEventCategoryEnum;
import com.wunding.learn.common.enums.other.ExcitationOperationEnum;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.enums.other.SystemConfigCodeEnum;
import com.wunding.learn.common.enums.user.UserTypeEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.mybatis.service.impl.BaseServiceImpl;
import com.wunding.learn.common.util.bean.SpringUtil;
import com.wunding.learn.common.util.excel.ExcelCheckMessage;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.common.util.string.TranslateUtil;
import com.wunding.learn.excitation.service.admin.dto.UserGoldCoinDataImportDTO;
import com.wunding.learn.excitation.service.admin.dto.UserGoldCoinImportDTO;
import com.wunding.learn.excitation.service.admin.dto.UserGoldCoinPageDTO;
import com.wunding.learn.excitation.service.admin.dto.UserGoldCoinRefundDTO;
import com.wunding.learn.excitation.service.admin.query.UserGoldCoinPageQuery;
import com.wunding.learn.excitation.service.client.dto.ExcitationEventObjectDTO;
import com.wunding.learn.excitation.service.client.dto.GoldCoinExchangeResource;
import com.wunding.learn.excitation.service.client.dto.MallUserInfoDTO;
import com.wunding.learn.excitation.service.client.dto.UserGoldCoinDTO;
import com.wunding.learn.excitation.service.imports.UserGoldCoinTemplate;
import com.wunding.learn.excitation.service.mapper.ExcitationTradeRecordMapper;
import com.wunding.learn.excitation.service.mapper.UserGoldCoinMapper;
import com.wunding.learn.excitation.service.model.ExchangeRuleConfig;
import com.wunding.learn.excitation.service.model.ExcitationTradeRecord;
import com.wunding.learn.excitation.service.model.UserGoldCoin;
import com.wunding.learn.excitation.service.service.IExchangeRuleConfigService;
import com.wunding.learn.excitation.service.service.IExcitationOperation;
import com.wunding.learn.excitation.service.service.IExcitationService;
import com.wunding.learn.excitation.service.service.IUserGoldCoinService;
import com.wunding.learn.excitation.service.service.IUserIntegralService;
import com.wunding.learn.file.api.component.ExportComponent;
import com.wunding.learn.file.api.constant.ExportBizType;
import com.wunding.learn.file.api.constant.ExportFileNameEnum;
import com.wunding.learn.file.api.dto.AbstractExportDataDTO;
import com.wunding.learn.file.api.dto.IExportDataDTO;
import com.wunding.learn.file.api.dto.ImportDataDTO;
import com.wunding.learn.file.api.service.ImportDataFeign;
import com.wunding.learn.payment.api.dto.RefundOrderDTO;
import com.wunding.learn.payment.api.service.OrderFeign;
import com.wunding.learn.user.api.dto.MemberOrgFeignDTO;
import com.wunding.learn.user.api.dto.UserDTO;
import com.wunding.learn.user.api.service.MemberOrgFeign;
import com.wunding.learn.user.api.service.OrgFeign;
import com.wunding.learn.user.api.service.ParaFeign;
import com.wunding.learn.user.api.service.UserFeign;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p> 用户金币表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi</a>
 * @since 2022-08-08
 */
@Slf4j
@Service("userGoldCoinService")
public class UserGoldCoinServiceImpl extends BaseServiceImpl<UserGoldCoinMapper, UserGoldCoin> implements
    IUserGoldCoinService, IExcitationOperation {

    @Resource
    private UserFeign userFeign;

    @Resource(name="userIntegralService")
    @Lazy
    private IUserIntegralService integralService;

    @Resource
    @Lazy
    private IExchangeRuleConfigService exchangeRuleConfigService;

    @Resource
    private IExcitationService excitationService;

    @Resource
    private ParaFeign paraFeign;

    @Resource
    private OrgFeign orgFeign;

    @Resource
    private MemberOrgFeign memberOrgFeign;

    @Resource
    private ImportDataFeign importDataFeign;

    @Resource
    @SuppressWarnings("rawtypes")
    private RedisTemplate redisTemplate;

    @Resource
    private ExcitationTradeRecordMapper excitationTradeRecordMapper;

    @Resource
    private OrderFeign orderFeign;
    @Resource
    private ExportComponent exportComponent;

    @Override
    public void saveOrUpdateUserGoldCoin(ExcitationEventObjectDTO eventObject) {
        long count = count(new LambdaQueryWrapper<UserGoldCoin>().eq(UserGoldCoin::getUserId, eventObject.getUserId()));
        if (count == 0) {
            // 新增
            save(new UserGoldCoin().setUserId(eventObject.getUserId()).setNum(eventObject.getScore())
                .setRemainNum(eventObject.getScore()).setConvertibleNum(eventObject.getScore()).setOriginNum(BigDecimal.valueOf(0)));
        } else {
            // 更新
            LambdaUpdateWrapper<UserGoldCoin> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(UserGoldCoin::getUserId, eventObject.getUserId());
            String sql = eventObject.getOperateType() == 0 ? "num = num + " + eventObject.getScore()
                + ", remain_num = remain_num + " + eventObject.getScore()
                + ", convertible_num = convertible_num + " + eventObject.getScore()
                : "remain_num = remain_num - " + eventObject.getScore()
                + ",convertible_num =  convertible_num - " + eventObject.getScore();
            updateWrapper.setSql(sql);
            update(updateWrapper);
        }
    }

    @Override
    public BigDecimal getUserGold(String userId) {
        //根据系统参数设置,是否是关闭可兑换区分配置
        String paraValue = paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_912.getCode());
        Integer isClose = StringUtils.isBlank(paraValue) ? Integer.valueOf(0) : Integer.valueOf(paraValue);
        LambdaQueryWrapper<UserGoldCoin> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserGoldCoin::getUserId, userId);
        UserGoldCoin one = getOne(queryWrapper);
        if (one != null) {
            return Objects.equals(isClose, GeneralJudgeEnum.CONFIRM.getValue()) ? one.getRemainNum()
                : one.getConvertibleNum();
        }
        return BigDecimal.ZERO;
    }

    @Override
    public UserGoldCoin initUserGoldCoin(String userId, BigDecimal fund) {
        return initUserGoldCoin(userId, fund, GeneralJudgeEnum.NEGATIVE.getValue());
    }

    @Override
    public UserGoldCoin initUserGoldCoin(String userId, BigDecimal fund, Integer isExchange) {
        UserGoldCoin goldCoin = getById(userId);
        if (Objects.nonNull(goldCoin)) {
            return goldCoin;
        }
        goldCoin = new UserGoldCoin().setUserId(userId).setNum(fund).setOriginNum(fund).setRemainNum(fund);
        if (Objects.equals(isExchange, GeneralJudgeEnum.CONFIRM.getValue())) {
            goldCoin.setConvertibleNum(fund);
        }
        save(goldCoin);
        goldCoin = getById(userId);
        return goldCoin;
    }

    @Override
    public MallUserInfoDTO getUserMallInfo() {
        MallUserInfoDTO userInfoDTO = new MallUserInfoDTO();
        String userId = UserThreadContext.getUserId();
        UserDTO userById = userFeign.getUserById(userId);
        BigDecimal userGold = getUserGold(userId);
        userInfoDTO.setMyGoldCoin(userGold);
        if (StringUtils.isNotBlank(userById.getAvatar())) {
            userInfoDTO.setAvatar(userById.getAvatar());
        }
        userInfoDTO.setFullName(userById.getFullName());
        return userInfoDTO;
    }

    @Override
    public UserGoldCoin getDataById(String userId) {
        return Optional.ofNullable(getById(userId)).orElse(initUserGoldCoin(userId, BigDecimal.ZERO));
    }

    @Override
    public UserGoldCoinDTO
    getCurrentUserGoldCoin() {
        UserGoldCoinDTO userGoldCoin = new UserGoldCoinDTO();
        String currentUserId = UserThreadContext.getUserId();
        // 取系统配置的兑换参数
        //根据系统参数设置,是否是关闭可兑换区分配置

        userGoldCoin.setNum(getUserGold(currentUserId));
        userGoldCoin.setUnitPrice(excitationService.getGoldCoinUnitPrice());
        UserDTO userDTO = userFeign.getUserById(currentUserId);
        userGoldCoin.setUserType(userDTO.getUserType());

        // 取用户积分
        BigDecimal userIntegral = integralService.getUserIntegral(currentUserId);
        GoldCoinExchangeResource resource = new GoldCoinExchangeResource();
        resource.setType(ExcitationTypeEnum.INTEGRAL.getCode());
        resource.setNum(userIntegral);
        ExchangeRuleConfig config = exchangeRuleConfigService.getOne(new LambdaQueryWrapper<ExchangeRuleConfig>()
            .eq(ExchangeRuleConfig::getExcitationId, ExcitationTypeEnum.INTEGRAL.getCode())
            .eq(ExchangeRuleConfig::getSystemType, userDTO.getSystemType())
        );
        // 没有配置对应规则或者资源没有达到配置下限不允许兑换
        if (Objects.isNull(config) || config.getMinBound().compareTo(userIntegral) > 0) {
            resource.setExchangeGoldCoinNum(BigDecimal.ZERO);
        } else {
            // 计算可兑换数量
            BigDecimal exchangeable = userIntegral.divide(config.getConsumeNum(), 0, RoundingMode.FLOOR)
                .multiply(config.getCoinNum());
            resource.setExchangeGoldCoinNum(exchangeable);
        }

        userGoldCoin.setGoldCoinExchangeResources(resource);
        return userGoldCoin;
    }

    @Override
    public BigDecimal add(String userId, BigDecimal amount) {
        return add(userId, amount, GeneralJudgeEnum.NEGATIVE.getValue());
    }

    /**
     * 根据系统参数设置,是否是关闭可兑换区分配置 SYSTEM_CONFIG_CODE_912 1，关闭可兑换区分配置，强制修改可兑换积分成为不可兑换积分入库 2，开启可兑换区分配置，根据传入的是否可兑换积分入库
     *
     * @param userId
     * @param amount
     * @param isExchange
     * @return
     */
    @Override
    public BigDecimal add(String userId, BigDecimal amount, Integer isExchange) {
        //根据系统参数设置,是否是关闭可兑换区分配置
        Integer isClose = Integer.valueOf(
            paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_912.getCode()));
        if (Objects.equals(isClose, GeneralJudgeEnum.CONFIRM.getValue())) {
            //1，关闭可兑换区分配置，强制修改可兑换积分成为不可兑换积分入库
            isExchange = GeneralJudgeEnum.NEGATIVE.getValue();
        } else {
            //2，开启可兑换区分配置，根据传入的是否可兑换积分入库
        }
        return baseAdd(userId, amount, isExchange);
    }

    @Override
    public BigDecimal baseAdd(String userId, BigDecimal amount, Integer isExchange) {
        UserGoldCoin userGoldCoin = initUserGoldCoin(userId, BigDecimal.ZERO, isExchange);
        UserGoldCoin goldCoin = new UserGoldCoin().setUserId(userId).setNum(userGoldCoin.getNum().add(amount))
            .setRemainNum(userGoldCoin.getRemainNum().add(amount));
        if (Objects.equals(isExchange, GeneralJudgeEnum.CONFIRM.getValue())) {
            goldCoin.setConvertibleNum(userGoldCoin.getConvertibleNum().add(amount));
        }
        updateById(goldCoin);
        return goldCoin.getRemainNum();
    }

    /**
     * 根据系统参数设置,是否是关闭可兑换区分配置 SYSTEM_CONFIG_CODE_912 1，关闭可兑换区分配置，优先消耗可兑换激励值，可兑换激励值不够时，再继续消耗不可兑换
     * 2，开启可兑换区分配置，优先消耗可兑换激励值，若是可兑换激励值不够时，直接报错
     *
     * @param userId
     * @param amount
     * @return
     */
    @Override
    public BigDecimal subtract(String userId, BigDecimal amount) {
        //根据系统参数设置,是否是关闭可兑换区分配置
        Integer isClose = Integer.valueOf(
            paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_912.getCode()));
        UserGoldCoin userGoldCoin = initUserGoldCoin(userId, BigDecimal.ZERO, GeneralJudgeEnum.CONFIRM.getValue());
        UserGoldCoin goldCoin;
        BigDecimal convertibleNum = userGoldCoin.getConvertibleNum();
        BigDecimal convertibleNumAmount;

        if (Objects.equals(isClose, GeneralJudgeEnum.CONFIRM.getValue())) {
            //1，关闭可兑换区分配置，优先消耗可兑换激励值，可兑换激励值不够时，再继续消耗不可兑换
            if (userGoldCoin.getRemainNum().compareTo(amount) < 0) {
                throw new BusinessException(ExcitationErrorNoEnum.ERR_GOLD_COIN_NOT_ENOUGH);
            }
            convertibleNumAmount = userGoldCoin.getConvertibleNum().compareTo(amount) < 0 ? new BigDecimal(0)
                : convertibleNum.subtract(amount);
        } else {
            //2，开启可兑换区分配置，优先消耗可兑换激励值，若是可兑换激励值不够时，直接报错
            if (userGoldCoin.getRemainNum().compareTo(amount) < 0
                || userGoldCoin.getConvertibleNum().compareTo(amount) < 0) {
                throw new BusinessException(ExcitationErrorNoEnum.ERR_GOLD_COIN_NOT_ENOUGH);
            }
            convertibleNumAmount = convertibleNum.subtract(amount);
        }

        goldCoin = new UserGoldCoin().setUserId(userId)
            .setRemainNum(userGoldCoin.getRemainNum().subtract(amount))
            .setConvertibleNum(convertibleNumAmount);
        updateById(goldCoin);
        return goldCoin.getRemainNum();
    }

    @Override
    public PageInfo<UserGoldCoinPageDTO> pageUserGoldCoin(UserGoldCoinPageQuery query) {
        if (StringUtils.isNotBlank(query.getOrgId())) {
            Optional.ofNullable(orgFeign.getById(query.getOrgId()))
                .ifPresent(org -> query.setOrgLevelPath(org.getLevelPath()));
        }
        if (StringUtils.isNotBlank(query.getUserIds())) {
            query.setUserIdCollection(TranslateUtil.translateBySplit(query.getUserIds(), String.class));
        }

        if (com.alibaba.excel.util.StringUtils.isNotBlank(query.getUserIds())) {
            query.setUserIdCollection(TranslateUtil.translateBySplit(query.getUserIds(), String.class));
        }
        PageInfo<UserGoldCoinPageDTO> objectPageInfo = PageMethod.startPage(query.getPageNo(), query.getPageSize())
            .doSelectPageInfo(() -> baseMapper.userGoldCoinList(query));

        if (CollectionUtils.isEmpty(objectPageInfo.getList())) {
            return objectPageInfo;
        }
        Set<String> memberOrgIds = objectPageInfo.getList().stream().map(dto -> {
            if (Objects.nonNull(dto) && Objects.nonNull(dto.getUserInfo())) {
                return dto.getUserInfo().getMemberOrgId();
            }
            return StringUtils.EMPTY;
        }).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        Map<String, MemberOrgFeignDTO> orgFeignDTOMap =
            CollectionUtils.isEmpty(memberOrgIds) ? new HashMap<>() : memberOrgFeign.getMapByIds(memberOrgIds);
        Set<String> userIdSet = objectPageInfo.getList().stream().map(UserGoldCoinPageDTO::getUserId)
            .collect(Collectors.toSet());
        Map<String, UserDTO> userNameMap =
            CollectionUtils.isEmpty(userIdSet) ? new HashMap<>() : userFeign.getUserNameMapByIds(userIdSet);
        objectPageInfo.getList().stream().filter(Objects::nonNull).forEach(dto -> {
            Optional.ofNullable(orgFeignDTOMap.get(dto.getUserInfo().getMemberOrgId())).ifPresent(info ->
                dto.getUserInfo().setMemberOrgName(info.getName()));
            Optional.ofNullable(userNameMap.get(dto.getUserId())).ifPresent(user -> {
                Optional.ofNullable(dto.getUserInfo())
                    .ifPresent(userInfoDTO -> userInfoDTO.setPhone(user.getTelephone()));
            });
        });
        return objectPageInfo;
    }

    /**
     * 积分导入 1，先获得导入的积分记录 2，用导入的积分用户id去获取历史现有积分 3，把两部分记录合并，根据用户进行分组 4，全部使用+法运算,进行积分计算 5，更新到用户积分表，新增到积分交易记录表
     *
     * @param dto
     * @return
     */
    @Override
    public ImportResultDTO importData(UserGoldCoinImportDTO dto) {
        ImportResultDTO importResultDTO = new ImportResultDTO();
        importResultDTO.setIsSuccess(false);
        long beginTime = System.currentTimeMillis();
        ImportDataDTO importDataDTO = importDataFeign.getImportData(dto.getFilePath());
        log.info("获取导入的数据耗时：{}，共{}条数据", System.currentTimeMillis() - beginTime,
            importDataDTO.getRowCount());
        String[][] excel = importDataDTO.getExcel();

        //根据系统参数设置,是否是关闭可兑换区分配置 SYSTEM_CONFIG_CODE_912
        String paraValue = paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_912.getCode());
        Integer isClose = StringUtils.isBlank(paraValue) ? Integer.valueOf(0) : Integer.valueOf(paraValue);
        Integer isExchange = Objects.equals(isClose, GeneralJudgeEnum.CONFIRM.getValue()) ? 0 : 1;

        beginTime = System.currentTimeMillis();
        ExcelCheckMessage excelCheckMessage = new UserGoldCoinTemplate(redisTemplate, userFeign, this, isExchange)
            .check(excel);
        log.info("UserGoldCoinTemplate check 耗时：{}", System.currentTimeMillis() - beginTime);

        if (CollectionUtils.isEmpty(excelCheckMessage.getMessage())) {
            if (CollectionUtils.isEmpty(excelCheckMessage.getObjects())) {
                excelCheckMessage.getMessage().add("导入数据为空！");
            }

            int count = excelCheckMessage.getObjects().size();
            beginTime = System.currentTimeMillis();
            Collection<UserGoldCoinDataImportDTO> userGoldCoins = (Collection<UserGoldCoinDataImportDTO>) excelCheckMessage.getObjects();
            //最终计算结果集合
            List<UserGoldCoin> saveUserGoldCoinList = new ArrayList<>();
            //导入的金币集合
            List<UserGoldCoin> goldCoinList = userGoldCoins.stream().map(goldCoinDTO -> {
                UserGoldCoin userGoldCoin = new UserGoldCoin();
                BeanUtils.copyProperties(goldCoinDTO, userGoldCoin);
                //可兑换金币
                BigDecimal convertibleNum = BigDecimal.ZERO;
                //金币可兑换
                if (1 == goldCoinDTO.getIsExchange()) {
                    convertibleNum = goldCoinDTO.getAvailableNum();
                }
                userGoldCoin.setConvertibleNum(convertibleNum);
                return userGoldCoin;
            }).collect(Collectors.toList());

            //获取现有金币集合
            Set<String> goldCoinUserIds = goldCoinList.stream().map(UserGoldCoin::getUserId)
                .collect(Collectors.toSet());
            LambdaQueryWrapper<UserGoldCoin> historyWrapper = new LambdaQueryWrapper<>();
            historyWrapper.in(UserGoldCoin::getUserId, goldCoinUserIds);
            List<UserGoldCoin> historyGoldCoinList = baseMapper.selectList(historyWrapper);
            if (!CollectionUtils.isEmpty(historyGoldCoinList)) {
                goldCoinList.addAll(historyGoldCoinList);
            }

            //对金币进行运算合并成一条记录
            Map<String, List<UserGoldCoin>> goldCoinMap = goldCoinList.stream()
                .collect(Collectors.groupingBy(UserGoldCoin::getUserId));
            extracted(goldCoinMap, saveUserGoldCoinList);

            // 批量更新金币记录
            updateBatchById2(saveUserGoldCoinList);
            // 批量保存金币交易记录
            saveGoldCoinTradeRecord(userGoldCoins);
            log.info("成功导入" + count + "条金币数据, 耗时{}", System.currentTimeMillis() - beginTime);
            importResultDTO.setMsg("成功导入" + count + "条金币数据");
            importResultDTO.setIsSuccess(true);
        }
        importResultDTO.setMsg(JsonUtil.objToJson(excelCheckMessage.getMessage()));
        return importResultDTO;
    }

    private static void extracted(Map<String, List<UserGoldCoin>> goldCoinMap,
        List<UserGoldCoin> saveUserGoldCoinList) {
        for (Map.Entry<String, List<UserGoldCoin>> entry : goldCoinMap.entrySet()) {
            String userId = entry.getKey();

            UserGoldCoin item = new UserGoldCoin();
            //初始化金币数据：总金币数,可用金币数,可兑换金币
            BigDecimal num = BigDecimal.ZERO, availableNum = BigDecimal.ZERO, convertibleNum = BigDecimal.ZERO;
            List<UserGoldCoin> userGoldCoinList = entry.getValue();
            for (UserGoldCoin goldCoin : userGoldCoinList) {
                num = num.add(goldCoin.getNum());
                availableNum = availableNum.add(goldCoin.getConvertibleNum());
                convertibleNum = convertibleNum.add(goldCoin.getConvertibleNum());
            }
            item.setUserId(userId);
            item.setNum(num.compareTo(BigDecimal.ZERO) > 0 ? num : BigDecimal.ZERO)
                .setRemainNum(availableNum.compareTo(BigDecimal.ZERO) > 0 ? availableNum : BigDecimal.ZERO)
                .setConvertibleNum(convertibleNum.compareTo(BigDecimal.ZERO) > 0 ? convertibleNum : BigDecimal.ZERO);
            saveUserGoldCoinList.add(item);
        }
    }

    /**
     * 保存用户金币交易记录
     *
     * @param userGoldCoinDataImportDTOS
     */
    private void saveGoldCoinTradeRecord(Collection<UserGoldCoinDataImportDTO> userGoldCoinDataImportDTOS) {
        ExcitationTradeRecord tradeRecord;
        for (UserGoldCoinDataImportDTO importDTO : userGoldCoinDataImportDTOS) {
            tradeRecord = new ExcitationTradeRecord().setId(StringUtil.newId())
                .setUserId(importDTO.getUserId())
                .setTargetName(ExcitationTypeEnum.GOLD_COIN.getName())
                .setEventId(OtherEventCategoryEnum.manualImportGoldCoin.name())
                .setOperateNum(importDTO.getOperateNum())
                .setOperateType(importDTO.getOperateType().getValue())
                .setCurrentNum(importDTO.getAvailableNum())
                .setExcitationId(ExcitationTypeEnum.GOLD_COIN.getCode())
                .setTradeType(ExcitationTypeEnum.GOLD_COIN.getCode())
                .setBizType(importDTO.getBizType())
                .setIsExchange(importDTO.getIsExchange());
            String symbol;
            if (new BigDecimal(0).compareTo(importDTO.getOperateNum()) < 0) {
                symbol = ExcitationOperationEnum.INCREASE.getSymbol();
            } else {
                //分数原本带有负号，所以不用加符号
                symbol = "";
            }
            tradeRecord.setSummary("手动导入金币" + symbol + importDTO.getOperateNum());
            excitationTradeRecordMapper.insert(tradeRecord);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void refund(UserGoldCoinRefundDTO refundDTO) {
        log.info(">>>>>>>>>>>>>金币流水退款:{}", JsonUtil.objToJson(refundDTO));

        String billId = refundDTO.getBillId();
        ExcitationTradeRecord tradeRecord = excitationTradeRecordMapper.selectById(billId);
        String userId = tradeRecord.getUserId();
        UserGoldCoin goldCoin = getById(userId);
        String orderId = tradeRecord.getOrderId();
        BigDecimal refundNum = BigDecimal.valueOf(refundDTO.getGoldCoinNum());
        BigDecimal subtractNum = refundNum;

        // TODO：退款之外的操作可以放到退款回调的MQ里面去，需要的参数可以由微信回调带回来
        log.info(">>>>>>>>>>>>>退款,(内部)订单id:{}", orderId);
        Result<String> refundResult = orderFeign.refundOrder(
            new RefundOrderDTO().setOrderId(orderId)
                .setRefundAmount(refundDTO.getAmount())
        );
        String refundOrderNo = refundResult.getData();
        if (StringUtils.isEmpty(refundOrderNo)){
            log.error("<<<<<<<<<<<发起退款失败, {}", refundResult.getMessage());
        }
        log.info("<<<<<<<<<<<<<退款，第三方流水号:{}", refundOrderNo);

        log.info(">>>>>>>>>>>>>扣除用户金币");
        UserGoldCoin updateGoldCoin = new UserGoldCoin().setUserId(userId);
        if (goldCoin.getRemainNum().compareTo(refundNum) < 0){
            log.info("<<<<<<<<<<<<<<<用户剩余金币少于需要扣除金币，金币直接清零");

            updateGoldCoin.setRemainNum(BigDecimal.ZERO)
                .setConvertibleNum(BigDecimal.ZERO);
            subtractNum = goldCoin.getRemainNum();
        }else{
            updateGoldCoin.setRemainNum(goldCoin.getRemainNum().subtract(refundNum))
                .setConvertibleNum(refundNum.compareTo(goldCoin.getConvertibleNum()) > 0 ? BigDecimal.ZERO
                    : goldCoin.getConvertibleNum().subtract(refundNum));
        }
        updateById(updateGoldCoin);
        log.info("<<<<<<<<<<<<<<扣除用户金币");

        log.info(">>>>>>>>>>>>>增加金币扣除记录");
        String refundRecordId = StringUtil.newId();
        excitationTradeRecordMapper.insert(new ExcitationTradeRecord()
                .setId(refundRecordId)
                .setUserId(userId)
                .setTargetId(ExcitationTypeEnum.GOLD_COIN.getCode())
                .setTargetName(ExcitationTypeEnum.GOLD_COIN.getName())
                .setExcitationId(ExcitationTypeEnum.GOLD_COIN.getCode())
                .setOperateType(ExcitationOperationEnum.DECREASE.getValue())
                .setRefundRecordId(billId)
                .setOperateNum(Objects.equals(subtractNum, BigDecimal.ZERO) ? subtractNum : subtractNum.negate())
                .setCurrentNum(updateGoldCoin.getRemainNum())
                .setIsRefundable(GeneralJudgeEnum.NEGATIVE.getValue())
                .setEventId(ExcitationEventEnum.REFUND_ORDER.name())
                .setSummary("订单退款,管理员输入扣除"+refundDTO.getGoldCoinNum() + "金币,实际扣除:"+subtractNum.intValue() +
                    "金币,第三方流水号:" + refundOrderNo)
        );
        log.info("<<<<<<<<<<<<增加金币扣除记录");

        log.info("<<<<<<<<<<<<<<<金币流水退款，第三方流水号:{}", refundOrderNo);
    }

    @Override
    public void exportData(UserGoldCoinPageQuery query) {
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<IUserGoldCoinService, UserGoldCoinPageDTO>(
            query) {

            @Override
            protected IUserGoldCoinService getBean() {
                return SpringUtil.getBean("userGoldCoinService", IUserGoldCoinService.class);
            }

            @Override
            protected PageInfo<UserGoldCoinPageDTO> getPageInfo() {
                return getBean().pageUserGoldCoin(query);
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.UserGoldCoin;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.UserGoldCoin.getType();
            }

            @Override
            protected void afterCreateBeanMap(Map<String, Object> map) {
                Map<String, Object> userInfoDTO = Objects.isNull(map.get("userInfo")) ? null : (Map) map.get("userInfo");
                if(userInfoDTO!=null){
                    map.put("type", Objects.nonNull(userInfoDTO.get("type")) ?
                        UserTypeEnum.getTextByValue((Integer) userInfoDTO.get("type")) : "未知类型用户");
                    map.put("name", userInfoDTO.get("name"));
                    map.put("phone", userInfoDTO.get("phone"));
                    map.put("loginName", userInfoDTO.get("loginName"));
                    map.put("memberOrgName", userInfoDTO.get("memberOrgName"));
                    map.put("orgName", userInfoDTO.get("orgName"));
                }
            }
        };

        exportComponent.exportRecord(exportDataDTO);
    }
}
