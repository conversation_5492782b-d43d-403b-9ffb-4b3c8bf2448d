package com.wunding.learn.excitation.service.service.impl;

import com.wunding.learn.common.constant.user.UserErrorNoEnum;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.enums.redis.UserRedisKeyEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.redis.util.RedisLockUtil;
import com.wunding.learn.common.util.date.DateHelper;
import com.wunding.learn.common.util.date.DateUtil;
import com.wunding.learn.common.util.string.StringUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.excitation.service.mapper.UserClockInRecordMapper;
import com.wunding.learn.excitation.service.model.UserClockInRecord;
import com.wunding.learn.excitation.service.service.IUserClockInRecordService;
import java.util.Date;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 打卡记录表 服务实现类
 *
 * <AUTHOR> href="mailto:"></a>
 * @since 2024-08-29
 */
@Slf4j
@Service("userClockInRecordService")
public class UserClockInRecordServiceImpl extends ServiceImpl<UserClockInRecordMapper, UserClockInRecord> implements
    IUserClockInRecordService {

    @Override
    public void clickClockIn() {
        String userId = UserThreadContext.getUserId();
        Long runDays = 1L;
        String lockName = UserRedisKeyEnum.EVERY_DAY_CLOCK_IN_USERID.getKey() + userId;
        try {
            UserClockInRecord userClockInRecord = lambdaQuery()
                .eq(UserClockInRecord::getUserId, userId)
                .orderByDesc(UserClockInRecord::getCreateTime)
                .orderByDesc(UserClockInRecord::getId)
                .last("limit 1").one();
            if (userClockInRecord != null) {
                //记录最新日期
                String old = DateUtil.formatToStr(userClockInRecord.getCreateTime(), DateHelper.YYYYMMDD);
                //今天日期
                String day = DateUtil.formatToStr(new Date(), DateHelper.YYYYMMDD);
                //昨天日期
                String preDate = DateUtil.formatToStr(DateUtil.getLast(new Date()), DateHelper.YYYYMMDD);
                if (Objects.equals(old, day)) {
                    // 代表手动签到
                    throw new BusinessException(UserErrorNoEnum.ERR_CLOCK_IN_TODAY);
                }
                if (Objects.equals(old, preDate)) {
                    //昨天已签到，累计签到天数
                    runDays = userClockInRecord.getRunDays() + 1;
                }
            }
            // 构建新的签到记录对象
            UserClockInRecord save = new UserClockInRecord();
            save.setId(StringUtil.newId());
            save.setUserId(userId);
            save.setCreateBy(userId);
            save.setCreateTime(new Date());
            save.setUpdateBy(userId);
            save.setUpdateTime(new Date());
            save.setRunDays(runDays);
            save(save);
        } finally {
            RedisLockUtil.release(lockName);
        }
    }

}
