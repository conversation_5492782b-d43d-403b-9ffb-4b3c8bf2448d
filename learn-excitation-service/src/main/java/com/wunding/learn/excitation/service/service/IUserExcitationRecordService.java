package com.wunding.learn.excitation.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.wunding.learn.excitation.api.dto.TargetValueGottenDetailClientDTO;
import com.wunding.learn.excitation.api.query.UserTargetValueQuery;
import com.wunding.learn.excitation.api.dto.UserGottenTargetValueDetailDTO;
import com.wunding.learn.excitation.service.model.UserExcitationRecord;

import java.util.Map;

/**
 * <p> 用户激励分值总记录表 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi</a>
    * @since 2022-08-08
 */
public interface IUserExcitationRecordService extends IService<UserExcitationRecord> {


    Map<String, Integer> getUserHasBeenGotTargetValue(UserTargetValueQuery query);

    PageInfo<UserGottenTargetValueDetailDTO> queryUserGottenTargetValueDetailList(UserTargetValueQuery query);

    PageInfo<TargetValueGottenDetailClientDTO> queryUserGottenTargetValueDetailsClient(UserTargetValueQuery query);
}
