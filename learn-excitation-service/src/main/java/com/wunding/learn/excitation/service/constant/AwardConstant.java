package com.wunding.learn.excitation.service.constant;

/**
 * 奖品相关常量
 *
 * <AUTHOR>
 * @since 2022/10/28
 */
public class AwardConstant {

    private AwardConstant() {
        // Add a private constructor to hide the implicit public one.
    }

    /**
     * 金币兑换奖品订单状态：成功状态(已领取)
     */
    public static final int AWARD_REDEEM_STATUS_SUCCESS = 1;

    /**
     * 金币兑换奖品订单状态：失败状态(已返还)
     */
    public static final int AWARD_REDEEM_STATUS_FIELD = 2;
}
