package com.wunding.learn.excitation.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.wunding.learn.common.constant.excitation.ExcitationErrorNoEnum;
import com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum;
import com.wunding.learn.common.enums.excitation.ExcitationTypeEnum;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.enums.other.SynUpdateCopyDataEventEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.i18n.util.I18nUtil;
import com.wunding.learn.common.mq.dto.ResourceConfigInitDTO;
import com.wunding.learn.common.mq.event.SynUpdateCopyCourseEvent;
import com.wunding.learn.common.mq.service.MqProducer;
import com.wunding.learn.common.mybatis.service.impl.BaseServiceImpl;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.common.util.string.TranslateUtil;
import com.wunding.learn.excitation.service.admin.dto.ExcitationConfigUserPageDTO;
import com.wunding.learn.excitation.service.admin.dto.ExcitationConfigUserSaveDTO;
import com.wunding.learn.excitation.service.admin.query.ExcitationConfigUserQuery;
import com.wunding.learn.excitation.service.dao.ExcitationConfigUserDao;
import com.wunding.learn.excitation.service.mapper.ExcitationConfigGlobalMapper;
import com.wunding.learn.excitation.service.mapper.ExcitationConfigUserMapper;
import com.wunding.learn.excitation.service.mapper.ExcitationEventMapper;
import com.wunding.learn.excitation.service.model.ExcitationConfigGlobal;
import com.wunding.learn.excitation.service.model.ExcitationConfigUser;
import com.wunding.learn.excitation.service.model.ExcitationEvent;
import com.wunding.learn.excitation.service.service.IExcitationConfigUserService;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * <p> 用户激励配置表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi</a>
 * @since 2022-08-08
 */
@Slf4j
@Service("excitationConfigUserService")
public class ExcitationConfigUserServiceImpl extends
    BaseServiceImpl<ExcitationConfigUserMapper, ExcitationConfigUser> implements IExcitationConfigUserService {

    @Resource
    private ExcitationEventMapper excitationEventMapper;

    @Resource
    private ExcitationConfigGlobalMapper excitationConfigGlobalMapper;

    @Resource
    private MqProducer mqProducer;
    @Resource(name = "excitationConfigUserDao")
    private ExcitationConfigUserDao excitationConfigUserDao;

    @Override
    public List<ExcitationConfigUserPageDTO> getDataList(ExcitationConfigUserQuery query) {
        List<ExcitationConfigUserPageDTO> targetDataList = new ArrayList<>();

        List<ExcitationConfigUser> originalDataList = list(new LambdaQueryWrapper<ExcitationConfigUser>()
            .eq(ExcitationConfigUser::getTargetId, query.getTargetId())
            .eq(Objects.nonNull(query.getSystemType()), ExcitationConfigUser::getSystemType, query.getSystemType())
            .orderByDesc(ExcitationConfigUser::getCreateTime)
        );

        if (CollectionUtils.isEmpty(originalDataList)) {
            return targetDataList;
        }

        // 获取激励事件相关信息
        Set<String> eventIdSet = originalDataList.stream().map(ExcitationConfigUser::getEventId)
            .collect(Collectors.toSet());
        List<ExcitationEvent> eventList = excitationEventMapper
            .selectList(new LambdaQueryWrapper<ExcitationEvent>().in(ExcitationEvent::getId, eventIdSet));
        Map<String, ExcitationEvent> eventMap = eventList.stream()
            .collect(Collectors.toMap(ExcitationEvent::getId, Function.identity()));

        targetDataList = originalDataList.stream().map(data -> {
            ExcitationConfigUserPageDTO dto = new ExcitationConfigUserPageDTO();
            BeanUtils.copyProperties(data, dto);

            dto.setCategory(data.getTypeId());
            dto.setExcitationName(ExcitationTypeEnum.getNameByCode(data.getTypeId()));

            ExcitationEvent event = eventMap.get(data.getEventId());
            if (Objects.isNull(event)) {
                return dto;
            }
            dto.setIntro(I18nUtil.getDefaultMessage(event.getIntro()));
            dto.setEventName(I18nUtil.getDefaultMessage(event.getName()));
            dto.setEventType(event.getType());
            return dto;
        }).collect(Collectors.toList());

        return targetDataList;
    }

    @Override
    public void saveData(ExcitationConfigUserSaveDTO saveDTO) {
        ExcitationConfigUser configUser = new ExcitationConfigUser();
        BeanUtils.copyProperties(saveDTO, configUser);
        configUser.setId(StringUtil.newId());
        configUser.setExcitationCategory(saveDTO.getCategory());
        ExcitationEvent excitationEvent = excitationEventMapper.selectById(saveDTO.getEventId());
        String eventName = Optional.ofNullable(excitationEvent).isPresent() ? excitationEvent.getName() : "";
        excitationConfigUserDao.saveExcitationConfigUser(configUser, eventName);

        // 课程相关数据变动，同步变动课程数据
        SynUpdateCopyCourseEvent synUpdateCopyCourseEvent = new SynUpdateCopyCourseEvent(configUser.getId(),
            SynUpdateCopyDataEventEnum.EXCITATION.getKey());
        log.info("用户配置激励规则变动，发送MQ:{}", JsonUtil.objToJson(synUpdateCopyCourseEvent));
        mqProducer.sendMsg(synUpdateCopyCourseEvent);
    }

    @Override
    public void modifyData(String id, ExcitationConfigUserSaveDTO saveDTO) {

        ExcitationConfigUser updateData = new ExcitationConfigUser();
        BeanUtils.copyProperties(saveDTO, updateData);
        updateData.setId(id);

        // 获取激励事务信息，用于日志记录
        ExcitationEvent excitationEvent = excitationEventMapper.selectById(updateData.getEventId());
        String eventName = Optional.ofNullable(excitationEvent).isPresent() ? excitationEvent.getName() : "";
        excitationConfigUserDao.updateExcitationConfigUser(updateData, eventName);

        // 课程相关数据变动，同步变动复制的课程数据
        SynUpdateCopyCourseEvent synUpdateCopyCourseEvent = new SynUpdateCopyCourseEvent(id,
            SynUpdateCopyDataEventEnum.EXCITATION.getKey());
        log.info("用户配置激励规则变动，发送MQ:{}", JsonUtil.objToJson(synUpdateCopyCourseEvent));
        mqProducer.sendMsg(synUpdateCopyCourseEvent);
    }

    @Override
    public void deleteData(String ids) {
        List<String> idList = TranslateUtil.translateBySplit(ids, String.class);
        // 检查是否有锁定数据
        List<ExcitationConfigUser> excitationConfigUserList = listByIds(idList);
        for (ExcitationConfigUser configUser : excitationConfigUserList) {
            if (Objects.equals(configUser.getIsLock(), GeneralJudgeEnum.CONFIRM.getValue())) {
                throw new BusinessException(ExcitationErrorNoEnum.ERR_EXCITATION_CONFIG_BEEN_LOCKED);
            }
        }
        removeBatchByIds2(idList);
    }

    @Override
    public void deleteDataById(String id) {
        // 检查是否有锁定数据
        ExcitationConfigUser excitationConfigUser = getById(id);
        if (Optional.ofNullable(excitationConfigUser).isEmpty()) {
            return;
        }
        if (Objects.equals(excitationConfigUser.getIsLock(), GeneralJudgeEnum.CONFIRM.getValue())) {
            throw new BusinessException(ExcitationErrorNoEnum.ERR_EXCITATION_CONFIG_BEEN_LOCKED);
        }
        //删除并记录日志
        ExcitationEvent excitationEvent = excitationEventMapper.selectById(excitationConfigUser.getEventId());
        String eventName = Optional.ofNullable(excitationEvent).isPresent() ? excitationEvent.getName() : "";
        excitationConfigUserDao.delExcitationConfigUser(excitationConfigUser, eventName);

        // 课程相关数据变动，同步变动课程数据
        mqProducer.sendMsg(new SynUpdateCopyCourseEvent(id, SynUpdateCopyDataEventEnum.EXCITATION.getKey()));
    }

    @Override
    public void initResourceConfigUser(ResourceConfigInitDTO initDTO) {

        // 激励事件分类
        Set<String> eventCategoryList = new HashSet<>();
        eventCategoryList.add(initDTO.getResourceType());

        // 课程分类事件要与课件分类事件一起查询
        if (ExcitationEventCategoryEnum.COURSE.getCode().equals(initDTO.getResourceType())) {
            eventCategoryList.add(ExcitationEventCategoryEnum.COURSE_WARE.getCode());
        }

        // 获取对应的事件(未删除的)
        List<ExcitationEvent> eventList = excitationEventMapper.selectList(
            new LambdaQueryWrapper<ExcitationEvent>().in(ExcitationEvent::getCategory, eventCategoryList));
        if (CollectionUtils.isEmpty(eventList)) {
            return;
        }

        // 校验当前资源是否已经初始化
        List<ExcitationConfigUser> excitationConfigUsers = list(new LambdaQueryWrapper<ExcitationConfigUser>()
            .eq(ExcitationConfigUser::getTargetId, initDTO.getResourceId())
            .in(ExcitationConfigUser::getExcitationCategory, eventCategoryList));
        if (CollectionUtils.isNotEmpty(excitationConfigUsers)) {
            // 该资源已经初始化，无需继续初始化
            return;
        }

        // 获取全局配置中已启用的配置
        Set<String> eventIdSet = eventList.stream().map(ExcitationEvent::getId).collect(Collectors.toSet());
        List<ExcitationConfigGlobal> configGlobalList = excitationConfigGlobalMapper
            .selectList(new LambdaQueryWrapper<ExcitationConfigGlobal>()
                .in(ExcitationConfigGlobal::getEventId, eventIdSet)
                .eq(ExcitationConfigGlobal::getIsAvailable, GeneralJudgeEnum.CONFIRM.getValue()));
        if (CollectionUtils.isEmpty(configGlobalList)) {
            return;
        }

        // 初始化数据
        Set<ExcitationConfigUser> excitationConfigUserSet = new HashSet<>(configGlobalList.size());
        configGlobalList
            .forEach(configGlobal -> excitationConfigUserSet.add(createConfig(configGlobal, initDTO)));
        saveBatch2(excitationConfigUserSet);
    }

    /**
     * 创建用户配置激励规则
     */
    private ExcitationConfigUser createConfig(ExcitationConfigGlobal configGlobal, ResourceConfigInitDTO initDTO) {
        ExcitationConfigUser configUser = new ExcitationConfigUser();
        BeanUtils.copyProperties(configGlobal, configUser);
        configUser.setId(StringUtil.newId())
            .setExcitationCategory(initDTO.getResourceType())
            .setTargetId(initDTO.getResourceId())
            .setGlobalId(configGlobal.getId());
        return configUser;
    }

    @Override
    public void exchangeData(String id, Integer isExchange) {
        ExcitationConfigUser updateData = new ExcitationConfigUser();
        updateData.setId(id);
        updateData.setIsExchange(isExchange);
        ExcitationConfigUser excitationConfigUser = getById(id);
        if (Optional.ofNullable(excitationConfigUser).isEmpty()) {
            return;
        }
        ExcitationEvent excitationEvent = excitationEventMapper.selectById(excitationConfigUser.getEventId());
        String eventName = Optional.ofNullable(excitationEvent).isPresent() ? excitationEvent.getName() : "";
        excitationConfigUserDao.updateExcitationConfigUser(updateData, eventName);
    }
}
