package com.wunding.learn.excitation.service.factory;

import com.wunding.learn.common.constant.excitation.ExcitationErrorNoEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.mq.dto.ExcitationTradeDTO;
import com.wunding.learn.common.util.bean.SpringUtil;
import java.util.Objects;

/**
 * 激励减少策略类
 *
 * <AUTHOR>
 * @date 2022/11/26
 */
public class ExcitationSubtractContext {

    private IOperationSubtract subtract;

    private ExcitationTradeDTO excitationTradeDTO;

    public ExcitationSubtractContext(ExcitationTradeDTO excitationTradeDTO) {
        this.excitationTradeDTO = excitationTradeDTO;
        switch (Objects.requireNonNull(excitationTradeDTO.getExcitationTypeEnum())) {
            case INTEGRAL:
                subtract = SpringUtil.getBean(IntegralOperationSubtract.class);
                break;
            case CREDIT:
                subtract = SpringUtil.getBean(CreditOperationSubtract.class);
                break;
            case LEARN_TIME:
                subtract = SpringUtil.getBean(LearnTimeOperationSubtract.class);
                break;
            case GOLD_COIN:
                subtract = SpringUtil.getBean(GoldCoinOperationSubtract.class);
                break;
            default:
                throw new BusinessException(ExcitationErrorNoEnum.ERR_EXCITATION_TYPE);
        }
    }

    /**
     * 执行体
     */
    public void execute() {
        subtract.compute(excitationTradeDTO);
    }
}
