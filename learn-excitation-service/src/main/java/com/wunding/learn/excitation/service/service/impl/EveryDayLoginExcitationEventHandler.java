package com.wunding.learn.excitation.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.wunding.learn.common.constant.excitation.ExcitationEventConstant;
import com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum;
import com.wunding.learn.common.enums.excitation.ExcitationTypeEnum;
import com.wunding.learn.common.enums.other.ExcitationOperationEnum;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.mq.event.excitation.SystemLoginExcitationEvent;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.excitation.service.bo.ExcitationEventBO;
import com.wunding.learn.excitation.service.mapper.ExcitationConfigGlobalMapper;
import com.wunding.learn.excitation.service.mapper.ExcitationEventMapper;
import com.wunding.learn.excitation.service.mapper.ExcitationTradeRecordMapper;
import com.wunding.learn.excitation.service.mapper.UserCreditMapper;
import com.wunding.learn.excitation.service.mapper.UserExcitationRecordMapper;
import com.wunding.learn.excitation.service.mapper.UserExperienceMapper;
import com.wunding.learn.excitation.service.mapper.UserGoldCoinMapper;
import com.wunding.learn.excitation.service.mapper.UserIntegralMapper;
import com.wunding.learn.excitation.service.mapper.UserLearnTimeMapper;
import com.wunding.learn.excitation.service.model.ExcitationConfigGlobal;
import com.wunding.learn.excitation.service.model.ExcitationTradeRecord;
import com.wunding.learn.excitation.service.model.UserCredit;
import com.wunding.learn.excitation.service.model.UserExcitationRecord;
import com.wunding.learn.excitation.service.model.UserExperience;
import com.wunding.learn.excitation.service.model.UserGoldCoin;
import com.wunding.learn.excitation.service.model.UserIntegral;
import com.wunding.learn.excitation.service.model.UserLearnTime;
import com.wunding.learn.excitation.service.query.ExcitationEventBOQuery;
import com.wunding.learn.excitation.service.service.SystemLoginExcitationEventHandleService;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2022/11/1
 */
@Service(ExcitationEventConstant.EVERY_DAY_LOGIN + ExcitationEventConstant.BEAN_SUFFIX)
public class EveryDayLoginExcitationEventHandler implements SystemLoginExcitationEventHandleService {

    @Resource
    private ExcitationEventMapper excitationEventMapper;

    @Resource
    private ExcitationConfigGlobalMapper excitationConfigGlobalMapper;
    @Resource
    private UserExcitationRecordMapper userExcitationRecordMapper;
    @Resource
    private UserExperienceMapper userExperienceMapper;
    @Resource
    private ExcitationTradeRecordMapper excitationTradeRecordMapper;
    @Resource
    private UserCreditMapper userCreditMapper;
    @Resource
    private UserGoldCoinMapper userGoldCoinMapper;
    @Resource
    private UserIntegralMapper userIntegralMapper;
    @Resource
    private UserLearnTimeMapper userLearnTimeMapper;

    @Override
    public void handleExcitationEvent(SystemLoginExcitationEvent event) {
        // 获取本事件详情
        ExcitationEventBO excitationEvent = excitationEventMapper.queryEventBO(
            new ExcitationEventBOQuery().setEventId(event.getEventId()));
        if (Objects.isNull(excitationEvent)) {
            // 事件不存在
            return;
        }

        // 获取配置的同类型事件
        boolean enableUserConfig = ExcitationEventCategoryEnum.isEnableUserConfig(excitationEvent.getCategory());

        List<ExcitationConfigGlobal> configGlobalList = new ArrayList<>();
        // 允许配置用户自定义配置
        if (enableUserConfig) {
            // 查找自定义配置 【此处有一些无法执行的代码，已进行删除，查看代码请恢复历史版本】
        } else {
            // 查找全局配置(按照时间倒序)
            configGlobalList = excitationConfigGlobalMapper
                .selectList(new LambdaQueryWrapper<ExcitationConfigGlobal>()
                    .eq(ExcitationConfigGlobal::getEventId, event.getEventId())
                    .eq(ExcitationConfigGlobal::getIsAvailable, GeneralJudgeEnum.CONFIRM.getValue())
                    .orderByDesc(ExcitationConfigGlobal::getCreateTime)
                );
        }

        if (CollectionUtils.isEmpty(configGlobalList)) {
            // 无对应的激励配置，直接结束
            return;
        }

        // 开始执行激励配置
        configGlobalList.stream().forEach(config -> executeExcitationConfig(config, event, excitationEvent));
    }

    /**
     * 执行 激励配置
     */
    public void executeExcitationConfig(ExcitationConfigGlobal config, SystemLoginExcitationEvent event,
        ExcitationEventBO excitationEvent) {
        // 系统中配置的每日同类型事件获取上限
        Long bound = excitationEvent.getBound();

        //  pre 鉴别是否已经达到上限，到上限直接结束 1.是否允许配置上限 2.配置的上限是否是0，如果是0则还没有进行配置，等同于无上限
        if (Objects.equals(excitationEvent.getIsEnableConfig(), GeneralJudgeEnum.CONFIRM.getValue())
            && bound.compareTo(0L) > 0) {
            // 允许配置上限 且已经配置了上限

        }

        // 开始执行
        executeBody(config, excitationEvent.getCategory(), event.getUserId());
    }

    /**
     * 执行体
     *
     * @param config 激励配置
     */
    public void executeBody(ExcitationConfigGlobal config, String excitationEventCategory, String userId) {
        // 操作后的激励数据量
        BigDecimal currentNum;
        // 加入user_excitation_record 用户激励分值总记录表
        UserExcitationRecord userExcitationRecord = new UserExcitationRecord()
            .setId(StringUtil.newId())
            .setUserId(userId)
            .setExcitationCategory(excitationEventCategory)
            .setExcitationRecordId(config.getId())
            .setExcitationTypeId(config.getTypeId())
            .setOperateNum(config.getExcitationNum())
            .setOperateType(ExcitationOperationEnum.INCREASE.getValue());

        // 所有的激励都可以获得同等经验
        UserExperience userExperience = userExperienceMapper.selectById(userId);
        if (Objects.isNull(userExperience)) {
            UserExperience experienceSave = new UserExperience().setUserId(userId).setNum(config.getExcitationNum())
                .setOriginNum(BigDecimal.ZERO).setRemainNum(config.getExcitationNum());
            userExperienceMapper.insert(experienceSave);
        } else {
            UserExperience experienceUpdate = userExperience
                .setNum(userExperience.getNum().add(config.getExcitationNum()))
                .setRemainNum(userExperience.getRemainNum().add(config.getExcitationNum()));
            userExperienceMapper.updateById(experienceUpdate);
        }

        // 加入: 用户积分/学分/学时/金币
        switch (ExcitationTypeEnum.get(config.getTypeId())) {
            case INTEGRAL:
                // 进行积分计算,返回剩余积分
                currentNum = computeIntegral(config, userId);
                break;
            case GOLD_COIN:
                // 进行金币计算,返回剩余金币
                currentNum = computeGoldCoin(config, userId);
                break;
            case CREDIT:
                // 进行学分计算,返回学分
                currentNum = computeCredit(config, userId);
                break;
            case LEARN_TIME:
                // 进行学时计算,返回学时
                currentNum = computeLearnTime(config, userId);
                break;
            default:
                currentNum = new BigDecimal(-1);
        }

        userExcitationRecord.setCurrentNum(currentNum);
        userExcitationRecordMapper.insert(userExcitationRecord);

        // 加入excitation_trade_record 激励交易记录表
        if (config.getExcitationNum().compareTo(BigDecimal.ZERO) > 0 && ExcitationTypeEnum
            .isTradeExcitationType(config.getTypeId())) {
            ExcitationTradeRecord excitationTradeRecord = new ExcitationTradeRecord().setId(StringUtil.newId())
                .setExcitationId(config.getTypeId())
                .setCurrentNum(currentNum)
                .setEventId(config.getEventId())
                .setOperateNum(config.getExcitationNum())
                .setOperateType(ExcitationOperationEnum.INCREASE.getValue())
//                .setTargetId()
                .setTradeType(excitationEventCategory)
                .setSummary(StringUtils.EMPTY);
            excitationTradeRecordMapper.insert(excitationTradeRecord);
        }
    }

    // 增加积分
    public BigDecimal computeIntegral(ExcitationConfigGlobal config, String userId) {
        // 查找积分数据
        UserIntegral dbData = userIntegralMapper.selectById(userId);
        if (Objects.isNull(dbData)) {
            // 新增并返回
            UserIntegral data = new UserIntegral().setUserId(userId)
                .setOriginNum(BigDecimal.ZERO)
                .setNum(config.getExcitationNum())
                .setAvailableNum(config.getExcitationNum());
            userIntegralMapper.insert(data);
            return data.getAvailableNum();
        }
        // 修改积分数据
        UserIntegral data = new UserIntegral().setUserId(userId)
            .setNum(dbData.getNum().add(config.getExcitationNum()))
            .setAvailableNum(dbData.getAvailableNum().add(config.getExcitationNum()));
        userIntegralMapper.updateById(data);
        return data.getAvailableNum();
    }

    // 增加金币
    public BigDecimal computeGoldCoin(ExcitationConfigGlobal config, String userId) {
        // 查找积分数据
        UserGoldCoin dbUserData = userGoldCoinMapper.selectById(userId);
        if (Objects.isNull(dbUserData)) {
            // 新增并返回
            UserGoldCoin data = new UserGoldCoin().setUserId(userId)
                .setOriginNum(BigDecimal.ZERO)
                .setNum(config.getExcitationNum())
                .setRemainNum(config.getExcitationNum());
            userGoldCoinMapper.insert(data);
            return data.getRemainNum();
        }
        // 修改积分数据
        UserGoldCoin data = new UserGoldCoin().setUserId(userId)
            .setNum(dbUserData.getNum().add(config.getExcitationNum()))
            .setRemainNum(dbUserData.getRemainNum().add(config.getExcitationNum()));
        userGoldCoinMapper.updateById(data);
        return data.getRemainNum();
    }

    // 增加学时
    public BigDecimal computeCredit(ExcitationConfigGlobal config, String userId) {
        // 查找积分数据
        UserCredit dbData = userCreditMapper.selectById(userId);
        if (Objects.isNull(dbData)) {
            // 新增并返回
            UserCredit data = new UserCredit().setUserId(userId)
                .setOriginNum(BigDecimal.ZERO)
                .setNum(config.getExcitationNum())
                .setRemainNum(config.getExcitationNum());
            userCreditMapper.insert(data);
            return data.getRemainNum();
        }
        // 修改积分数据
        UserCredit data = new UserCredit().setUserId(userId)
            .setNum(dbData.getNum().add(config.getExcitationNum()))
            .setRemainNum(dbData.getRemainNum().add(config.getExcitationNum()));
        userCreditMapper.updateById(data);
        return data.getRemainNum();
    }

    // 增加学分
    public BigDecimal computeLearnTime(ExcitationConfigGlobal config, String userId) {
        // 查找积分数据
        UserLearnTime dbData = userLearnTimeMapper.selectById(userId);
        if (Objects.isNull(dbData)) {
            // 新增并返回
            UserLearnTime data = new UserLearnTime().setUserId(userId)
                .setOriginNum(BigDecimal.ZERO)
                .setNum(config.getExcitationNum())
                .setAvailableNum(config.getExcitationNum());
            userLearnTimeMapper.insert(data);
            return data.getAvailableNum();
        }
        // 修改积分数据
        UserLearnTime data = new UserLearnTime().setUserId(userId)
            .setNum(dbData.getNum().add(config.getExcitationNum()))
            .setAvailableNum(dbData.getAvailableNum().add(config.getExcitationNum()));
        userLearnTimeMapper.updateById(data);
        return data.getAvailableNum();
    }
}
