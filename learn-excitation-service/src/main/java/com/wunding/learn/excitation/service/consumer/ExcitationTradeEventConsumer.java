package com.wunding.learn.excitation.service.consumer;

import com.rabbitmq.client.Channel;
import com.wunding.learn.common.constant.mq.MqConst;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.mq.dto.ExcitationTradeDTO;
import com.wunding.learn.common.mq.event.excitation.ExcitationTradeEvent;
import com.wunding.learn.common.mq.util.ConsumerAckUtil;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.excitation.service.factory.ExcitationAddContext;
import com.wunding.learn.excitation.service.factory.ExcitationSubtractContext;
import java.io.IOException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

/**
 * 激励交易事件消费者
 *
 * <AUTHOR>
 * @date 2022/11/24
 */
@Component
@Slf4j
public class ExcitationTradeEventConsumer {

    /**
     * 交易激励事件消费者队列名称
     */
    private static final String EXCITATION_TRADE_EVENT_CONSUMER_QUEUE = "excitationTradeEventConsumerQueue";

    @RabbitListener(
        bindings = @QueueBinding(
            value = @Queue(value = EXCITATION_TRADE_EVENT_CONSUMER_QUEUE),
            exchange = @Exchange(value = MqConst.EXCHANGE_TOPIC, type = ExchangeTypes.TOPIC),
            key = MqConst.EXCITATION_TRADE_EVENT_ROUTING_KEY
        ),
        id = "excitationTradeEventHandler"
    )
    public void excitationTradeEventHandler(@Payload ExcitationTradeEvent tradeEvent,
        @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag, Channel channel) throws IOException {

        log.info("excitationTradeEventHandler excitationTradeEvent: {}", JsonUtil.objToJson(tradeEvent));
        UserThreadContext.setTenantId(tradeEvent.getTenantId());

        execute(tradeEvent.getExcitationTradeDTO());
        UserThreadContext.remove();
        ConsumerAckUtil.basicAck(tradeEvent,channel,deliveryTag, false);
    }

    /**
     * 交易执行
     *
     * @param dto
     */
    public void execute(ExcitationTradeDTO dto) {
        // 增加积分
        new ExcitationAddContext(dto).execute();
        // 减少积分
        new ExcitationSubtractContext(dto).execute();
    }
}
