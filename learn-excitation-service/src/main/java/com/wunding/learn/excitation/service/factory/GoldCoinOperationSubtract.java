package com.wunding.learn.excitation.service.factory;

import com.wunding.learn.common.enums.excitation.ExcitationTypeEnum;
import com.wunding.learn.common.enums.other.ExcitationOperationEnum;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.enums.other.SystemConfigCodeEnum;
import com.wunding.learn.common.mq.dto.ExcitationTradeDTO;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.excitation.service.model.ExcitationTradeRecord;
import com.wunding.learn.excitation.service.service.IExcitationTradeRecordService;
import com.wunding.learn.excitation.service.service.impl.UserGoldCoinServiceImpl;
import com.wunding.learn.user.api.dto.UserDTO;
import com.wunding.learn.user.api.service.ParaFeign;
import com.wunding.learn.user.api.service.UserFeign;
import java.math.BigDecimal;
import java.util.Objects;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since  2022/11/25
 */
@Component
public class GoldCoinOperationSubtract implements IOperationSubtract {

    @Resource
    private UserGoldCoinServiceImpl goldCoinService;
    @Resource
    private IExcitationTradeRecordService tradeRecordService;
    @Resource
    private UserFeign userFeign;
    @Resource
    private ParaFeign paraFeign;

    @Override
    public void compute(ExcitationTradeDTO dto) {
        // 交易数量
        BigDecimal amount = dto.getAmount();
        // 收受人ID
        String payeeId = dto.getPayeeId();
        // 支付人ID
        String payerId = dto.getPayerId();
        // 减少积分
        BigDecimal availableAmount = goldCoinService.subtract(payerId, amount);
        // 增加交易记录
        ExcitationTradeRecord tradeRecord = new ExcitationTradeRecord().setId(StringUtil.newId())
            .setOperateNum(amount)
            .setCurrentNum(availableAmount)
            .setOperateType(ExcitationOperationEnum.DECREASE.getValue())
            .setTargetId(payeeId)
            .setUserId(payerId)
            .setEventId(dto.getEventId())
            .setExcitationId(dto.getExcitationTypeEnum().getCode())
            .setSummary(dto.getSummary() + ExcitationOperationEnum.DECREASE.getSymbol() + amount
                + ExcitationTypeEnum.getNameByCode(dto.getExcitationTypeEnum().getCode()));
            //根据系统参数设置,是否是关闭可兑换区分配置 SYSTEM_CONFIG_CODE_912
            Integer isClose = Integer
                .valueOf(paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_912.getCode()));
            //SYSTEM_CONFIG_CODE_912 为关闭可兑换区分，强制修改激励都为不可兑换
            if (Objects.equals(isClose, GeneralJudgeEnum.CONFIRM.getValue())) {
                tradeRecord.setIsExchange(GeneralJudgeEnum.NEGATIVE.getValue());
            }else{
                //交易的记录应该都是是否可兑换为1才行
                tradeRecord.setIsExchange(GeneralJudgeEnum.CONFIRM.getValue());
            }
        UserDTO payee = userFeign.getUserById(payeeId);
        if (Objects.nonNull(payee)) {
            tradeRecord.setTargetName(payee.getFullName());
        }
        tradeRecordService.save(tradeRecord);
    }

}
