package com.wunding.learn.excitation.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.excitation.service.mapper.UserIntegralRecordMapper;
import com.wunding.learn.excitation.service.model.UserIntegralRecord;
import com.wunding.learn.excitation.service.service.IUserIntegralRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p> 积分记录表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi</a>
    * @since 2022-08-08
 */
@Slf4j
@Service("userIntegralRecordService")
public class UserIntegralRecordServiceImpl extends ServiceImpl<UserIntegralRecordMapper, UserIntegralRecord> implements IUserIntegralRecordService {

}
