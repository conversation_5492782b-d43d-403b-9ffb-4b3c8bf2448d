package com.wunding.learn.excitation.service.consumer;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.rabbitmq.client.Channel;
import com.wunding.learn.common.constant.mq.MqConst;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum;
import com.wunding.learn.common.mq.dto.ExcitationGlobalSyncMqEventDTO;
import com.wunding.learn.common.mq.event.excitation.ExcitationGlobalSyncEvent;
import com.wunding.learn.common.mq.util.ConsumerAckUtil;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.course.api.service.CourseFeign;
import com.wunding.learn.evaluation.api.service.EvaluationFeign;
import com.wunding.learn.exam.api.service.ExamFeign;
import com.wunding.learn.example.api.service.ExampleFeign;
import com.wunding.learn.excitation.service.model.ExcitationConfigGlobal;
import com.wunding.learn.excitation.service.model.ExcitationConfigUser;
import com.wunding.learn.excitation.service.service.IExcitationConfigGlobalService;
import com.wunding.learn.excitation.service.service.IExcitationConfigUserService;
import com.wunding.learn.forum.api.service.ForumFeign;
import com.wunding.learn.info.api.service.InfoFeign;
import com.wunding.learn.live.api.service.LiveFeign;
import com.wunding.learn.market.api.service.SignFeign;
import com.wunding.learn.project.api.service.ProjectFeign;
import com.wunding.learn.survey.api.service.SurveyFeign;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.BeanUtils;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

/**
 * <p>全局激励同步消费事件</p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">李恒</a>
 * @since 2024-04-06 17:25:11
 */
@Component
@Slf4j
public class ExcitationGlobalSyncEventConsumer {

    /**
     * 消息队列
     */
    public static final String EXCITATION_GLOBAL_SYNC_CONSUMER_QUEUE = "excitationGlobalSyncEventConsumerQueue";

    @Resource
    private IExcitationConfigGlobalService excitationConfigGlobalService;

    @Resource
    private IExcitationConfigUserService excitationConfigUserService;

    @Resource
    private CourseFeign courseFeign;

    @Resource
    private ExamFeign examFeign;

    @Resource
    private ExampleFeign exampleFeign;

    @Resource
    private LiveFeign liveFeign;

    @Resource
    private SurveyFeign surveyFeign;

    @Resource
    private InfoFeign infoFeign;

    @Resource
    private EvaluationFeign evaluationFeign;

    @Resource
    private ForumFeign forumFeign;

    @Resource
    private SignFeign signFeign;

    @Resource
    private ProjectFeign projectFeign;

    /**
     * 执行全局激励消费事件
     *
     * @param globalSyncEvent 全局激励消费事件
     * @param deliveryTag     MQ消息投递序号
     * @param channel         MQ信道
     * @throws IOException 异常信息
     */
    @RabbitListener(
        bindings = @QueueBinding(
            value = @Queue(value = EXCITATION_GLOBAL_SYNC_CONSUMER_QUEUE),
            exchange = @Exchange(value = MqConst.EXCHANGE_TOPIC, type = ExchangeTypes.TOPIC),
            key = MqConst.GLOBAL_EXCITATION_SYNC_ROUTING_KEY
        ),
        id = "excitationGlobalSyncEventHandler"
    )
    public void excitationGlobalSyncEventHandler(
        @Payload ExcitationGlobalSyncEvent globalSyncEvent,
        @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag, Channel channel) throws IOException {

        log.info("全局激励同步消费 excitationGlobalSyncEvent: {}", JsonUtil.objToJson(globalSyncEvent));

        try {

            StopWatch stopWatch = new StopWatch();
            stopWatch.start();

            UserThreadContext.setTenantId(globalSyncEvent.getTenantId());

            ExcitationGlobalSyncMqEventDTO globalSyncMqEventDTO = globalSyncEvent.getExcitationGlobalSyncMqEventDTO();
            String category = globalSyncMqEventDTO.getCategory();
            String categoryName = ExcitationEventCategoryEnum.getNameByCode(category);
            String eventName = globalSyncMqEventDTO.getEventName();

            List<String> effectiveIds = new ArrayList<>();

            // 获取待同步资源ID列表
            switch (Objects.requireNonNull(ExcitationEventCategoryEnum.get(category))) {
                case COURSE:
                    effectiveIds = courseFeign.getEffectiveCourseIds();
                    break;
                case COURSE_WARE:
                    effectiveIds = courseFeign.getEffectiveCourseIds();
                    break;
                case EXAM:
                    effectiveIds = examFeign.getEffectiveExamIds(1);
                    break;
                case EXERCISE:
                    effectiveIds = examFeign.getEffectiveExamIds(2);
                    break;
                case EXAMPLE:
                    effectiveIds = exampleFeign.getEffectiveExampleIds();
                    break;
                case LIVE:
                    effectiveIds = liveFeign.getEffectiveLiveIds();
                    break;
                case SURVEY:
                    effectiveIds = surveyFeign.getEffectiveSurveyIds();
                    break;
                case PROJECT:
                    effectiveIds = projectFeign.getEffectiveProjectIds();
                    break;
                case NEWS:
                    effectiveIds = infoFeign.getEffectiveInfoIds();
                    break;
                case EVALUATION:
                    effectiveIds = evaluationFeign.getEffectiveEvaluationIds();
                    break;
                case TOPIC:
                    effectiveIds = forumFeign.getEffectiveForumSectionIds();
                    break;
                case SIGN:
                    effectiveIds = signFeign.getEffectiveSignIds();
                    break;
                default:
                    log.error("全局激励同步发生异常：【{}】【{}】事件类型不支持全局同步", categoryName, eventName);
                    break;
            }

            // 装配更新数据列表
            List<ExcitationConfigUser> configUserList = new ArrayList<>();
            effectiveIds.forEach(targetId -> fabricatedGlobalSyncList(globalSyncMqEventDTO, targetId, configUserList));

            // 执行批量添加或更新
            if (!configUserList.isEmpty()) {
                excitationConfigUserService.saveOrUpdateBatch(configUserList);
            }

            // 修改全局配置状态为不是正在同步中
            LambdaUpdateWrapper<ExcitationConfigGlobal> updateWrapper = new UpdateWrapper<ExcitationConfigGlobal>()
                .lambda()
                .set(ExcitationConfigGlobal::getIsSyncing, 0)
                .eq(ExcitationConfigGlobal::getId, globalSyncMqEventDTO.getId());
            excitationConfigGlobalService.update(updateWrapper);

            stopWatch.stop();
            log.info("全局激励同步消费 同步类型: {}-{} 同步事件：{} 数量总量：{} 用时：{}秒",
                categoryName, category, eventName, configUserList.size(), stopWatch.getTotalTimeSeconds());
        } catch (Exception e) {
            log.error("全局激励同步消费失败", e);
        } finally {
            UserThreadContext.remove();
            ConsumerAckUtil.basicAck(globalSyncEvent, channel, deliveryTag, false);
        }
    }

    /**
     * 装配资源激励事件列表
     *
     * @param globalSyncMqEventDTO 全局激励数据源
     * @param targetId             目标资源id
     * @param configUserList       待执行数据列表（装配结果）
     */
    private void fabricatedGlobalSyncList(ExcitationGlobalSyncMqEventDTO globalSyncMqEventDTO, String targetId,
        List<ExcitationConfigUser> configUserList) {

        // 数据封装
        ExcitationConfigUser excitationConfigUser = new ExcitationConfigUser();
        BeanUtils.copyProperties(globalSyncMqEventDTO, excitationConfigUser);
        excitationConfigUser.setExcitationCategory(globalSyncMqEventDTO.getCategory());
        excitationConfigUser.setGlobalId(globalSyncMqEventDTO.getId());
        excitationConfigUser.setTargetId(targetId);

        // 修复课件激励的分类 - 与现有程序兼容，统一改为课程分类 - 解决全局课件激励事件同步后不生效的问题
        if (ExcitationEventCategoryEnum.COURSE_WARE.getCode().equals(globalSyncMqEventDTO.getCategory())) {
            excitationConfigUser.setExcitationCategory(ExcitationEventCategoryEnum.COURSE.getCode());
        }

        // 验证对应资源的激励配置是否存在
        LambdaQueryWrapper<ExcitationConfigUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExcitationConfigUser::getGlobalId, globalSyncMqEventDTO.getId());
        queryWrapper.eq(ExcitationConfigUser::getTargetId, targetId);
        queryWrapper.eq(ExcitationConfigUser::getIsDel, 0);
        List<ExcitationConfigUser> configList = excitationConfigUserService.list(queryWrapper);

        // 配置已存在：更新
        if (Optional.ofNullable(configList).isPresent() && !configList.isEmpty()) {

            // 仅当要求更新配置数据时才执行更新
            if (Integer.valueOf(1).equals(globalSyncMqEventDTO.getIsCoverData())) {
                for (ExcitationConfigUser configUser : configList) {
                    excitationConfigUser.setId(configUser.getId());
                    configUserList.add(excitationConfigUser);
                }
            }

            // 配置已存在且不要求进行数据更新，跳过执行
        }

        // 配置不存在：新增
        else {
            excitationConfigUser.setId(StringUtil.newId());
            configUserList.add(excitationConfigUser);
        }
    }
}
