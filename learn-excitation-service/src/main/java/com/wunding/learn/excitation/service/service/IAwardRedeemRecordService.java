package com.wunding.learn.excitation.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.wunding.learn.excitation.service.admin.dto.AwardRedeemAuditDTO;
import com.wunding.learn.excitation.service.admin.dto.AwardRedeemDetailDTO;
import com.wunding.learn.excitation.service.admin.dto.AwardRedeemPageDTO;
import com.wunding.learn.excitation.service.admin.dto.AwardRedeemRecordDTO;
import com.wunding.learn.excitation.service.admin.query.AwardRedeemPageQuery;
import com.wunding.learn.excitation.service.admin.query.AwardRedeemQuery;
import com.wunding.learn.excitation.service.client.dto.AwardRedeemRecordClientDTO;
import com.wunding.learn.excitation.service.client.dto.AwardRedeemRecordClientInfoDTO;
import com.wunding.learn.excitation.service.client.dto.ExchangeCommodityRecordVO;
import com.wunding.learn.excitation.service.client.query.ExcitationApiQuery;
import com.wunding.learn.excitation.service.model.AwardRedeemRecord;

/**
 * <p> 奖品兑换记录表 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi</a>
 * @since 2022-08-08
 */
public interface IAwardRedeemRecordService extends IService<AwardRedeemRecord> {

    /**
     * 奖品兑换记录分页数据查询
     *
     * @param query
     * @return
     */
    PageInfo<AwardRedeemPageDTO> getAwardRedeemRecordPage(AwardRedeemPageQuery query);

    /**
     * 获取
     *
     * @param id
     * @return
     */
    AwardRedeemDetailDTO getAwardDTOById(String id);

    /**
     * 审批兑奖记录成功(通过)
     *
     * @param ids
     */
    void auditAwardRedeemRecordPass(String ids);

    /**
     * 审核兑奖订单
     *
     * @param id
     * @param auditDTO
     */
    void auditAwardRedeemRecord(String id, AwardRedeemAuditDTO auditDTO);

    /**
     * 奖品兑换记录数据对象
     *
     * @param query
     * @return
     */
    AwardRedeemRecordDTO getAwardRedeemDTO(AwardRedeemQuery query);

    /**
     * 导出金币兑换
     *
     * @param query
     */
    void exportData(AwardRedeemPageQuery query);


    /**
     * 导出金币兑换记录
     *
     * @param query
     */
    void exportExchangeRecordData(AwardRedeemPageQuery query);

    /**
     * 校验是否可以兑换奖品
     *
     * @param awardId 奖品ID
     * @return
     */
    Boolean checkExchangeCommodity(String awardId);

    /**
     * 保存兑奖记录
     *
     * @param awardRedeemRecordDTO
     * @throws IllegalAccessException
     */
    void exchangeCommodity(AwardRedeemRecordClientDTO awardRedeemRecordDTO) throws IllegalAccessException;

    /**
     * 获取用户领取状态和非领取状态的记录总数
     *
     * @param userId
     * @return
     */
    long getUserRecordStatusForHasReceive(String userId, String awardId);

    /**
     * 643-获取我的兑换商城商品记录
     *
     * @param query
     * @return
     */
    PageInfo<ExchangeCommodityRecordVO> getExchangeCommodityRecord(ExcitationApiQuery query);

    /**
     * 兑奖商品详细信息
     *
     * @param id
     * @return
     */
    AwardRedeemRecordClientInfoDTO getExchangeCommodityInfo(String id);

    /**
     * 确认领取奖品
     *
     * @param id
     */
    void confirmReceived(String id);
}
