package com.wunding.learn.excitation.service.consumer;

import com.rabbitmq.client.Channel;
import com.wunding.learn.common.constant.mq.MqConst;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.mq.event.user.InitUserExcitationEvent;
import com.wunding.learn.common.mq.util.ConsumerAckUtil;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.common.util.string.TranslateUtil;
import com.wunding.learn.excitation.service.service.IUserCreditService;
import com.wunding.learn.excitation.service.service.IUserExperienceService;
import com.wunding.learn.excitation.service.service.IUserGoldCoinService;
import com.wunding.learn.excitation.service.service.IUserIntegralService;
import com.wunding.learn.excitation.service.service.IUserLearnTimeService;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

/**
 * 初始化用户激励
 *
 * <AUTHOR>
 * @date 2022/11/23
 */
@Component
@Slf4j
public class InitUserExcitationEventConsumer {

    /**
     * 队列名称
     */
    public static final String INIT_USER_EXCITATION_EVENT_QUEUE = "userInitExcitationEventQueue";

    @Resource
    private IUserIntegralService userIntegralService;
    @Resource
    private IUserCreditService userCreditService;
    @Resource
    private IUserGoldCoinService userGoldCoinService;
    @Resource
    private IUserLearnTimeService userLearnTimeService;
    @Resource
    private IUserExperienceService userExperienceService;
    @RabbitListener(
            bindings = @QueueBinding(
                    value = @Queue(value = INIT_USER_EXCITATION_EVENT_QUEUE),
                    exchange = @Exchange(value = MqConst.EXCHANGE_TOPIC, type = ExchangeTypes.TOPIC),
                    key = MqConst.INIT_USER_EXCITATION_ROUTING_KEY
            ),
            id = "initUserExcitationHandler"
    )
    public void initUserExcitationHandler(@Payload InitUserExcitationEvent excitationEvent,
                                          @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag, Channel channel) throws IOException {
        log.info("initUserExcitationHandler initUserExcitationEvent: {}", JsonUtil.objToJson(excitationEvent));
        UserThreadContext.setTenantId(excitationEvent.getTenantId());
        String userIds = excitationEvent.getUserIds();
        if (StringUtils.isNotBlank(userIds)){
            List<String> userIdList = TranslateUtil.translateBySplit(userIds, String.class);
            userIdList.forEach(userId -> {
                userIntegralService.initUserIntegral(userId, BigDecimal.ZERO);
                userCreditService.initUserCredit(userId, BigDecimal.ZERO);
                userGoldCoinService.initUserGoldCoin(userId, BigDecimal.ZERO);
                userLearnTimeService.initUserLearnTime(userId, BigDecimal.ZERO);
                userExperienceService.initUserExperience(userId, BigDecimal.ZERO);
            });
        }
        UserThreadContext.remove();
        ConsumerAckUtil.basicAck(excitationEvent,channel,deliveryTag, false);
    }
}
