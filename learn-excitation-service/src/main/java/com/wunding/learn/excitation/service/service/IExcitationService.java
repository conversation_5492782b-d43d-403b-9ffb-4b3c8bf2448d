package com.wunding.learn.excitation.service.service;

import com.wunding.learn.common.dto.pay.RequestPaymentDTO;
import com.wunding.learn.common.dto.pay.SubmitOrderDTO;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/8/21
 */
public interface IExcitationService {

    /**
     * 下单
     *
     * @param orderDTO
     * @return
     */
    RequestPaymentDTO placeOrder(SubmitOrderDTO orderDTO);

    /**
     * 获取金币单价
     *
     * @return
     */
    BigDecimal getGoldCoinUnitPrice();
}
