package com.wunding.learn.excitation.service.service.impl;

import com.wunding.learn.common.constant.user.UserErrorNoEnum;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.enums.excitation.ExcitationEventEnum;
import com.wunding.learn.common.enums.excitation.ExcitationTypeEnum;
import com.wunding.learn.common.enums.other.ExcitationOperationEnum;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.excitation.service.client.dto.ExchangeMemberCardDTO;
import com.wunding.learn.excitation.service.model.ExcitationTradeRecord;
import com.wunding.learn.excitation.service.service.IExcitationGoldCoinService;
import com.wunding.learn.user.api.dto.MemberCardDetailFeignDTO;
import com.wunding.learn.user.api.dto.MemberCardDetailFeignDTO.MemberCardSaleDTO;
import com.wunding.learn.user.api.dto.MemberCardExchangeDTO;
import com.wunding.learn.user.api.service.MemberCardFeign;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service("excitationGoldCoinService")
public class ExcitationGoldCoinServiceImpl implements IExcitationGoldCoinService {

    @Resource
    private MemberCardFeign memberCardFeign;
    @Resource
    private UserGoldCoinServiceImpl userGoldCoinService;
    @Resource
    private ExcitationTradeRecordServiceImpl excitationTradeRecordService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void exchangeMemberCard(ExchangeMemberCardDTO dto) {
        MemberCardDetailFeignDTO memberCardDetailFeignDTO = memberCardFeign.getMemberCardDetailFeignDTOById(
            dto.getMemberCardId());
        dto.setMemberCardName(memberCardDetailFeignDTO.getName());
        Date currentDate = new Date();
        // 每月金币
        Integer monthCoinNum = memberCardDetailFeignDTO.getMonthCoinNum();

        // 是否有优惠信息
        MemberCardSaleDTO saleDTO = memberCardDetailFeignDTO.getSaleDTO();
        if (Objects.nonNull(saleDTO)) {
            // 有优惠信息，是否在优惠期内
            if (Objects.nonNull(saleDTO.getStartTime()) && saleDTO.getStartTime().before(currentDate)
                && Objects.nonNull(saleDTO.getEndTime()) && saleDTO.getEndTime().after(currentDate)) {
                // 是否达到最小月数
                if (Objects.nonNull(saleDTO.getMinRenewalMonths()) && saleDTO.getMinRenewalMonths() > dto.getMonths()) {
                    throw new BusinessException(UserErrorNoEnum.ERROR_NO_EXCHANGE_MEMBER_CARD_FAIL, null,
                        "兑换失败，没有达到促销活动续费时长最小值(" + saleDTO.getMinRenewalMonths() + "月)");
                }
                // 是否超过最大月数
                if (Objects.nonNull(saleDTO.getMaxRenewalMonths()) && saleDTO.getMaxRenewalMonths() < dto.getMonths()) {
                    throw new BusinessException(UserErrorNoEnum.ERROR_NO_EXCHANGE_MEMBER_CARD_FAIL, null,
                        "兑换失败，已经超出促销活动续费时长最大值(" + saleDTO.getMaxRenewalMonths() + "月)");
                }
                monthCoinNum = saleDTO.getMonthCoinNum();
            }
        } else {
            // 没有优惠信息
            extracted(dto, memberCardDetailFeignDTO);
        }
        // 没有配置每月金币参数
        if (Objects.isNull(monthCoinNum)) {
            throw new BusinessException(UserErrorNoEnum.ERROR_NO_EXCHANGE_MEMBER_CARD_FAIL, null,
                "兑换失败，未配置该会员卡每月金币数量");
        }
        // 本次兑换需要消耗金币数量
        dto.setConsumeGoldCoinNum(monthCoinNum * dto.getMonths());
        executeExchange(dto);
    }

    private static void extracted(ExchangeMemberCardDTO dto, MemberCardDetailFeignDTO memberCardDetailFeignDTO) {
        if (Objects.nonNull(memberCardDetailFeignDTO.getMaxRenewalMonths())
            && memberCardDetailFeignDTO.getMaxRenewalMonths() < dto.getMonths()) {
            throw new BusinessException(UserErrorNoEnum.ERROR_NO_EXCHANGE_MEMBER_CARD_FAIL, null,
                "兑换失败，已经超出续费时长最大值(" + memberCardDetailFeignDTO.getMaxRenewalMonths() + "月)");
        }
    }

    /**
     * 执行兑换
     */
    public void executeExchange(ExchangeMemberCardDTO dto) {
        String currentUserId = UserThreadContext.getUserId();
        // 扣除金币
        BigDecimal reduce = userGoldCoinService.subtract(currentUserId,
            BigDecimal.valueOf(dto.getConsumeGoldCoinNum()));
        // 增加金币流水
        ExcitationTradeRecord tradeRecord = new ExcitationTradeRecord().setId(StringUtil.newId())
            .setUserId(currentUserId).setExcitationId(ExcitationTypeEnum.GOLD_COIN.getCode())
            .setOperateType(ExcitationOperationEnum.DECREASE.getValue())
            .setOperateNum(BigDecimal.valueOf(-dto.getConsumeGoldCoinNum()))
            .setIsExchange(GeneralJudgeEnum.CONFIRM.getValue()).setCurrentNum(reduce).setTradeType("MemberCard")
            .setTargetId(dto.getMemberCardId()).setTargetName(dto.getMemberCardName())
            .setEventId(ExcitationEventEnum.EXCHANGE_MEMBER_CARD.name()).setIsExchange(GeneralJudgeEnum.CONFIRM.getValue())
            .setSummary("兑换会员卡(" + dto.getMemberCardName() + ")" + ExcitationOperationEnum.DECREASE.getSymbol()
                + dto.getConsumeGoldCoinNum() + ExcitationTypeEnum.GOLD_COIN.getName());
        excitationTradeRecordService.save(tradeRecord);
        // 增加会员卡
        memberCardFeign.exchange(new MemberCardExchangeDTO().setMemberCardId(dto.getMemberCardId())
            .setUserId(currentUserId).setMonths(dto.getMonths()));
    }
}
