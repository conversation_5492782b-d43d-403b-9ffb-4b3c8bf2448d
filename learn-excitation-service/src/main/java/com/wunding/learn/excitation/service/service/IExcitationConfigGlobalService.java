package com.wunding.learn.excitation.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wunding.learn.excitation.service.admin.dto.ExcitationConfigGlobalGlobalSaveDTO;
import com.wunding.learn.excitation.service.admin.dto.ExcitationConfigGlobalPageDTO;
import com.wunding.learn.excitation.service.model.ExcitationConfigGlobal;
import java.util.List;

/**
 * <p> 全局激励配置表 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi</a>
 * @since 2022-08-08
 */
public interface IExcitationConfigGlobalService extends IService<ExcitationConfigGlobal> {

    /**
     * 根据激励事件分类获取对应的激励配置数据列表
     *
     * @param eventCategory 激励事件分类
     * @param systemType    体系分类
     * @return 激励配置数据列表
     */
    List<ExcitationConfigGlobalPageDTO> getDataListByCategory(String eventCategory, Integer systemType);

    /**
     * 保存激励配置数据
     *
     * @param saveDTO 全局激励配置信息保存数据对象
     */
    void saveData(ExcitationConfigGlobalGlobalSaveDTO saveDTO);

    /**
     * 更新激励配置数据
     *
     * @param id      激励配置id
     * @param saveDTO 激励配置信息保存数据对象
     */
    void modifyData(String id, ExcitationConfigGlobalGlobalSaveDTO saveDTO);

    /**
     * 启用/禁用数据
     *
     * @param id          数据主键id
     * @param isAvailable 0禁用/1启用
     */
    void availableData(String id, Integer isAvailable);

    /**
     * 启用/禁用激励配置数据兑换属性
     *
     * @param id         数据主键id
     * @param isExchange 0禁用/1启用
     */
    void exchangeData(String id, Integer isExchange);

    /**
     * 锁定/解除锁定
     *
     * @param id     数据主键id
     * @param isLock 是否锁定
     */
    void lockData(String id, Integer isLock);

    /**
     * 删除
     *
     * @param id
     */
    void deleteData(String id);

    /**
     * 全局激励配置同步
     *
     * @param id
     * @param isCoverData 是否更新数据配置 1是/0否 (选择否时，如果配置已存在则忽略同步)
     */
    void globalSyncData(String id, Integer isCoverData);
}
