package com.wunding.learn.excitation.service.service;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/11/25
 */
public interface IExcitationOperation {

    /**
     * 增加激励
     *
     * @param userId
     * @param amount
     * @return
     */
    BigDecimal add(String userId, BigDecimal amount);

    /**
     * 增加激励
     *
     * @param userId
     * @param amount
     * @param isExchange
     * @return
     */
    BigDecimal add(String userId, BigDecimal amount, Integer isExchange);

    /**
     * 增加激励
     *
     * @param userId
     * @param amount
     * @param isExchange
     * @return
     */
    BigDecimal baseAdd(String userId, BigDecimal amount, Integer isExchange);

    /**
     * 减少可用激励
     *
     * @param userId
     * @param amount
     * @return 剩余积分
     */
    BigDecimal subtract(String userId, BigDecimal amount);
}
