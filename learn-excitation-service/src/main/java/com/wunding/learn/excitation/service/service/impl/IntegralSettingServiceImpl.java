package com.wunding.learn.excitation.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.excitation.service.mapper.IntegralSettingMapper;
import com.wunding.learn.excitation.service.model.IntegralSetting;
import com.wunding.learn.excitation.service.service.IIntegralSettingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p> 积分设置表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi</a>
    * @since 2022-08-08
 */
@Slf4j
@Service("integralSettingService")
public class IntegralSettingServiceImpl extends ServiceImpl<IntegralSettingMapper, IntegralSetting> implements IIntegralSettingService {

}
