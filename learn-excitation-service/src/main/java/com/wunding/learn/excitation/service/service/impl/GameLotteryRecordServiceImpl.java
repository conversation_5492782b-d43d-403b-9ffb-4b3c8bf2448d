package com.wunding.learn.excitation.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.wunding.learn.common.constant.excitation.ExcitationErrorNoEnum;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.enums.file.ImageTypeEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.util.bean.SpringUtil;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.common.util.string.TranslateUtil;
import com.wunding.learn.excitation.service.admin.dto.ContactInfoDTO;
import com.wunding.learn.excitation.service.admin.dto.GameLotteryRecordDistributionDetailDTO;
import com.wunding.learn.excitation.service.admin.dto.GameLotteryRecordStatusDTO;
import com.wunding.learn.excitation.service.admin.dto.LotteryRecordPageDTO;
import com.wunding.learn.excitation.service.admin.query.AwardSqlQuery;
import com.wunding.learn.excitation.service.admin.query.LotteryRecordPageQuery;
import com.wunding.learn.excitation.service.client.query.ExcitationApiQuery;
import com.wunding.learn.excitation.service.mapper.AwardMapper;
import com.wunding.learn.excitation.service.mapper.AwardRedeemRecordMapper;
import com.wunding.learn.excitation.service.mapper.GameLotteryRecordMapper;
import com.wunding.learn.excitation.service.mapper.GameOptionMapper;
import com.wunding.learn.excitation.service.model.Award;
import com.wunding.learn.excitation.service.model.AwardRedeemRecord;
import com.wunding.learn.excitation.service.model.GameLotteryRecord;
import com.wunding.learn.excitation.service.model.GameOption;
import com.wunding.learn.excitation.service.service.IGameLotteryRecordService;
import com.wunding.learn.file.api.component.ExportComponent;
import com.wunding.learn.file.api.constant.ExportBizType;
import com.wunding.learn.file.api.constant.ExportFileNameEnum;
import com.wunding.learn.file.api.dto.IExportDataDTO;
import com.wunding.learn.file.api.service.FileFeign;
import com.wunding.learn.user.api.dto.OrgShowDTO;
import com.wunding.learn.user.api.dto.UserDTO;
import com.wunding.learn.user.api.service.OrgFeign;
import com.wunding.learn.user.api.service.UserFeign;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * <p> 抽奖记录表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi</a>
 * @since 2022-08-08
 */
@Slf4j
@Service("gameLotteryRecordService")
public class GameLotteryRecordServiceImpl extends ServiceImpl<GameLotteryRecordMapper, GameLotteryRecord> implements
    IGameLotteryRecordService {

    @Resource
    private AwardMapper awardMapper;

    @Resource
    private GameOptionMapper gameOptionMapper;

    @Resource
    private UserFeign userFeign;

    @Resource
    private ExportComponent exportComponent;

    @Resource
    private OrgFeign orgFeign;

    @Resource
    private FileFeign fileFeign;
    @Resource
    private AwardRedeemRecordMapper awardRedeemRecordMapper;

    @Override
    public PageInfo<LotteryRecordPageDTO> getLotteryRecordPage(LotteryRecordPageQuery pageQuery) {
        PageInfo<LotteryRecordPageDTO> targetPageInfo = new PageInfo<>();
        String name = pageQuery.getName();
        if (StringUtils.isNotBlank(pageQuery.getUserIdStr())) {
            pageQuery.setUserIds(TranslateUtil.translateBySplit(pageQuery.getUserIdStr(), String.class));
        }
        // 管辖范围
        Set<String> userManageAreaOrgId = orgFeign.findUserManageAreaLevelPath(UserThreadContext.getUserId());
        pageQuery.setUserManageAreaOrgId(userManageAreaOrgId);
        pageQuery.setCurrentUserId(UserThreadContext.getUserId());
        pageQuery.setCurrentOrgId(UserThreadContext.getOrgId());

        if (StringUtils.isNotBlank(name)) {
            List<Award> awardList = awardMapper.selectList(new LambdaQueryWrapper<Award>().like(Award::getName, name));
            Set<String> awardIds = awardList.stream().map(Award::getId).collect(Collectors.toSet());

            List<GameOption> optionList = gameOptionMapper
                .selectList(new LambdaQueryWrapper<GameOption>().like(GameOption::getName, name));
            Set<String> optionIds = optionList.stream().map(GameOption::getId).collect(Collectors.toSet());

            if (CollectionUtils.isEmpty(awardList) && CollectionUtils.isEmpty(optionList)) {
                return targetPageInfo;
            }
            pageQuery.setAwardIds(awardIds);
            pageQuery.setOptionIds(optionIds);
        }

        PageInfo<GameLotteryRecord> originalPageInfo = PageMethod
            .startPage(pageQuery.getPageNo(), pageQuery.getPageSize())
            .doSelectPageInfo(() -> baseMapper.queryPage(pageQuery));
        BeanUtils.copyProperties(originalPageInfo, targetPageInfo);

        List<GameLotteryRecord> dataList = originalPageInfo.getList();
        if (CollectionUtils.isEmpty(dataList)) {
            return targetPageInfo;
        }

        // 获取中奖用户信息
        Set<String> userIdSet = dataList.stream().map(GameLotteryRecord::getCreateBy).collect(Collectors.toSet());
        Map<String, UserDTO> userDTOMap = userFeign.getUserNameMapByIds(userIdSet);
        // 获取处理人信息
        Set<String> dealByIdSet = dataList.stream().map(GameLotteryRecord::getDealBy).collect(Collectors.toSet());
        Map<String, UserDTO> dealByMap = userFeign.getUserNameMapByIds(dealByIdSet);
        // 获取部门简称
        Map<String, OrgShowDTO> orgShowDTOMap = orgFeign.getOrgShowDTO(userFeign.getUserOrdIdByUserIds(userIdSet));
        // 获取奖项
        Set<String> optionIdSet = dataList.stream().map(GameLotteryRecord::getOptionId).collect(Collectors.toSet());
        List<GameOption> optionList = gameOptionMapper
            .selectList(new LambdaQueryWrapper<GameOption>().in(GameOption::getId, optionIdSet));
        Map<String, String> gameOptionMap = optionList.stream()
            .collect(Collectors.toMap(GameOption::getId, GameOption::getName));

        // 获取奖品
        Set<String> awardIdSet = dataList.stream().map(GameLotteryRecord::getAwardId).filter(StringUtils::isNotBlank)
            .collect(Collectors.toSet());
        List<Award> awardList = awardMapper.getAwardList(new AwardSqlQuery().setAwardIds(awardIdSet));
        Map<String, Award> awardMap = awardList.stream()
            .collect(Collectors.toMap(Award::getId,item->item, (key1,key2)->key1));

        List<LotteryRecordPageDTO> targetDataList = dataList.stream().map(data -> {
            LotteryRecordPageDTO dto = new LotteryRecordPageDTO();
            BeanUtils.copyProperties(data, dto);
            dto.setLotteryTime(data.getCreateTime());
            dto.setLevel(data.getAwardLevel());
            dto.setAwardId(data.getAwardId());

            // 中奖人信息
            UserDTO userDTO = userDTOMap.get(data.getCreateBy());
            if (Objects.nonNull(userDTO)) {
                dto.setLoginName(userDTO.getLoginName());
                dto.setUserName(userDTO.getFullName());
                Optional.ofNullable(orgShowDTOMap.get(userDTO.getOrgId())).ifPresent(orgShowDTO ->
                    dto.setOrgName(orgShowDTO.getOrgShortName()).setOrgPath(orgShowDTO.getLevelPathName())
                );
            }
            // 发放人信息
            UserDTO dealUserDTO = dealByMap.get(data.getDealBy());
            if (Objects.nonNull(dealUserDTO)) {
                dto.setDealLoginName(dealUserDTO.getLoginName());
                dto.setDealUserName(dealUserDTO.getFullName());
            }

            // 奖品信息
            Award awardInfo = awardMap.get(data.getAwardId());
            if(null != awardInfo){
                dto.setAwardName(awardInfo.getName());
                dto.setAwardCategoryId(awardInfo.getCategoryId());
            }
            dto.setOptionName(gameOptionMap.get(data.getOptionId()));

            return dto;
        }).collect(Collectors.toList());
        targetPageInfo.setList(targetDataList);
        return targetPageInfo;
    }

    @Override
    public void exportData(LotteryRecordPageQuery pageQuery) {

        IExportDataDTO exportDataDTO = new IExportDataDTO() {
            @Override
            public List<Map<String, Object>> getData(Integer pageNo, Integer pageSize) {
                IGameLotteryRecordService gameLotteryRecordService =
                    SpringUtil.getBean("gameLotteryRecordService", IGameLotteryRecordService.class);

                pageQuery.setExport(true);
                pageQuery.setPageNo(pageNo);
                pageQuery.setPageSize(pageSize);
                List<Map<String, Object>> lotteryRecordPageDTOS = new ArrayList<>();
                if (Optional.ofNullable(gameLotteryRecordService).isPresent()) {
                    PageInfo<LotteryRecordPageDTO> pageInfo = gameLotteryRecordService.getLotteryRecordPage(pageQuery);
                    for (LotteryRecordPageDTO lotteryRecordPageDTO : pageInfo.getList()) {
                        Map<String, Object> beanMap = JsonUtil.parseObjectToMap(lotteryRecordPageDTO);
                        lotteryRecordPageDTOS.add(beanMap);
                    }
                }
                return lotteryRecordPageDTOS;
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.GameLottery;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.GameLottery.getType();
            }
        };

        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    public void updateGameLotteryRecordDistributionInfo(
        GameLotteryRecordDistributionDetailDTO distributionDetailDTO) {
        GameLotteryRecord gameLotteryRecord = getById(distributionDetailDTO.getId());
        gameLotteryRecord.setContactName(distributionDetailDTO.getContactName())
            .setContactPhone(distributionDetailDTO.getContactPhone())
            .setRemark(distributionDetailDTO.getRemark())
            .setAddress(distributionDetailDTO.getAddress());

        baseMapper.updateById(gameLotteryRecord);
    }

    @Override
    public void updateGameLotteryRecordStatus(GameLotteryRecordStatusDTO gameLotteryRecordStatusDTO) {
        List<String> idList = Arrays.asList(gameLotteryRecordStatusDTO.getId().split(","));
        log.info("idList: " + idList);
        for(String id : idList) {
            GameLotteryRecord gameLotteryRecord = getById(id);

            if (Optional.ofNullable(gameLotteryRecord).isEmpty()){
                throw new BusinessException(ExcitationErrorNoEnum.ERR_CONTACT_IS_NULL);
            }
            gameLotteryRecord.setRemark(gameLotteryRecordStatusDTO.getRemark());
            if (gameLotteryRecordStatusDTO.getStatus() == 0){
                gameLotteryRecord.setStatus(0);
                gameLotteryRecord.setDealTime(null);
                gameLotteryRecord.setDealBy("");
            } else {
                gameLotteryRecord.setStatus(gameLotteryRecordStatusDTO.getStatus())
                    .setDealBy(UserThreadContext.getUserId())
                    .setDealTime(new Date());
            }
            log.info("gameLotteryRecord: " + gameLotteryRecord);
            baseMapper.updateGameLotteryRecordById(gameLotteryRecord);

            // 同步标记奖品兑换记录表
            AwardRedeemRecord awardRedeemRecord = awardRedeemRecordMapper.selectById(id);
            if (awardRedeemRecord != null) {
                awardRedeemRecord.setStatus(gameLotteryRecordStatusDTO.getStatus());
                awardRedeemRecordMapper.updateById(awardRedeemRecord);
            }
        }
    }

    @Override
    public PageInfo<LotteryRecordPageDTO> getGameLotteryRecord(ExcitationApiQuery pageQuery) {
        pageQuery.setCurrentUserId(UserThreadContext.getUserId());
        PageInfo<LotteryRecordPageDTO> pageInfo = PageMethod
            .startPage(pageQuery.getPageNo(), pageQuery.getPageSize())
            .doSelectPageInfo(() -> baseMapper.getGameLotteryRecord(pageQuery));
        if (CollectionUtils.isEmpty(pageInfo.getList())) {
            return pageInfo;
        }
        // 获取奖项
        Set<String> optionIdSet = pageInfo.getList().stream().map(LotteryRecordPageDTO::getOptionId).collect(Collectors.toSet());
        List<GameOption> optionList = gameOptionMapper
            .selectList(new LambdaQueryWrapper<GameOption>().in(GameOption::getId, optionIdSet));
        Map<String, String> gameOptionMap = optionList.stream()
            .collect(Collectors.toMap(GameOption::getId, GameOption::getName));

        // 获取奖品
        Set<String> awardIdSet = pageInfo.getList().stream().map(LotteryRecordPageDTO::getAwardId).filter(StringUtils::isNotBlank)
            .collect(Collectors.toSet());
        List<Award> awardList = awardMapper.getAwardList(new AwardSqlQuery().setAwardIds(awardIdSet));
        Map<String, Award> awardMap = awardList.stream()
            .collect(Collectors.toMap(Award::getId,item->item, (key1,key2)->key1));

        // 奖品图片
        Map<String, String> imageUrlsByIds = fileFeign.getImageUrlsByIds(awardIdSet,
            ImageTypeEnum.AwardImgFirst.name());

        pageInfo.getList().forEach(lotteryRecordPageDTO -> {
            // 奖品信息
            Award awardInfo = awardMap.get(lotteryRecordPageDTO.getAwardId());
            if(null != awardInfo){
                lotteryRecordPageDTO.setAwardName(awardInfo.getName());
                lotteryRecordPageDTO.setAwardCategoryId(awardInfo.getCategoryId());
            }
            lotteryRecordPageDTO.setOptionName(gameOptionMap.get(lotteryRecordPageDTO.getOptionId()));

            String imgPath = imageUrlsByIds.get(lotteryRecordPageDTO.getAwardId());
            if (StringUtils.isNotBlank(imgPath)) {
                lotteryRecordPageDTO.setImage(imgPath);
            }

        });

        return pageInfo;
    }

    @Override
    public ContactInfoDTO getContactInfo(String id) {
        return baseMapper.getContactInfo(id);
    }
}
