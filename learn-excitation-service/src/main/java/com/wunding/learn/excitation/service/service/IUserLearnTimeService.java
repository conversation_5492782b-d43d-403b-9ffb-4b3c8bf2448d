package com.wunding.learn.excitation.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wunding.learn.excitation.service.client.dto.ExcitationEventObjectDTO;
import com.wunding.learn.excitation.service.model.UserLearnTime;
import java.math.BigDecimal;

/**
 * <p> 用户学时表 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi</a>
 * @since 2022-08-08
 */
public interface IUserLearnTimeService extends IService<UserLearnTime> {

    /**
     * 保存或更新用户学时
     *
     * @param eventObject {@link ExcitationEventObjectDTO}
     */
    void saveOrUpdateUserLearnTime(ExcitationEventObjectDTO eventObject);

    /**
     * @param userId
     * @return
     */
    BigDecimal getLearnTime(String userId);

    /**
     * 初始化用户学时
     *
     * @param userId
     * @param fund
     */
    UserLearnTime initUserLearnTime(String userId, BigDecimal fund);

    /**
     * 初始化用户学时
     *
     * @param userId
     * @param fund
     * @param isExchange
     */
    UserLearnTime initUserLearnTime(String userId, BigDecimal fund, Integer isExchange);

    /**
     * @param userId
     * @return
     */
    BigDecimal getLearnAvailavleTime(String userId);

    /**
     * getById的平替
     *
     * @param userId
     * @return
     */
    UserLearnTime getDataById(String userId);

    /**
     * 获取 可兑换学时
     *
     * @param userId
     * @return
     */
    BigDecimal getLearnConvertibleNum(String userId);
}
