package com.wunding.learn.excitation.service.service.impl;

import static com.wunding.learn.common.util.string.StringUtil.newId;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.common.constant.other.DelEnum;
import com.wunding.learn.common.enums.excitation.ExcitationTypeEnum;
import com.wunding.learn.common.enums.excitation.OtherEventCategoryEnum;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.util.string.StringPool;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.excitation.service.admin.dto.AutomaticExchangeDTO;
import com.wunding.learn.excitation.service.admin.dto.ExchangeRuleConfigDTO;
import com.wunding.learn.excitation.service.client.dto.ExchangeConfig;
import com.wunding.learn.excitation.service.client.dto.ExcitationEventObjectDTO;
import com.wunding.learn.excitation.service.dao.ExchangeRuleConfigDao;
import com.wunding.learn.excitation.service.mapper.ExchangeRuleConfigMapper;
import com.wunding.learn.excitation.service.mapper.ExcitationTypeMapper;
import com.wunding.learn.excitation.service.model.ExchangeRecord;
import com.wunding.learn.excitation.service.model.ExchangeRuleConfig;
import com.wunding.learn.excitation.service.model.ExcitationType;
import com.wunding.learn.excitation.service.model.UserGoldCoin;
import com.wunding.learn.excitation.service.service.IExchangeRecordService;
import com.wunding.learn.excitation.service.service.IExchangeRuleConfigService;
import com.wunding.learn.excitation.service.service.IExcitationEventService;
import com.wunding.learn.excitation.service.service.IUserGoldCoinService;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * <p> 金币兑换配置 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi</a>
 * @since 2022-08-08
 */
@Slf4j
@Service("exchangeRuleConfigService")
public class ExchangeRuleConfigServiceImpl extends ServiceImpl<ExchangeRuleConfigMapper, ExchangeRuleConfig> implements
    IExchangeRuleConfigService {

    @Resource
    private ExcitationTypeMapper excitationTypeMapper;

    @Resource
    private IExcitationEventService excitationEventService;

    @Resource
    private IUserGoldCoinService userGoldCoinService;

    @Resource
    private IExchangeRecordService exchangeRecordService;
    @Resource(name = "exchangeRuleConfigDao")
    private ExchangeRuleConfigDao exchangeRuleConfigDao;

    @Override
    public List<ExchangeRuleConfigDTO> dataList(Integer systemType) {
        // 获取可以兑换金币的激励
        List<ExcitationType> excitationTypes = excitationTypeMapper.selectList(
            new LambdaQueryWrapper<ExcitationType>().eq(ExcitationType::getIsDel, DelEnum.NOT_DELETE.getValue())
                .eq(ExcitationType::getExchangeGoldCoin, GeneralJudgeEnum.CONFIRM.getValue()));
        Set<String> excitationIds = excitationTypes.stream().map(ExcitationType::getId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(excitationIds)) {
            return new ArrayList<>();
        }
        // 获取当前激励体系下已经配置的兑换规则
        Map<String, ExchangeRuleConfig> exchangeRuleConfigMap = list(
            new LambdaQueryWrapper<ExchangeRuleConfig>().in(ExchangeRuleConfig::getExcitationId, excitationIds)
                .eq(ExchangeRuleConfig::getSystemType, systemType)).stream()
            .collect(Collectors.toMap(ExchangeRuleConfig::getExcitationId, Function.identity(), (v1, v2) -> v1));
        return excitationIds.stream().map(excitationId -> {
            ExchangeRuleConfigDTO configDTO = new ExchangeRuleConfigDTO().setExcitationId(excitationId)
                .setSystemType(systemType);
            Optional.ofNullable(exchangeRuleConfigMap.get(excitationId))
                .ifPresent(exchangeRuleConfig -> BeanUtils.copyProperties(exchangeRuleConfig, configDTO));
            return configDTO;
        }).collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateDataBatch(List<ExchangeRuleConfigDTO> configDTOList) {
        configDTOList.forEach(exchangeRuleConfigDTO -> {
            ExchangeRuleConfig config = new ExchangeRuleConfig();
            BeanUtils.copyProperties(exchangeRuleConfigDTO, config);
            String configName = null;
            switch (exchangeRuleConfigDTO.getExcitationId()) {
                case "integral":
                    configName = "积分";
                    break;
                case "credit":
                    configName = "学分";
                    break;
                case "learnTime":
                    configName = "学时";
                    break;
                default:
                    configName = "";
            }
            if (StringUtils.isBlank(config.getId())) {
                config.setId(newId());
                exchangeRuleConfigDao.saveExchangeRuleConfig(config, configName);
            } else {
                exchangeRuleConfigDao.updateExchangeRuleConfig(config, configName);
            }
        });
    }

    @Override
    public Integer isExchange(Integer systemType) {
        return baseMapper.isExchange(systemType);
    }

    @Override
    public List<ExchangeConfig> getExchangeConfigs(Integer systemType) {
        return baseMapper.getExchangeConfigs(systemType);
    }

    @Override
    public ExchangeConfig getExchangeConfigById(String code, Integer systemType) {
        return baseMapper.getExchangeConfigById(code, systemType);
    }

    @Override
    public void autoExchangeGoldCoin() {
        long startTime = System.currentTimeMillis();
        log.info("-----------autoExchangeGoldCoin------------");

        List<ExcitationEventObjectDTO> eventObjectList = new ArrayList<>();
        List<ExchangeRecord> exchangeRecordList = new ArrayList<>();

        //查询自动兑换的配置
        List<ExchangeRuleConfig> ruleConfigList = lambdaQuery().eq(ExchangeRuleConfig::getType, 1)
            .ne(ExchangeRuleConfig::getExcitationId, StringPool.EMPTY).list();
        for (ExchangeRuleConfig exchangeRuleConfig : ruleConfigList) {
            ExcitationTypeEnum typeEnum = ExcitationTypeEnum.get(exchangeRuleConfig.getExcitationId());
            List<AutomaticExchangeDTO> exchangeVOList = baseMapper.selectAutomaticExchangeDTOList(typeEnum.getCode(),
                exchangeRuleConfig.getSystemType());
            for (AutomaticExchangeDTO exchangeVO : exchangeVOList) {
                // 兑换值消耗
                ExcitationEventObjectDTO exchangeValue = new ExcitationEventObjectDTO();
                exchangeValue.setUserId(exchangeVO.getUserId());
                exchangeValue.setTargetId(typeEnum.getCode());
                exchangeValue.setCategory(OtherEventCategoryEnum.exchangeGoldCoin.name());
                exchangeValue.setEventId(OtherEventCategoryEnum.exchangeGoldCoin.name());
                exchangeValue.setScore(new BigDecimal(exchangeVO.getPayNum()));
                exchangeValue.setTypeId(typeEnum.getCode());
                exchangeValue.setOperateType(1);
                exchangeValue.setRemark("兑换".concat(exchangeVO.getCoinNum().toString()).concat("金币"));
                exchangeValue.setAddBy("admin");
                eventObjectList.add(exchangeValue);
                // 金币增加
                ExcitationEventObjectDTO coinValue = new ExcitationEventObjectDTO();
                coinValue.setUserId(exchangeVO.getUserId());
                coinValue.setTargetId(typeEnum.getCode());
                coinValue.setCategory(OtherEventCategoryEnum.exchangeGoldCoin.name());
                coinValue.setEventId(OtherEventCategoryEnum.exchangeGoldCoin.name());
                coinValue.setScore(new BigDecimal(exchangeVO.getCoinNum()));
                coinValue.setTypeId(ExcitationTypeEnum.GOLD_COIN.getCode());
                coinValue.setOperateType(0);
                coinValue.setRemark(exchangeVO.getPayNum().toString().concat(typeEnum.getName()).concat("兑换"));
                coinValue.setAddBy("admin");
                eventObjectList.add(coinValue);
                // 金币兑换记录
                UserGoldCoin userGoldCoin = userGoldCoinService.getDataById(exchangeVO.getUserId());
                // 兑换记录
                ExchangeRecord exchangeRecord = new ExchangeRecord().setId(StringUtil.newId())
                    .setUserId(exchangeVO.getUserId()).setConsumeNum(new BigDecimal(exchangeVO.getPayNum()))
                    .setRewardNum(new BigDecimal(exchangeVO.getCoinNum()))
                    .setRestNum(userGoldCoin.getRemainNum().add(new BigDecimal(exchangeVO.getCoinNum()))).setType(1)
                    .setRemark(typeEnum.getName().concat("兑换金币")).setExcitationTypeId(typeEnum.getCode());
                exchangeRecordList.add(exchangeRecord);
            }
        }
        if (eventObjectList.size() > 0) {
            excitationEventService.otherBatchEventHandler(eventObjectList);
        }

        if (exchangeRecordList.size() > 0) {
            exchangeRecordService.saveBatch(exchangeRecordList, 100);
        }

        log.info("-----------autoExchangeGoldCoin------------end:" + (System.currentTimeMillis() - startTime));
    }
}
