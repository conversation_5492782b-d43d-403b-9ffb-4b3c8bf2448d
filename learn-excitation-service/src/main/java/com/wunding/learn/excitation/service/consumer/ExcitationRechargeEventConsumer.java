package com.wunding.learn.excitation.service.consumer;

import com.wunding.learn.common.constant.mq.MqConst;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

/**
 * 金币充值事件消费者
 *
 * <AUTHOR>
 * @date 2023/7/27
 */
@Component
@Slf4j
public class ExcitationRechargeEventConsumer {

    @RabbitListener(
        bindings = @QueueBinding(
            value = @Queue(value = MqConst.ORDER_OF_EXCITATION_PAY_SUCCESS_EVENT_QUEUE),
            exchange = @Exchange(value = MqConst.EXCHANGE_TOPIC, type = ExchangeTypes.TOPIC),
            key = MqConst.ORDER_PAY_SUCCESS_EVENT_ROUTING_KEY_EXCITATION
        ),
        id = "excitationRechargeEventConsumerHandler"
    )
    public void handle() {
        // 增加金币余额
        // 增加金币充值记录
        // 手动ack
    }
}
