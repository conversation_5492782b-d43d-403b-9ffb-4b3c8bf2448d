package com.wunding.learn.excitation.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.excitation.service.mapper.ExcitationSchemeRangeMapper;
import com.wunding.learn.excitation.service.model.ExcitationSchemeRange;
import com.wunding.learn.excitation.service.service.IExcitationSchemeRangeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p> 激励方案范围表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi</a>
    * @since 2022-08-08
 */
@Slf4j
@Service("excitationSchemeRangeService")
public class ExcitationSchemeRangeServiceImpl extends ServiceImpl<ExcitationSchemeRangeMapper, ExcitationSchemeRange> implements IExcitationSchemeRangeService {

}
