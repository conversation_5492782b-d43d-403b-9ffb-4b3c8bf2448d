package com.wunding.learn.excitation.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.wunding.learn.excitation.service.admin.dto.CategoryDTO;
import com.wunding.learn.excitation.service.admin.dto.CategoryListDTO;
import com.wunding.learn.excitation.service.admin.dto.CategoryPageDTO;
import com.wunding.learn.excitation.service.admin.dto.CategorySaveDTO;
import com.wunding.learn.excitation.service.admin.query.CategoryListQuery;
import com.wunding.learn.excitation.service.admin.query.CategoryPageQuery;
import com.wunding.learn.excitation.service.model.Category;
import java.util.List;
import org.springframework.scheduling.annotation.Async;

/**
 * <AUTHOR>
 * @date 2022/6/21
 */
public interface ICategoryService extends IService<Category> {

    /**
     * 分页查询分类数据
     *
     * @param query
     * @return
     */
    PageInfo<CategoryPageDTO> queryPage(CategoryPageQuery query);

    /**
     * 查询分类数据列表
     *
     * @param query
     * @return
     */
    List<CategoryListDTO> queryList(CategoryListQuery query);

    /**
     * 获取分类数据详情
     *
     * @param id
     * @return
     */
    CategoryDTO getCategoryById(String id);

    /**
     * 新增分类数据
     *
     * @param saveDTO
     */
    void saveCategory(CategorySaveDTO saveDTO);

    /**
     * 新增分类数据
     *
     * @param id
     * @param saveDTO
     */
    void updateCategory(String id, CategorySaveDTO saveDTO);

    /**
     * 启用/禁用分类
     *
     * @param ids
     * @param availableStatus
     */
    void availableCategory(String ids, Integer availableStatus);

    /**
     * 删除分类
     *
     * @param ids
     */
    void deleteCategory(String ids);

    /**
     * 导出奖品分类列表
     */
    @Async
    void exportData(CategoryPageQuery query);
}
