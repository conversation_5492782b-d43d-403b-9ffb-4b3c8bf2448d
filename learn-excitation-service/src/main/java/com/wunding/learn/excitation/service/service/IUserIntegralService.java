package com.wunding.learn.excitation.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.dto.ImportResultDTO;
import com.wunding.learn.common.dto.MyStatisticListDTO;
import com.wunding.learn.excitation.service.admin.dto.ImportIntegralDTO;
import com.wunding.learn.excitation.service.admin.dto.UserIntegralDetailDTO;
import com.wunding.learn.excitation.service.admin.query.IntegralClearQuery;
import com.wunding.learn.excitation.service.admin.query.UserIntegralQuery;
import com.wunding.learn.excitation.service.client.dto.CommitExchangeGoldCoin;
import com.wunding.learn.excitation.service.client.dto.ExcitationEventObjectDTO;
import com.wunding.learn.excitation.service.client.dto.ExcitationExplainDTO;
import com.wunding.learn.excitation.service.client.dto.MyStatisticDTO;
import com.wunding.learn.excitation.service.client.dto.PointsDTO;
import com.wunding.learn.excitation.service.client.dto.UserExcitationItemDTO;
import com.wunding.learn.excitation.service.client.query.ExcitationApiQuery;
import com.wunding.learn.excitation.service.client.query.ExcitationExplainQuery;
import com.wunding.learn.excitation.service.model.UserIntegral;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p> 用户积分表 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi</a>
 * @since 2022-08-08
 */
public interface IUserIntegralService extends IService<UserIntegral> {

    /**
     * 保存或更新用户积分
     *
     * @param eventObject {@link ExcitationEventObjectDTO}
     */
    void saveOrUpdateUserIntegral(ExcitationEventObjectDTO eventObject);

    /**
     * 获取个人信息
     *
     * @param checkSysConfig 是否校验系统参数配置
     * @return
     */
    List<MyStatisticListDTO> getPersonalInfo(boolean checkSysConfig) throws IllegalAccessException;

    /**
     * 获取积分
     *
     * @param queryDTO
     * @return
     */
    PageInfo<UserExcitationItemDTO> getIntegralPageInfo(ExcitationApiQuery queryDTO);

    /**
     * 获取学时
     *
     * @param queryDTO
     * @return
     */
    PageInfo<UserExcitationItemDTO> getLearnTimePageInfo(ExcitationApiQuery queryDTO);

    /**
     * 获取金币
     *
     * @param queryDTO
     * @return
     */
    PageInfo<UserExcitationItemDTO> getLearnCreditPageInfo(ExcitationApiQuery queryDTO);

    /**
     * 获取金币
     *
     * @param queryDTO
     * @return
     */
    PageInfo<UserExcitationItemDTO> getGoldCoinPageInfo(ExcitationApiQuery queryDTO);

    /**
     * 初始化用户积分
     *
     * @param userId
     * @param amount
     * @return
     */
    UserIntegral initUserIntegral(String userId, BigDecimal amount);

    /**
     * 初始化用户积分
     *
     * @param userId
     * @param amount
     * @param isExchange
     * @return
     */
    UserIntegral initUserIntegral(String userId, BigDecimal amount, Integer isExchange);


    /**
     * 获取激励说明
     *
     * @param excitationExplainQuery
     * @return
     */
    List<ExcitationExplainDTO> getExcitationExplain(ExcitationExplainQuery excitationExplainQuery);

    /**
     * 积分清零
     *
     * @param query
     */
    void cleanIntegral(IntegralClearQuery query);

    /**
     * 批量导入积分数据
     *
     * @param dto
     * @return
     */
    ImportResultDTO importData(ImportIntegralDTO dto);


    /**
     * 获取用户总积分总学分总学时
     *
     * @return
     */
    MyStatisticDTO getMyStatisticDTO();

    /**
     * 获取用户有效积分，有效学分，有效学时
     *
     * @return
     */
    MyStatisticDTO getMyStatisticAvailableNumDTO();

    /**
     * 是否去兑换金币
     *
     * @return
     */
    Boolean isExchange();

    List<PointsDTO> getMyPointsInfo();


    /**
     * 兑换金币
     *
     * @param commitExchangeGoldCoin
     */
    void commitExchangeGoldCoin(CommitExchangeGoldCoin commitExchangeGoldCoin);

    /**
     * 查看用户积分记录
     *
     * @param query
     * @return
     */
    PageInfo<UserIntegralDetailDTO> getUserIntegralRecorder(UserIntegralQuery query);

    /**
     * 导出用户积分记录
     *
     * @param query
     */
    void exportUserIntegralRecorderData(UserIntegralQuery query);

    /**
     * getById的平替
     *
     * @param userId
     * @return
     */
    UserIntegral getDataById(String userId);

    /**
     * 根据兑换类型，获取课程可兑换激励数量
     *
     * @param excitationType
     * @return 兑换激励数量
     */
    BigDecimal getCourseExchangeable(String excitationType);
    /**
     * 取用户积分
     *
     * @param userId
     * @return
     */
    BigDecimal getUserIntegral(String userId);

}
