package com.wunding.learn.excitation.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.wunding.learn.common.category.model.Categorys;
import com.wunding.learn.common.category.service.ICategorysService;
import com.wunding.learn.common.constant.excitation.ExcitationErrorNoEnum;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.util.bean.SpringUtil;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.common.util.string.TranslateUtil;
import com.wunding.learn.excitation.service.admin.dto.CategoryDTO;
import com.wunding.learn.excitation.service.admin.dto.CategoryListDTO;
import com.wunding.learn.excitation.service.admin.dto.CategoryPageDTO;
import com.wunding.learn.excitation.service.admin.dto.CategorySaveDTO;
import com.wunding.learn.excitation.service.admin.query.CategoryListQuery;
import com.wunding.learn.excitation.service.admin.query.CategoryPageQuery;
import com.wunding.learn.excitation.service.dao.CategorysDao;
import com.wunding.learn.excitation.service.mapper.AwardMapper;
import com.wunding.learn.excitation.service.model.Award;
import com.wunding.learn.file.api.component.ExportComponent;
import com.wunding.learn.file.api.constant.ExportBizType;
import com.wunding.learn.file.api.constant.ExportFileNameEnum;
import com.wunding.learn.file.api.dto.AbstractExportDataDTO;
import com.wunding.learn.file.api.dto.IExportDataDTO;
import com.wunding.learn.user.api.service.OrgFeign;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @since 2022/6/21
 */
@Slf4j
@Service("excitationCustomerCategoryService")
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class ExcitationCustomerCategoryService {

    private static final String TAG_SEPARATOR = "/";
    private final OrgFeign orgFeign;
    private final ICategorysService categorysService;
    private final AwardMapper awardMapper;
    private final ExportComponent exportComponent;
    @Resource(name = "awardCategorysDao")
    private CategorysDao categorysDao;

    public PageInfo<CategoryPageDTO> queryPage(CategoryPageQuery query) {
        PageInfo<CategoryPageDTO> resultPageInfo = new PageInfo<>();
        Integer pageSize = query.getPageSize();
        Integer pageNo = query.getPageNo();
        String userId = UserThreadContext.getUserId();
        query.setCurrentUserId(userId);
        query.setCurrentOrgId(UserThreadContext.getOrgId());

        PageInfo<Categorys> sqlPageInfo = PageMethod.startPage(pageNo, pageSize)
            .doSelectPageInfo(() -> awardMapper.getCategoryPage(query));

        List<Categorys> dataList = sqlPageInfo.getList();
        if (CollectionUtils.isEmpty(dataList)) {
            resultPageInfo.setList(new ArrayList<>());
            return resultPageInfo;
        }

        // 获取上级分类id
        Set<String> parentIdSet = dataList.stream().map(Categorys::getParentId).filter(StringUtils::isNotBlank)
            .collect(Collectors.toSet());
        List<Categorys> parentCategoryList =
            CollectionUtils.isEmpty(parentIdSet) ? new ArrayList<>() : categorysService.listByIds(parentIdSet);
        Map<String, String> parentCategoryMap = parentCategoryList.stream()
            .collect(Collectors.toMap(Categorys::getId, Categorys::getCategoryName));

        List<CategoryPageDTO> libraryCategoryListDTOList = dataList.stream().map(
                category -> new CategoryPageDTO().setCategoryName(category.getCategoryName())
                    .setIsAvailable(category.getIsAvailable())
                    .setSortNo(category.getSortNo())
                    .setParentId(category.getParentId())
                    .setParentCategoryName(parentCategoryMap.get(category.getParentId()))
                    .setId(category.getId()))
            .sorted(Comparator.comparingInt(CategoryPageDTO::getSortNo)).collect(Collectors.toList());

        BeanUtils.copyProperties(sqlPageInfo, resultPageInfo);
        resultPageInfo.setList(libraryCategoryListDTOList);

        return resultPageInfo;
    }

    public List<CategoryListDTO> queryList(CategoryListQuery query) {
        String userId = UserThreadContext.getUserId();
        Set<String> userManageAreaOrgIdList = orgFeign.findUserManageAreaLevelPath(userId);
        query.setUserManagement(userManageAreaOrgIdList);
        query.setCurrentOrgId(UserThreadContext.getOrgId());
        query.setCurrentUserId(UserThreadContext.getUserId());
        List<Categorys> dataList = awardMapper.getCategoryList(query);

        if (CollectionUtils.isEmpty(dataList)) {
            return new ArrayList<>();
        }

        Map<String, List<CategoryListDTO>> regionMap = dataList.stream().map(
                category -> new CategoryListDTO()
                    .setCategoryName(category.getCategoryName())
                    .setParentId(category.getParentId())
                    .setAvailable(category.getIsAvailable())
                    .setSortNo(category.getSortNo())
                    .setId(category.getId())
            ).sorted(Comparator.comparingInt(CategoryListDTO::getSortNo))
            .collect(Collectors.groupingBy(CategoryListDTO::getParentId, Collectors.toList()));

        List<CategoryListDTO> resultList = new ArrayList<>(regionMap.size());
        // 封装树形结构并塞进resultList数组中
        regionMap.forEach((parentId, collect) -> {
            if (parentId.equals(StringUtils.EMPTY)) {
                resultList.addAll(collect);
            }
            collect.forEach(item -> item.setSubCategorys(regionMap.get(item.getId())));
        });

        return resultList;
    }

    public CategoryDTO getCategoryById(String id) {
        Categorys category = categorysService.getById(id);
        CategoryDTO dto = new CategoryDTO();
        BeanUtils.copyProperties(category, dto);
        return dto;
    }

    public void saveCategory(CategorySaveDTO saveDTO) {
        String id = StringUtil.newId();
        Integer categoryLevel = 1;
        String levelPath = TAG_SEPARATOR + id + TAG_SEPARATOR;

        Categorys category = new Categorys();
        saveDTO.setId(id);
        BeanUtils.copyProperties(saveDTO, category);
        category.setOrgId(UserThreadContext.getOrgId());

        buildCategory(category, categoryLevel, levelPath);

        categorysDao.saveCategory(category);
    }

    public void updateCategory(String id, CategorySaveDTO saveDTO) {
        // 获取旧数据
        Categorys dbCategory = categorysService.getById(id);
        Integer categoryLevel = 1;
        String levelPath = TAG_SEPARATOR + id + TAG_SEPARATOR;

        Categorys category = new Categorys();
        BeanUtils.copyProperties(saveDTO, category);
        category.setId(id);
        category.setOrgId(UserThreadContext.getOrgId());

        if (!Objects.equals(dbCategory.getParentId(), saveDTO.getParentId())) {
            buildCategory(category, categoryLevel, levelPath);
        }

        categorysDao.updateCategory(category);
    }

    public void availableCategory(String ids, Integer availableStatus) {
        List<String> idList = TranslateUtil.translateBySplit(ids, String.class);
        if (CollectionUtils.isEmpty(idList)) {
            log.error("excitation_available_category_ids_must_be_not_null");
            return;
        }
        // 如果是启用，跳过检查
        if (Objects.equals(availableStatus, GeneralJudgeEnum.NEGATIVE.getValue())) {
            disableCheck(idList, "禁用");
        }
        // 禁用
        categorysService.isAvailable(ids, availableStatus);
    }

    public void deleteCategory(String ids) {
        List<String> idList = TranslateUtil.translateBySplit(ids, String.class);
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }
        List<Award> awards = awardMapper.selectList(new LambdaQueryWrapper<Award>().in(Award::getCategoryId, idList));
        if (!CollectionUtils.isEmpty(awards)) {
            throw new BusinessException(ExcitationErrorNoEnum.ERR_CATEGORY_BEEN_USED_BY_AWARD);
        }
        categorysService.deleteCategory(ids);
    }

    /**
     * 禁用/删除检查
     *
     * @param idList     id列表
     * @param actionName 启用/禁用
     */
    private void disableCheck(List<String> idList, String actionName) {
        // 校验分类是否被引用,如果被引用直接抛出异常
        Long awardCount = awardMapper.selectCount(new LambdaQueryWrapper<Award>().in(Award::getCategoryId, idList));
        if (Objects.nonNull(awardCount) && awardCount.compareTo(0L) > 0) {
            throw new BusinessException(ExcitationErrorNoEnum.ERR_CATEGORY_BEEN_USED_BY_AWARD2, null, actionName);
        }
        long childCount = categorysService.count(
            new LambdaQueryWrapper<Categorys>().in(Categorys::getParentId, idList));
        if (childCount > 0L) {
            throw new BusinessException(ExcitationErrorNoEnum.ERR_CATEGORY_BEEN_USED_BY_CHILD, null, actionName);
        }
        // 校验分类是否为系统初始化数据,如果是系统初始化数据直接抛出异常
        Long systemInitCount = categorysService.getBaseMapper().selectCount(
            new LambdaQueryWrapper<Categorys>().in(Categorys::getId, idList).eq(Categorys::getSysDefined, 1));
        if (Objects.nonNull(systemInitCount) && systemInitCount.compareTo(0L) > 0) {
            throw new BusinessException(ExcitationErrorNoEnum.ERR_CATEGORY_BEEN_SYSTEM_INIT_CODE, null, actionName);
        }
    }

    /**
     * 设置分类的等级和层级属性
     *
     * @param category      类别
     * @param categoryLevel 类别级别
     * @param levelPath     水平路径
     */
    private void buildCategory(Categorys category, Integer categoryLevel, String levelPath) {
        if (StringUtils.isNotBlank(category.getParentId())) {
            Categorys parentCategory = categorysService.getById(category.getParentId());
            if (Objects.nonNull(parentCategory)) {
                Integer parentLevel = parentCategory.getCategoryLevel();
                if (Objects.nonNull(parentLevel)) {
                    categoryLevel = parentLevel + 1;
                }

                String parentLevelPath = parentCategory.getLevelPath();
                if (StringUtils.isNotBlank(parentLevelPath)) {
                    levelPath = parentLevelPath + category.getId() + TAG_SEPARATOR;
                }
            }
        }
        category.setCategoryLevel(categoryLevel);
        category.setLevelPath(levelPath);
    }

    /**
     * 这里用激励自己的导出，因为激励的category表的 category_level是会表示当前层级，与其他模块的分类不同，其他模块永远是1 也不去更新这个字段
     *
     * @param query 查询
     */
    @Async("exportTaskThreadPool")
    public void exportData(CategoryPageQuery query) {
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<ExcitationCustomerCategoryService, CategoryPageDTO>(
            query) {

            @Override
            protected ExcitationCustomerCategoryService getBean() {
                return SpringUtil.getBean(ExcitationCustomerCategoryService.class);
            }

            @Override
            protected PageInfo<CategoryPageDTO> getPageInfo() {
                return getBean().queryPage((CategoryPageQuery) pageQueryDTO);
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.AwardCategory;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.AwardCategory.getType();
            }

        };

        exportComponent.exportRecord(exportDataDTO);
    }
}
