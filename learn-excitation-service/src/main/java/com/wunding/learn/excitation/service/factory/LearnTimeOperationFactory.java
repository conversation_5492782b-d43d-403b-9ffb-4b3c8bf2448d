package com.wunding.learn.excitation.service.factory;

import com.wunding.learn.common.util.bean.SpringUtil;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/11/25
 */
@Component
public class LearnTimeOperationFactory implements ExcitationOperationAbstractFactory {

    @Override
    public IOperationAdd newOperationAdd() {
        return SpringUtil.getBean(LearnTimeOperationAdd.class);
    }

    @Override
    public IOperationSubtract newOperationSubtract() {
        return SpringUtil.getBean(LearnTimeOperationSubtract.class);
    }
}
