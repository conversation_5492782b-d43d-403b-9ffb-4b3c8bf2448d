package com.wunding.learn.excitation.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.common.constant.excitation.ExcitationErrorNoEnum;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.enums.other.SystemConfigCodeEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.excitation.service.client.dto.ExcitationEventObjectDTO;
import com.wunding.learn.excitation.service.mapper.UserLearnTimeMapper;
import com.wunding.learn.excitation.service.model.UserLearnTime;
import com.wunding.learn.excitation.service.service.IExcitationOperation;
import com.wunding.learn.excitation.service.service.IUserLearnTimeService;
import com.wunding.learn.user.api.service.ParaFeign;
import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.Objects;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p> 用户学时表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi</a>
 * @since 2022-08-08
 */
@Slf4j
@Service("userLearnTimeService")
public class UserLearnTimeServiceImpl extends ServiceImpl<UserLearnTimeMapper, UserLearnTime> implements
    IUserLearnTimeService, IExcitationOperation {

    @Resource
    ParaFeign paraFeign;

    @Override
    public void saveOrUpdateUserLearnTime(ExcitationEventObjectDTO eventObject) {
        long count = count(
            new LambdaQueryWrapper<UserLearnTime>().eq(UserLearnTime::getUserId, eventObject.getUserId()));
        if (count == 0) {
            // 新增
            save(new UserLearnTime().setUserId(eventObject.getUserId()).setNum(eventObject.getScore())
                .setAvailableNum(eventObject.getScore()).setOriginNum(BigDecimal.valueOf(0)));
        } else {
            // 更新
            LambdaUpdateWrapper<UserLearnTime> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(UserLearnTime::getUserId, eventObject.getUserId());
            String sql = eventObject.getOperateType() == 0 ? "num = num + " + eventObject.getScore()
                + ", available_num = available_num + " + eventObject.getScore()
                + ", convertible_num = convertible_num + " + eventObject.getScore()
                : "available_num = available_num - " + eventObject.getScore() + ", convertible_num = convertible_num - "
                    + eventObject.getScore();
            updateWrapper.setSql(sql);
            update(updateWrapper);
        }
    }

    @Override
    public BigDecimal getLearnTime(String userId) {
        LambdaQueryWrapper<UserLearnTime> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserLearnTime::getUserId, userId);
        UserLearnTime one = getOne(queryWrapper);
        if (one != null) {
            return one.getNum();
        }
        return BigDecimal.ZERO;
    }

    @Override
    public UserLearnTime initUserLearnTime(String userId, BigDecimal fund) {
        return initUserLearnTime(userId, fund, GeneralJudgeEnum.NEGATIVE.getValue());
    }

    @Override
    public UserLearnTime initUserLearnTime(String userId, BigDecimal fund, Integer isExchange) {
        UserLearnTime learnTime = getById(userId);
        if (Objects.nonNull(learnTime)) {
            return learnTime;
        }
        learnTime = new UserLearnTime().setUserId(userId).setNum(fund).setAvailableNum(fund).setOriginNum(fund);
        save(learnTime);
        learnTime = getById(userId);
        return learnTime;
    }

    @Override
    public BigDecimal getLearnAvailavleTime(String userId) {
        LambdaQueryWrapper<UserLearnTime> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserLearnTime::getUserId, userId);
        UserLearnTime one = getOne(queryWrapper);
        if (one != null) {
            return one.getAvailableNum();
        }
        return BigDecimal.ZERO;
    }

    @Override
    public UserLearnTime getDataById(String userId) {
        return Optional.ofNullable(getById(userId)).orElse(initUserLearnTime(userId, BigDecimal.ZERO));
    }

    @Override
    public BigDecimal add(String userId, BigDecimal amount) {
        return add(userId, amount, GeneralJudgeEnum.NEGATIVE.getValue());
    }

    /**
     * 根据系统参数设置,是否是关闭可兑换区分配置 SYSTEM_CONFIG_CODE_912 1，关闭可兑换区分配置，强制修改可兑换积分成为不可兑换积分入库 2，开启可兑换区分配置，根据传入的是否可兑换积分入库
     *
     * @param userId
     * @param amount
     * @param isExchange
     * @return
     */
    @Override
    public BigDecimal add(String userId, BigDecimal amount, Integer isExchange) {
        //根据系统参数设置,是否是关闭可兑换区分配置
        Integer isClose = Integer.valueOf(
            paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_912.getCode()));
        if (Objects.equals(isClose, GeneralJudgeEnum.CONFIRM.getValue())) {
            //1，关闭可兑换区分配置，强制修改可兑换积分成为不可兑换积分入库
            isExchange = GeneralJudgeEnum.NEGATIVE.getValue();
        } else {
            //2，开启可兑换区分配置，根据传入的是否可兑换积分入库
        }
        return baseAdd(userId, amount, isExchange);
    }

    @Override
    public BigDecimal baseAdd(String userId, BigDecimal amount, Integer isExchange) {
        UserLearnTime learnTime = initUserLearnTime(userId, BigDecimal.ZERO, isExchange);
        UserLearnTime userLearnTime = new UserLearnTime().setUserId(userId).setNum(learnTime.getNum().add(amount))
            .setAvailableNum(learnTime.getAvailableNum().add(amount));
        if (Objects.equals(isExchange, GeneralJudgeEnum.CONFIRM.getValue())) {
            userLearnTime.setConvertibleNum(learnTime.getConvertibleNum().add(amount));
        }
        updateById(userLearnTime);
        return userLearnTime.getAvailableNum();
    }

    /**
     * 根据系统参数设置,是否是关闭可兑换区分配置 SYSTEM_CONFIG_CODE_912 1，关闭可兑换区分配置，优先消耗可兑换激励值，可兑换激励值不够时，再继续消耗不可兑换
     * 2，开启可兑换区分配置，优先消耗可兑换激励值，若是可兑换激励值不够时，直接报错
     *
     * @param userId
     * @param amount
     * @return
     */
    @Override
    public BigDecimal subtract(String userId, BigDecimal amount) {
        //根据系统参数设置,是否是关闭可兑换区分配置
        Integer isClose = Integer.valueOf(
            paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_912.getCode()));
        UserLearnTime learnTime = initUserLearnTime(userId, BigDecimal.ZERO);
        UserLearnTime userLearnTime;
        BigDecimal convertibleNum = learnTime.getConvertibleNum();
        BigDecimal convertibleNumAmount;

        if (Objects.equals(isClose, GeneralJudgeEnum.CONFIRM.getValue())) {
            //1，关闭可兑换区分配置，优先消耗可兑换激励值，可兑换激励值不够时，再继续消耗不可兑换
            if (learnTime.getAvailableNum().compareTo(amount) < 0) {
                throw new BusinessException(ExcitationErrorNoEnum.ERR_LEARN_TIME_NOT_ENOUGH);
            }
            convertibleNumAmount = learnTime.getConvertibleNum().compareTo(amount) < 0 ? new BigDecimal(0)
                : convertibleNum.subtract(amount);
        } else {
            //2，开启可兑换区分配置，优先消耗可兑换激励值，若是可兑换激励值不够时，直接报错
            if (learnTime.getAvailableNum().compareTo(amount) < 0
                || learnTime.getConvertibleNum().compareTo(amount) < 0) {
                throw new BusinessException(ExcitationErrorNoEnum.ERR_LEARN_TIME_NOT_ENOUGH);
            }
            convertibleNumAmount = convertibleNum.subtract(amount);
        }

        userLearnTime = new UserLearnTime().setUserId(userId)
            .setAvailableNum(learnTime.getAvailableNum().subtract(amount))
            .setConvertibleNum(convertibleNumAmount);
        updateById(userLearnTime);
        return userLearnTime.getAvailableNum();
    }

    @Override
    public BigDecimal getLearnConvertibleNum(String userId) {
        LambdaQueryWrapper<UserLearnTime> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserLearnTime::getUserId, userId);
        UserLearnTime one = getOne(queryWrapper);
        if (one != null) {
            return one.getConvertibleNum();
        }
        return BigDecimal.ZERO;
    }
}
