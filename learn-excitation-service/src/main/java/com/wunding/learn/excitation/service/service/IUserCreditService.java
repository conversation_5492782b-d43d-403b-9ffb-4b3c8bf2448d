package com.wunding.learn.excitation.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wunding.learn.excitation.service.client.dto.ExcitationEventObjectDTO;
import com.wunding.learn.excitation.service.model.UserCredit;
import java.math.BigDecimal;

/**
 * <p> 用户学分表 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi</a>
 * @since 2022-08-08
 */
public interface IUserCreditService extends IService<UserCredit> {

    /**
     * 保存或更新用户学分
     *
     * @param eventObject {@link ExcitationEventObjectDTO}
     */
    void saveOrUpdateUserCredit(ExcitationEventObjectDTO eventObject);

    /**
     * @param userId
     * @return
     */
    BigDecimal getUserCredit(String userId);

    /**
     * 获取可以兑换学分
     *
     * @param userId
     * @return
     */
    BigDecimal getUserConvertibleNumCredit(String userId);

    /**
     * 初始化用户学分
     *
     * @param userId
     * @param fund
     */
    UserCredit initUserCredit(String userId, BigDecimal fund, Integer isExchange);

    /**
     * 初始化用户学分
     *
     * @param userId
     * @param fund
     */
    UserCredit initUserCredit(String userId, BigDecimal fund);


    /**
     * getById的平替
     *
     * @param userId
     * @return
     */
    UserCredit getDataById(String userId);
}
