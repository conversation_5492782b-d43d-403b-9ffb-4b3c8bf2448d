package com.wunding.learn.excitation.service.factory;

import com.wunding.learn.common.enums.excitation.ExcitationTypeEnum;
import com.wunding.learn.common.enums.other.ExcitationOperationEnum;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.mq.dto.ExcitationTradeDTO;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.excitation.service.model.ExcitationTradeRecord;
import com.wunding.learn.excitation.service.service.IExcitationTradeRecordService;
import com.wunding.learn.excitation.service.service.IUserExperienceService;
import com.wunding.learn.excitation.service.service.impl.UserGoldCoinServiceImpl;
import com.wunding.learn.user.api.dto.UserDTO;
import com.wunding.learn.user.api.service.UserFeign;
import java.math.BigDecimal;
import java.util.Objects;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2022/11/24
 */
@Component
public class GoldCoinOperationAdd implements IOperationAdd {

    @Resource
    private UserGoldCoinServiceImpl goldCoinService;
    @Resource
    private IExcitationTradeRecordService tradeRecordService;
    @Resource
    private IUserExperienceService experienceService;
    @Resource
    private UserFeign userFeign;

    @Override
    public void compute(ExcitationTradeDTO dto) {
        // 交易数量
        BigDecimal amount = dto.getAmount();
        // 收受人ID
        String payeeId = dto.getPayeeId();
        // 增加学分
        BigDecimal availableAmount = goldCoinService.add(payeeId, amount);
        // 增加经验
        experienceService.add(payeeId, amount);
        // 增加交易记录
        ExcitationTradeRecord tradeRecord = new ExcitationTradeRecord().setId(StringUtil.newId())
            .setOperateNum(amount)
            .setCurrentNum(availableAmount)
            .setOperateType(ExcitationOperationEnum.INCREASE.getValue())
            .setTargetId(dto.getPayerId())
            .setUserId(payeeId)
            .setEventId(dto.getEventId())
            .setExcitationId(dto.getExcitationTypeEnum().getCode())
            .setSummary(dto.getSummary() + ExcitationOperationEnum.INCREASE.getSymbol() + amount
                + ExcitationTypeEnum.getNameByCode(dto.getExcitationTypeEnum().getCode()))
            .setIsExchange(GeneralJudgeEnum.CONFIRM.getValue());
        //交易的记录应该都是是否可兑换为1才行
        UserDTO payer = userFeign.getUserById(dto.getPayerId());
        if (Objects.nonNull(payer)) {
            tradeRecord.setTargetName(payer.getFullName());
        }
        tradeRecordService.save(tradeRecord);
    }
}
