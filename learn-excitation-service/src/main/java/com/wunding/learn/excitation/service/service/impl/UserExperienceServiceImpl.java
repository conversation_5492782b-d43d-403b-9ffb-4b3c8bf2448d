package com.wunding.learn.excitation.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.excitation.api.dto.UserTitleDTO;
import com.wunding.learn.excitation.service.client.dto.ExcitationEventObjectDTO;
import com.wunding.learn.excitation.service.mapper.UserExperienceMapper;
import com.wunding.learn.excitation.service.model.UserExperience;
import com.wunding.learn.excitation.service.service.IUserExperienceService;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p> 用户经验值表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi</a>
 * @since 2022-08-08
 */
@Slf4j
@Service("userExperienceService")
public class UserExperienceServiceImpl extends ServiceImpl<UserExperienceMapper, UserExperience> implements
    IUserExperienceService {

    @Override
    public void saveOrUpdateUserExperience(ExcitationEventObjectDTO eventObject) {
        long count = count(
            new LambdaQueryWrapper<UserExperience>().eq(UserExperience::getUserId, eventObject.getUserId()));
        if (count == 0) {
            // 新增
            save(new UserExperience().setUserId(eventObject.getUserId()).setNum(eventObject.getScore())
                .setRemainNum(eventObject.getScore()).setOriginNum(BigDecimal.valueOf(0)));
        } else {
            // 更新
            LambdaUpdateWrapper<UserExperience> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(UserExperience::getUserId, eventObject.getUserId());
            String sql = eventObject.getOperateType() == 0 ? "num = num + " + eventObject.getScore()
                + ", remain_num = remain_num + " + eventObject.getScore()
                : "remain_num = remain_num - " + eventObject.getScore();
            updateWrapper.setSql(sql);
            update(updateWrapper);
        }
    }

    @Override
    public Map<String, String> getUsersTitle(Collection<String> userIds) {
        return baseMapper.getUsersTitle(userIds).stream().collect(
            Collectors.toMap(UserTitleDTO::getUserId, item -> Optional.ofNullable(item.getTitle()).orElse(""),
                (key1, key2) -> key1));
    }

    @Override
    public UserExperience initUserExperience(String userId, BigDecimal value) {
        UserExperience experience = getById(userId);
        if (Objects.nonNull(experience)) {
            return experience;
        }
        experience = new UserExperience().setUserId(userId).setNum(value).setOriginNum(value).setRemainNum(value);
        save(experience);
        return experience;
    }

    @Override
    public BigDecimal add(String userId, BigDecimal amount) {
        UserExperience experience = initUserExperience(userId, BigDecimal.ZERO);
        UserExperience experienceUpdate = new UserExperience().setUserId(userId).setNum(experience.getNum().add(amount))
            .setRemainNum(experience.getRemainNum().add(amount));
        updateById(experienceUpdate);
        return experienceUpdate.getRemainNum();
    }

    @Override
    public BigDecimal getBalance(String userId) {
        LambdaQueryWrapper<UserExperience> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserExperience::getUserId, userId);
        UserExperience one = getOne(queryWrapper);
        if (one != null) {
            return one.getRemainNum();
        }
        return BigDecimal.valueOf(0.00);
    }

    @Override
    public UserExperience getDataById(String userId) {
        return Optional.ofNullable(getById(userId)).orElse(initUserExperience(userId, BigDecimal.ZERO));
    }

}
