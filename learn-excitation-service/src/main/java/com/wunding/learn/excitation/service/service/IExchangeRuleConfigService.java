package com.wunding.learn.excitation.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wunding.learn.excitation.service.admin.dto.ExchangeRuleConfigDTO;
import com.wunding.learn.excitation.service.client.dto.ExchangeConfig;
import com.wunding.learn.excitation.service.model.ExchangeRuleConfig;
import java.util.List;

/**
 * <p> 金币兑换配置 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi</a>
 * @since 2022-08-08
 */
public interface IExchangeRuleConfigService extends IService<ExchangeRuleConfig> {

    /**
     * 获取金币兑换规则数据对象列表
     *
     * @param systemType 体系分类
     * @return 金币兑换规则数据对象列表
     */
    List<ExchangeRuleConfigDTO> dataList(Integer systemType);

    /**
     * 批量额更新金币兑换规则
     *
     * @param configDTOList 金币兑换规则数据对象列表
     */
    void updateDataBatch(List<ExchangeRuleConfigDTO> configDTOList);

    /**
     * 是否去兑换金币
     *
     * @return
     */
    Integer isExchange(Integer systemType);

    /**
     * @return
     */
    List<ExchangeConfig> getExchangeConfigs(Integer systemType);

    /**
     * 获取相应配置
     *
     * @param code
     * @return
     */
    ExchangeConfig getExchangeConfigById(String code, Integer systemType);

    /**
     * 自动兑换金币
     */
    void autoExchangeGoldCoin();
}
