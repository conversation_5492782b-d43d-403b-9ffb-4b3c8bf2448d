package com.wunding.learn.excitation.service;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.micrometer.core.instrument.MeterRegistry;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryCustomizer;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.cloud.openfeign.FeignAutoConfiguration;
import org.springframework.cloud.openfeign.clientconfig.HttpClient5FeignConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * <AUTHOR>
 * @date 2022/8/5
 */
@SpringBootApplication
@ComponentScan(value = "com.wunding.learn")
@EnableFeignClients(basePackages = {
    "com.wunding.learn.user.api.*",
    "com.wunding.learn.file.api.*",
    "com.wunding.learn.course.api.*",
    "com.wunding.learn.forum.api.*",
    "com.wunding.learn.certification.api.*",
    "com.wunding.learn.exam.api.*",
    "com.wunding.learn.example.api.*",
    "com.wunding.learn.live.api.*",
    "com.wunding.learn.survey.api.*",
    "com.wunding.learn.info.api.*",
    "com.wunding.learn.evaluation.api.*",
    "com.wunding.learn.market.api.*",
    "com.wunding.learn.project.api.*",
    "com.wunding.learn.operation.api.*",
    "com.wunding.learn.payment.api.*"
})
@ImportAutoConfiguration({FeignAutoConfiguration.class, HttpClient5FeignConfiguration.class})
@EnableCaching
@EnableTransactionManagement
@EnableAsync
@MapperScan(value = "com.wunding.learn.**.mapper")
@JsonIgnoreProperties(ignoreUnknown = true)
public class LearnExcitationServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(LearnExcitationServiceApplication.class, args);
    }

    @Bean
    public MeterRegistryCustomizer<MeterRegistry> configurer(
        @Value("${spring.application.name}") String applicationName) {
        return (registry) -> registry.config().commonTags("application", applicationName);
    }
}
