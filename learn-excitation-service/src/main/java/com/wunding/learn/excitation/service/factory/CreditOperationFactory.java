package com.wunding.learn.excitation.service.factory;

import com.wunding.learn.common.util.bean.SpringUtil;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/11/25
 */
@Component
public class CreditOperationFactory implements ExcitationOperationAbstractFactory {

    @Override
    public IOperationAdd newOperationAdd() {
        return SpringUtil.getBean(CreditOperationAdd.class);
    }

    @Override
    public IOperationSubtract newOperationSubtract() {
        return SpringUtil.getBean(CreditOperationSubtract.class);
    }
}
