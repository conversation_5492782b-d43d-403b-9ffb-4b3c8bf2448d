package com.wunding.learn.excitation.service.component;

import com.wunding.learn.common.viewlimit.constant.LimitTable;
import com.wunding.learn.common.viewlimit.service.impl.BaseViewLimitServiceImpl;
import com.wunding.learn.excitation.service.model.AwardViewLimit;
import com.wunding.learn.excitation.service.service.IAwardViewLimitService;
import java.util.List;
import jakarta.annotation.Resource;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2022/10/27
 */
@Component("awardViewLimitComponent")
public class AwardViewLimitComponent extends BaseViewLimitServiceImpl<AwardViewLimit> implements CommandLineRunner {

    @Resource
    private IAwardViewLimitService awardViewLimitService;

    public AwardViewLimitComponent() {
        super(LimitTable.AWARD_VIEW_LIMIT);
    }

    @Override
    public void saveBatch(List<AwardViewLimit> baseViewLimits) {
        awardViewLimitService.saveBatch(baseViewLimits);
    }

    @Override
    public void removeBatchByIds(List<AwardViewLimit> baseViewLimits) {
        awardViewLimitService.removeBatchByIds(baseViewLimits);
    }

    @Override
    public void run(String... args) {
        // 无任务代码实现，注释 清除警告
    }
}
