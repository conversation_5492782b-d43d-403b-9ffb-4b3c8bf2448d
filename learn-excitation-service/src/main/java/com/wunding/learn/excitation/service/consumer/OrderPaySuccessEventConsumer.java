package com.wunding.learn.excitation.service.consumer;

import com.rabbitmq.client.Channel;
import com.wunding.learn.common.constant.mq.MqConst;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.enums.excitation.ExcitationEventEnum;
import com.wunding.learn.common.enums.excitation.ExcitationTypeEnum;
import com.wunding.learn.common.enums.other.ExcitationOperationEnum;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.mq.dto.PaymentOrderInfoDTO;
import com.wunding.learn.common.mq.event.OrderPaySuccessEvent;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.excitation.service.model.ExcitationTradeRecord;
import com.wunding.learn.excitation.service.model.UserGoldCoin;
import com.wunding.learn.excitation.service.service.IExcitationTradeRecordService;
import com.wunding.learn.excitation.service.service.impl.UserGoldCoinServiceImpl;
import java.io.IOException;
import java.math.BigDecimal;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class OrderPaySuccessEventConsumer {
    /**
     * 队列名称
     */
    public static final String ORDER_PAY_SUCCESS_EVENT_QUEUE = "orderPaySuccessEventQueue";

    @Resource
    private UserGoldCoinServiceImpl goldCoinService;

    @Resource
    private IExcitationTradeRecordService tradeRecordService;

    @RabbitListener(
        bindings = @QueueBinding(
            value = @Queue(value = ORDER_PAY_SUCCESS_EVENT_QUEUE),
            exchange = @Exchange(value = MqConst.EXCHANGE_TOPIC, type = ExchangeTypes.TOPIC),
            key = MqConst.ORDER_PAY_SUCCESS_EVENT_ROUTING_KEY
        ),
        id = "orderPaySuccessEventHandler"
    )
    public void orderPaySuccessEventHandler(@Payload OrderPaySuccessEvent event,
        @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag, Channel channel) throws IOException {
        log.info("orderPaySuccessEventHandler orderPaySuccessEvent:{}", JsonUtil.objToJson(event));
        PaymentOrderInfoDTO orderInfoDTO = event.getOrderInfoDTO();
        UserThreadContext.setTenantId(event.getTenantId());
        UserGoldCoin userGoldCoin = goldCoinService.getDataById(orderInfoDTO.getPayerId());

        BigDecimal num = BigDecimal.valueOf(orderInfoDTO.getGoodsNum());

        // 增加金币
        BigDecimal res = goldCoinService.baseAdd(userGoldCoin.getUserId(), num, GeneralJudgeEnum.CONFIRM.getValue());

        // 增加金币明细
        ExcitationTradeRecord tradeRecord = new ExcitationTradeRecord();
        tradeRecord.setId(StringUtil.newId())
            .setExcitationId(orderInfoDTO.getGoodsId())
            .setEventId(ExcitationEventEnum.BUY_GOLD_COIN.name())
            .setSummary("购买金币+" + orderInfoDTO.getGoodsNum())
            .setOrderId(orderInfoDTO.getOrderId())
            .setOperateNum(num)
            .setCurrentNum(res)
            .setTradeType(ExcitationTypeEnum.GOLD_COIN.getCode())
            .setTargetId(ExcitationTypeEnum.GOLD_COIN.getCode())
            .setTargetName(ExcitationTypeEnum.GOLD_COIN.getName())
            .setOperateType(ExcitationOperationEnum.INCREASE.getValue())
            .setIsExchange(GeneralJudgeEnum.CONFIRM.getValue())
            .setUserId(orderInfoDTO.getPayerId())
            .setIsRefundable(orderInfoDTO.getIsRefundable());
        tradeRecordService.save(tradeRecord);
        UserThreadContext.remove();
        channel.basicAck(deliveryTag, true);
    }

}
