package com.wunding.learn.excitation.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.excitation.service.mapper.ExcitationSettingsMapper;
import com.wunding.learn.excitation.service.model.ExcitationSettings;
import com.wunding.learn.excitation.service.service.IExcitationSettingsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p> 激励设置表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi</a>
    * @since 2022-08-08
 */
@Slf4j
@Service("excitationSettingsService")
public class ExcitationSettingsServiceImpl extends ServiceImpl<ExcitationSettingsMapper, ExcitationSettings> implements IExcitationSettingsService {

}
