package com.wunding.learn.excitation.service.service.impl;

import static com.wunding.learn.common.constant.excitation.ExcitationErrorNoEnum.ERR_NO_ALLOWED_CONFIG_GOLD;

import com.alibaba.excel.util.StringUtils;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.dto.pay.RequestPaymentDTO;
import com.wunding.learn.common.dto.pay.SubmitOrderDTO;
import com.wunding.learn.common.enums.other.SystemConfigCodeEnum;
import com.wunding.learn.common.enums.payment.GoodsTypeEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.excitation.service.service.IExcitationService;
import com.wunding.learn.payment.api.dto.PreOrderDTO;
import com.wunding.learn.payment.api.service.OrderFeign;
import com.wunding.learn.user.api.dto.UserDTO;
import com.wunding.learn.user.api.service.ParaFeign;
import com.wunding.learn.user.api.service.UserFeign;
import java.math.BigDecimal;
import java.math.RoundingMode;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/8/21
 */
@Slf4j
@Service("excitationService")
public class ExcitationServiceImpl implements IExcitationService {

    @Resource
    private ParaFeign paraFeign;

    @Resource
    private OrderFeign orderFeign;

    @Resource
    private UserFeign userFeign;

    @Override
    public RequestPaymentDTO placeOrder(SubmitOrderDTO orderDTO) {
        UserDTO userDTO = userFeign.getUserById(UserThreadContext.getUserId());
        BigDecimal unitPrice = getGoldCoinUnitPrice();
        PreOrderDTO preOrderDTO = new PreOrderDTO();
        BigDecimal amount = unitPrice.multiply(BigDecimal.valueOf(orderDTO.getGoodsNum()));
        BeanUtils.copyProperties(orderDTO, preOrderDTO);
        preOrderDTO.setGoodsName("金币")
            .setGoodsType(GoodsTypeEnum.EXCITATION.name())
            .setUserId(userDTO.getId())
            .setOpenId(userDTO.getOpenId())
            .setPayableAmount(amount)
            .setTotalAmount(amount)
            .setUnitPrice(unitPrice);
        return orderFeign.prePlaceOrder(preOrderDTO);
    }

    @Override
    public BigDecimal getGoldCoinUnitPrice() {
        // 取购买金币配置
        String paraValue = paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_909.getCode());
        if (StringUtils.isBlank(paraValue)) {
            throw new BusinessException(ERR_NO_ALLOWED_CONFIG_GOLD);
        }
        return BigDecimal.ONE.divide(new BigDecimal(paraValue), 2, RoundingMode.UP);
    }


}
