package com.wunding.learn.excitation.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wunding.learn.excitation.service.model.GameOption;

/**
 * <p>  服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">gao<PERSON></a>
    * @since 2022-08-08
 */
public interface IGameOptionService extends IService<GameOption> {


    /**
     * 奖品数量-1
     * @param optionId
     */
    void updateAwardOptions(String optionId);
}
