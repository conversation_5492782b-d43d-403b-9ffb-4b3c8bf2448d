package com.wunding.learn.excitation.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.excitation.service.mapper.ExchangeRecordMapper;
import com.wunding.learn.excitation.service.model.ExchangeRecord;
import com.wunding.learn.excitation.service.service.IExchangeRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p> 金币兑换记录 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi</a>
    * @since 2022-08-08
 */
@Slf4j
@Service("exchangeRecordService")
public class ExchangeRecordServiceImpl extends ServiceImpl<ExchangeRecordMapper, ExchangeRecord> implements IExchangeRecordService {

}
