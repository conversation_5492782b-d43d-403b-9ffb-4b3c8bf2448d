package com.wunding.learn.excitation.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.excitation.service.mapper.ExcitationSchemeMapper;
import com.wunding.learn.excitation.service.model.ExcitationScheme;
import com.wunding.learn.excitation.service.service.IExcitationSchemeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p> 激励方案表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi</a>
    * @since 2022-08-08
 */
@Slf4j
@Service("excitationSchemeService")
public class ExcitationSchemeServiceImpl extends ServiceImpl<ExcitationSchemeMapper, ExcitationScheme> implements IExcitationSchemeService {

}
