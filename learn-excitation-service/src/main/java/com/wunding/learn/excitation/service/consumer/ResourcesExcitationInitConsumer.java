package com.wunding.learn.excitation.service.consumer;

import com.rabbitmq.client.Channel;
import com.wunding.learn.common.constant.mq.MqConst;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.mq.event.excitation.ExcitationInitMqEvent;
import com.wunding.learn.common.mq.util.ConsumerAckUtil;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.excitation.service.service.IExcitationConfigUserService;
import java.io.IOException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

/**
 * 各个资源激励配置规则初始化消费者
 *
 * <AUTHOR>
 * @date 2022/11/6
 */
@Component
@Slf4j
public class ResourcesExcitationInitConsumer {

    /**
     * 队列名称
     */
    public static final String RESOURCES_EXCITATION_CONFIG_INIT_QUEUE = "resourceExcitationConfigInitQueue";
    @Resource
    private IExcitationConfigUserService excitationConfigUserService;

    @RabbitListener(
            bindings = @QueueBinding(
                    value = @Queue(value = RESOURCES_EXCITATION_CONFIG_INIT_QUEUE),
                    exchange = @Exchange(value = MqConst.EXCHANGE_TOPIC, type = ExchangeTypes.TOPIC),
                    key = MqConst.RESOURCES_EXCITATION_INIT_ROUTING_KEY
            ),
            id = "resourcesExcitationConfigInitHandler"
    )
    public void resourcesExcitationConfigInitHandler(@Payload ExcitationInitMqEvent event,
                                                     @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag, Channel channel) throws IOException {

        log.info("event: {}", JsonUtil.objToJson(event));
        try {
            UserThreadContext.setTenantId(event.getTenantId());
            excitationConfigUserService.initResourceConfigUser(event.getResourceConfigInitDTO());
            UserThreadContext.remove();
            ConsumerAckUtil.basicAck(event,channel,deliveryTag, false);
        } catch (IOException e) {
            log.error("resourcesExcitationConfigInitHandler ",e);
            ConsumerAckUtil.basicNack(event,channel,deliveryTag, false,true);
        }
    }

}
