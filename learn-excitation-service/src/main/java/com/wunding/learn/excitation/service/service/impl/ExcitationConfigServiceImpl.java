package com.wunding.learn.excitation.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.excitation.service.mapper.ExcitationConfigMapper;
import com.wunding.learn.excitation.service.model.ExcitationConfig;
import com.wunding.learn.excitation.service.service.IExcitationConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p> 激励配置表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">gq</a>
    * @since 2023-04-26
 */
@Slf4j
@Service("excitationConfigService")
public class ExcitationConfigServiceImpl extends ServiceImpl<ExcitationConfigMapper, ExcitationConfig> implements IExcitationConfigService {

}
