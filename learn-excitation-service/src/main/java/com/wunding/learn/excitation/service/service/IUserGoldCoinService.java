package com.wunding.learn.excitation.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.dto.ImportResultDTO;
import com.wunding.learn.excitation.service.admin.dto.UserGoldCoinImportDTO;
import com.wunding.learn.excitation.service.admin.dto.UserGoldCoinPageDTO;
import com.wunding.learn.excitation.service.admin.dto.UserGoldCoinRefundDTO;
import com.wunding.learn.excitation.service.admin.query.UserGoldCoinPageQuery;
import com.wunding.learn.excitation.service.client.dto.ExcitationEventObjectDTO;
import com.wunding.learn.excitation.service.client.dto.MallUserInfoDTO;
import com.wunding.learn.excitation.service.client.dto.UserGoldCoinDTO;
import com.wunding.learn.excitation.service.model.UserGoldCoin;
import java.math.BigDecimal;
import org.springframework.scheduling.annotation.Async;

/**
 * <p> 用户金币表 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi</a>
 * @since 2022-08-08
 */
public interface IUserGoldCoinService extends IService<UserGoldCoin> {

    /**
     * 保存或更新用户金币
     *
     * @param eventObject {@link ExcitationEventObjectDTO}
     */
    void saveOrUpdateUserGoldCoin(ExcitationEventObjectDTO eventObject);

    /**
     * @param userId
     * @return
     */
    BigDecimal getUserGold(String userId);

    /**
     * 初始化用户金币信息
     *
     * @param userId
     * @param fund
     */
    UserGoldCoin initUserGoldCoin(String userId, BigDecimal fund);

    /**
     * 初始化用户金币信息
     *
     * @param userId
     * @param fund
     * @param isExchange
     */
    UserGoldCoin initUserGoldCoin(String userId, BigDecimal fund, Integer isExchange);

    /**
     * 获取用户金币情况以及用户信息
     *
     * @return
     */
    MallUserInfoDTO getUserMallInfo();

    /**
     * 查询数据
     *
     * @param userId
     * @return
     */
    UserGoldCoin getDataById(String userId);

    /**
     * 获取当前用户金币数
     *
     * @return
     */
    UserGoldCoinDTO getCurrentUserGoldCoin();

    /**
     * 用户金币分页数据
     *
     * @param query
     * @return
     */
    PageInfo<UserGoldCoinPageDTO> pageUserGoldCoin(UserGoldCoinPageQuery query);


    /**
     * 导入金币
     *
     * @param dto
     * @return
     */
    ImportResultDTO importData(UserGoldCoinImportDTO dto);


    void refund(UserGoldCoinRefundDTO refundDTO);


    @Async
    void exportData(UserGoldCoinPageQuery query);
}
