# 应用服务WEB访问端口
server:
  port: 28014

spring:

  # 数据库设置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************
    username: wdxuexi
    password: learnTest


  #redis
  data:
    redis:
      #Redis 服务器地址
      host: redis
      #Redis服务器连接端口
      port: 6379
      #Redis数据库索引(默认为0)
      database: 5
      # Redis服务器连接密码
      password: M0eHdhs9kk4VKLjRAb7J41
      # 连接超时时间(毫秒)
      timeout: 10000


  # rabbitmq 配置
  rabbitmq:
    host: rabbitmq
    port: 5672
    virtual-host: /
    username: guest
    password: guest


management:
  tracing:
    sampling:
      probability: 1.0
    enabled: true
    propagation:
      type: B3
  otlp:
    metrics:
      export:
        enabled: true
  zipkin:
    tracing:
      endpoint: http://jaeger-collector.observability.svc:14250

opentelemetry:
  traces:
    exporter: otlp
  service:
    name: ${spring.application.name}
  jaeger:
    endpoint: http://jaeger-collector.observability.svc:4317
    protocol: grpc
  instrumentation:
    feign:
      enabled: true
    spring-webmvc:
      enabled: true


xxl:
  job:
    admin:
      addresses: http://job:8080/xxl-job-admin
    executor:
      appName: ${spring.application.name}
      ip:
      port: 9999
      logPath: /data/applogs/xxl-job/jobhandler
      logRetentionDays: -1
    accessToken: vopeqn943epfaspdf

learn:
  service:
    learn-file-service: "http://file:8080"
    learn-user-service: "http://user:8080"
    learn-project-service: "http://project:8080"
    learn-course-service: "http://course:8080"
    learn-certification-service: "http://certification:8080"
    learn-forum-service: "http://forum:8080"
    learn-exam-service: "http://exam:8080"
    learn-example-service: "http://example:8080"
    learn-live-service: "http://live:8080"
    learn-survey-service: "http://survey:8080"
    learn-info-service: "http://info:8080"
    learn-evaluation-service: "http://evaluation:8080"
    learn-market-service: "http://market:8080"
    learn-train-service: "http://train:8080"
    learn-operation-service: "http://operation:8080"
    learn-payment-service: "http://payment:8080"
app:
  signKey: bladexisapowerfulmicroservicearchitectureupgradedandoptimizedfromacommercialproject
  single:
    - api


#seata:
#  client:
#    undo:
#      # kryo序列化工具
#      log-serialization: kryo
#  config:
#    type: file
#  application-id: ${spring.application.name}
#  # enable-auto-data-source-proxy: false
#  registry:
#    type: file
#  service:
#    grouplist:
#      default: seata:8091
#    vgroup-mapping:
#      springboot-seata-group: default
#  # seata 事务组编号 用于TC集群名
#  tx-service-group: springboot-seata-group


debug: false
############# springDoc配置 #############
springdoc:
  version: '@springdoc.version@'
  api-docs:
    groups:
      enabled: true
    enabled: true
  swagger-ui:
    operationsSorter: function
    tagsSorter: alpha
    docExpansion: none
    path: /swagger-ui.html
  group-configs:
    - group: api
      packages-to-scan: com.wunding.learn.excitation.service.client.rest
    - group: web
      packages-to-scan: com.wunding.learn.excitation.service.admin.rest
  # 不配置下面 get请求为对象会变为 json
  default-flat-param-object: true
  disable-i18n: true













