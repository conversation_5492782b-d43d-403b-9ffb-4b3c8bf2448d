<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false" scan="true" scanPeriod="1 seconds">

    <contextName>logback</contextName>

    <property name="log.path" value="/data/logs/excitation/excitation_${HOSTNAME}.log" />
    <property name="mqlog.path"  value="/data/logs/excitation/mq_${HOSTNAME}.log" />
    <property name="bizlog.path"  value="/data/logs/excitation/biz_${HOSTNAME}.log" />
    <property name="restlog.path"  value="/data/logs/excitation/rest_${HOSTNAME}.log" />

    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <!-- <filter class="com.example.logback.filter.MyFilter" /> -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>debug</level>
        </filter>
        <encoder>
            <pattern>%date %level [%X{traceId}][%thread] %logger{36} [%file : %line] %msg%n
            </pattern>
        </encoder>
    </appender>

    <appender name="file"
      class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}.%d{yyyy-MM-dd}.%i.zip</fileNamePattern>
            <!--每个文件最多500MB-->
            <maxFileSize>500MB</maxFileSize>
            <!--日志文件保留天数-->
            <maxHistory>15</maxHistory>
            <!--每个文件最多500MB，保留15天的历史记录，但最多10GB-->
            <totalSizeCap>10GB</totalSizeCap>
            <!--重启清理日志文件-->
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>

        <encoder>
            <pattern>%date %level [%X{traceId}][%thread] %logger{36} [%file : %line] %msg%n
            </pattern>
        </encoder>
    </appender>

    <appender name="mqlog"
      class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${mqlog.path}</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${mqlog.path}.%d{yyyy-MM-dd}.%i.zip</fileNamePattern>
            <!--每个文件最多500MB-->
            <maxFileSize>500MB</maxFileSize>
            <!--日志文件保留天数-->
            <maxHistory>15</maxHistory>
            <!--每个文件最多500MB，保留15天的历史记录，但最多10GB-->
            <totalSizeCap>10GB</totalSizeCap>
            <!--重启清理日志文件-->
            <cleanHistoryOnStart>false</cleanHistoryOnStart>
        </rollingPolicy>

        <encoder>
            <pattern>%date [%thread] %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="bizlog"
      class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${bizlog.path}</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${bizlog.path}.%d{yyyy-MM-dd}.%i.zip</fileNamePattern>
            <!--每个文件最多500MB-->
            <maxFileSize>500MB</maxFileSize>
            <!--日志文件保留天数-->
            <maxHistory>15</maxHistory>
            <!--每个文件最多500MB，保留15天的历史记录，但最多10GB-->
            <totalSizeCap>10GB</totalSizeCap>
            <!--重启清理日志文件-->
            <cleanHistoryOnStart>false</cleanHistoryOnStart>
        </rollingPolicy>

        <encoder>
            <pattern>%date [%thread] %msg%n</pattern>
        </encoder>
    </appender>
    <appender name="restlog"
      class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${restlog.path}</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${restlog.path}.%d{yyyy-MM-dd}.%i.zip</fileNamePattern>
            <!--每个文件最多500MB-->
            <maxFileSize>500MB</maxFileSize>
            <!--日志文件保留天数-->
            <maxHistory>15</maxHistory>
            <!--每个文件最多500MB，保留15天的历史记录，但最多10GB-->
            <totalSizeCap>10GB</totalSizeCap>
            <!--重启清理日志文件-->
            <cleanHistoryOnStart>false</cleanHistoryOnStart>
        </rollingPolicy>

        <encoder>
            <pattern>%date [%thread] %msg%n</pattern>
        </encoder>
    </appender>

    <root level="${LOG_LEVEL:-INFO}">
        <appender-ref ref="console" />
        <!--<appender-ref ref="file" />-->
    </root>

    <logger name="com.wunding.learn.excitation.service.mapper" level="${LOG_LEVEL:-DEBUG}" />
    <logger name="com.wunding.learn.common.category.mapper" level="${LOG_LEVEL:-DEBUG}" />
    <logger name="com.wunding.learn.common.viewlimit.mapper" level="${LOG_LEVEL:-DEBUG}" />
    <logger name="org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer" level="${LOG_LEVEL:-WARN}" />

    <logger name="mqlog" level="info">
        <!--<appender-ref ref="mqlog" />-->
    </logger>
    <logger name="bizlog" level="info">
        <!--<appender-ref ref="bizlog" />-->
    </logger>
    <logger name="restlog" level="info">
        <!--<appender-ref ref="restlog" />-->
    </logger>

    <logger name="io.opentelemetry.sdk.internal.ThrottlingLogger" level="off" />
    <logger name="io.opentelemetry.exporter.internal.grpc.OkHttpGrpcExporter" level="off" />

</configuration>