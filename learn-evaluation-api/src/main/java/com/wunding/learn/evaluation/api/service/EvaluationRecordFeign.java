package com.wunding.learn.evaluation.api.service;

import com.wunding.learn.common.dto.AppFinishUserQuery;
import com.wunding.learn.common.dto.lecturerworkbench.AppFinishDTO;
import com.wunding.learn.evaluation.api.dto.EvalRecordSaveDTO;
import com.wunding.learn.evaluation.api.query.UserEvalRecordCountMapQuery;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.Map;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @date 2022/12/9
 */
@FeignClient(url = "${learn.service.learn-evaluation-service}", path = "/evaluation", name = "learn-evaluation-service")
public interface EvaluationRecordFeign {

    /**
     * 获取当前用户指定评估完成时间
     *
     * @param evalId
     * @return
     */
    @GetMapping("/record/getFinishTime")
    Date getFinishTime(@RequestParam("evalId") String evalId);

    /**
     * 获取指定评估的用户
     *
     * @param evalId
     * @return
     */
    @GetMapping("/record/getUserId")
    String getUserId(@RequestParam("evalId") String evalId);

    /**
     * 保存记录
     *
     * @param saveDTO
     */
    @PostMapping("/record/save")
    void save(@RequestBody EvalRecordSaveDTO saveDTO);

    /**
     * 获取评估成绩
     *
     * @param evalIds
     * @return
     */
    @PostMapping("/record/getScoreMap")
    Map<String, BigDecimal> getEvalScoreMap(@RequestBody Collection<String> evalIds);

    /**
     * 获取评估成绩
     *
     * @param evalIds
     * @return
     */
    @PostMapping("/record/getAvgScoreMap")
    Map<String, BigDecimal> getEvalAvgScoreMap(@RequestBody Collection<String> evalIds);

    /**
     * 获取评估成绩
     *
     * @param evalObjects
     * @return
     */
    @PostMapping("/record/getEvalScoreMapByEvalObjects")
    Map<String, BigDecimal> getEvalScoreMapByEvalObjects(@RequestBody Collection<String> evalObjects);

    /**
     * 获取当前用户完成的评估数量
     *
     * @param query
     * @return
     */
    @PostMapping("/record/getUserEvalRecordCountMap")
    Map<String, Integer> getUserEvalRecordCountMap(@RequestBody UserEvalRecordCountMapQuery query);

    /**
     * 获取评估完成人数
     *
     * @param detailIdRecordIdMap 记录id详细信息id映射
     * @return {@link Map}<{@link String}, {@link Integer}>
     */
    @PostMapping("/record/getFinishCountMap")
    Map<String, Integer> getEvalFinishCountMap(@RequestBody Map<String, String> detailIdColumnMap);

    /**
     * 获取已经完成评估的用户ID集合
     *
     * @param query
     * @return
     */
    @PostMapping("/record/getEvalUserFinishDTOMap")
    Map<String, AppFinishDTO> getEvalUserFinishDTOMap(@RequestBody AppFinishUserQuery query);
}
