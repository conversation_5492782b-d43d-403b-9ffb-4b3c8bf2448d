package com.wunding.learn.evaluation.api.service;

import com.wunding.learn.evaluation.api.dto.EvalQuestionFeignDTO;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 评估问题Feign
 *
 * <AUTHOR>
 * @date 2022/12/12
 */
@FeignClient(url = "${learn.service.learn-evaluation-service}", path = "/evaluation", name = "learn-evaluation-service")
public interface EvaluationQuestionFeign {

    /**
     * 评估ID
     *
     * @param evalId
     * @return
     */
    @GetMapping("/evaluation/question/list")
    List<EvalQuestionFeignDTO> getEvalQuestionDTOList(@RequestParam("evalId") String evalId);

    /**
     * 获取评估题目数量Map
     *
     * @param evalIds
     * @return
     */
    @GetMapping("/evaluation/question/getCountMap")
    Map<String, Integer> getEvalQuestionCountMap(@RequestParam("evalIds") Collection<String> evalIds);


}
