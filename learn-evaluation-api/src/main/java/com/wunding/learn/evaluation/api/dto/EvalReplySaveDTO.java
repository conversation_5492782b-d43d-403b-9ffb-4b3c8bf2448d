package com.wunding.learn.evaluation.api.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 评估回答DTO
 *
 * <AUTHOR>
 * @date 2022/12/12
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class EvalReplySaveDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键id
     */
    private String id;
    /**
     * 评估记录人员id
     */
    private String recordId;
    /**
     * 评估id
     */
    private String evalId;
    /**
     * 问题id
     */
    private String questionId;
    /**
     * 回答人
     */
    private String answerBy;
    /**
     * 回答内容
     */
    private String answerContent;
    /**
     * 分值
     */
    private BigDecimal mark;
}
