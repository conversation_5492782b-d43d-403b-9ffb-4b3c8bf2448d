package com.wunding.learn.evaluation.api.service;

import com.wunding.learn.common.dto.FormTaskEvaluationRecord;
import com.wunding.learn.evaluation.api.dto.EvalReplySaveDTO;
import com.wunding.learn.evaluation.api.query.EvalReplyQueryDTO;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @date 2022/12/9
 */
@FeignClient(url = "${learn.service.learn-evaluation-service}", path = "/evaluation", name = "learn-evaluation-service")
public interface EvaluationReplyFeign {

    /**
     * 获取评估成绩Map
     *
     * @param evalIds 评估ID集合
     * @param type    类型(讲师、课程、项目)
     * @return
     */
    @PostMapping("/reply/getScoreByEvalIdsAndType")
    Map<String, BigDecimal> getScoreByEvalIdsAndType(@RequestBody Collection<String> evalIds,
        @RequestParam("type") String type);

    /**
     * 保存评估回答记录
     *
     * @param saveDTO
     */
    @PostMapping("/reply/save")
    void save(@RequestBody EvalReplySaveDTO saveDTO);

    @PostMapping("/reply/findEvaluationRecordsByRecordId")
    List<FormTaskEvaluationRecord> findEvaluationRecordsByRecordId(@RequestParam("recordId") String recordId);

    @PostMapping("/evaluation/reply/getMark")
    Integer getMark(@RequestParam("recordId") String recordId);

    /**
     *查项目评估总体的平均分
     *
     * @param projectIds 项目id集合
     * @return
     */
    @PostMapping("/reply/getScoreMapByProjectIds")
    Map<String, BigDecimal> getScoreMapByProjectIds(@RequestBody Collection<String> projectIds);

    /**
     * 根据评估对象查询评估分
     * @param evaluationObjects 评估对象集合
     * @return Map<String, BigDecimal>
     */
    @PostMapping("/reply/getScoreMapByEvaluationObjects")
    Map<String, BigDecimal> getScoreMapByEvaluationObjects(@RequestBody Collection<String> evaluationObjects);

    /**
     * 获取评估回答内容表DTO对象列表
     *
     * @param evalReplyQueryDTO
     * @return
     */
    @GetMapping("/reply/getEvalReplyList")
    List<EvalReplySaveDTO> getEvalReplyList(@RequestBody EvalReplyQueryDTO evalReplyQueryDTO);

    /**
     * 保存评估回答记录
     *
     * @param saveDTOList
     */
    @PostMapping("/reply/updateBatch")
    void updateBatch(@RequestBody List<EvalReplySaveDTO> saveDTOList);
}
