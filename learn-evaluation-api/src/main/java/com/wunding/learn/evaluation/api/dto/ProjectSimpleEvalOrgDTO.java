package com.wunding.learn.evaluation.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 学习项目评估数据统计
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">wgr</a>
 * @date 2023/5/23 15:52
 */
@Data
@Accessors(chain = true)
public class ProjectSimpleEvalOrgDTO {

    @Schema(description = "评估ID")
    private String  evalId;


    @Schema(description = "问题项ID")
    private String questionItemId;

    @Schema(description = "每个问题项的平均分")
    private Float itemAvgScore;



}
