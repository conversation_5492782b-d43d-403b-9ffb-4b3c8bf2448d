package com.wunding.learn.evaluation.api.query;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @title: LecturerMinScoreQueryDTO
 * @projectName: mlearn
 * @description：
 * @date 2022/8/11 14:43
 */
@Data
@Schema(name = "LecturerMinScoreQueryDTO", description = "获取学习项目对讲师在一定时间内所有评估最低分数查询对象")
public class LecturerMinScoreQueryDTO {

    @Schema(description = "讲师授课记录idList")
    private List<String> lecturerExaminationIdList;

    @Schema(description = "开始时间")
    private Date beginTime;

    @Schema(description = "结束时间")
    private Date endTime;
}
