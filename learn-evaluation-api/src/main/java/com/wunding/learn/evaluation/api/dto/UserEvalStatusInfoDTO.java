package com.wunding.learn.evaluation.api.dto;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 用户评估信息
 *
 * <AUTHOR>
 * @date 2022/12/13
 */
@Data
@Accessors(chain = true)
public class UserEvalStatusInfoDTO implements Serializable {

    public static final long serialVersionUID = 1L;

    /**
     * 阶段ID
     */
    private String phaseId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 资源ID
     */
    private String taskContent;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    private Integer status;

    private Integer userStatus;
}
