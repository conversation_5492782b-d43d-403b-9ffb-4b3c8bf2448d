package com.wunding.learn.evaluation.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2022/12/9
 */
@Accessors(chain = true)
@Data
public class EvaluationFeignDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 评估id
     */
    private String id;


    /**
     * 评估名称
     */
    private String evalName;


    /**
     * 评估简介
     */
    private String memo;


    /**
     * 开始时间
     */
    private Date startTime;


    /**
     * 结束时间
     */
    private Date endTime;


    /**
     * 组织id
     */
    private String orgId;


    /**
     * 是否发布
     */
    private Integer isPublish;


    /**
     * 发布人
     */
    private String publishBy;


    /**
     * 发布时间
     */
    private Date publishTime;


    /**
     * 是否可用
     */
    private Integer isAvailable;


    /**
     * 是否删除
     */
    private Integer isDel;


    /**
     * 新增人
     */
    private String createBy;


    /**
     * 新增时间
     */
    private Date createTime;


    /**
     * 更新人
     */
    private String updateBy;


    /**
     * 更新时间
     */
    private Date updateTime;


    /**
     * 是否培训班评估
     */
    private Integer isTrain;


    /**
     * 模板编号
     */
    private String templetId;


    /**
     * 评估来源： 模板添加 手动导入
     */
    private Integer isSource;


    /**
     * 评估类型
     */
    private Integer evaluationType;


    /**
     * 评估对象
     */
    private String evaluationObject;


    /**
     * 项目阶段id
     */
    private String phaseId;


    /**
     * 是否显示评估结果 0 不显示  1显示
     */
    private Integer isEvaluationResults;

    /**
     * 题目数量
     */
    private Long questionCount;

    /**
     * 参与人数
     */
    private Long participateCount;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 是否完成评估
     */
    private Integer isFinish;

    @Schema(description = "评估分制 ,5,10" , defaultValue = "10")
    private Long scoreType;
}
