package com.wunding.learn.evaluation.api.dto;

import com.wunding.learn.common.dto.SaveViewLimitDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 讲师授课记录评估保存数据对象
 *
 * <AUTHOR>
 * @date 2023/3/14
 */
@Data
@Accessors(chain = true)
public class ExaminationEvalFeignSaveDTO {

    /**
     * 评估分制 10-10分制,5-5分制
     */
    private Long scoreType;

    /**
     * 评估ID
     */
    private String id;

    /**
     * 讲师姓名(讲师授课评估时使用)
     */
    private String lecturerName;

    /**
     * 评估名称
     */
    private String evalName;

    /**
     * 评估方式 默认内训模板:4, 默认售后模板:5, 其他评估模板/手动上传:2, 评估库选择:6, 不需要评估:3
     */
    private Integer evalType;

    /**
     * 文件路径
     */
    private String excelFilePath;

    /**
     * 文件名称
     */
    private String excelFileName;

    /**
     * 授课记录ID
     */
    private String examinationId;

    /**
     * 评估开始时间
     */
    private Date evalStartTime;

    /**
     * 评估结束时间
     */
    private Date evalEndTime;

    /**
     * 是否发布
     */
    private Integer isPublish;

    /**
     * 评估库id
     */
    private String evalLibId;

    @Schema(description = "评估下发范围类型")
    private Integer viewLimitType;

    /**
     * 下发范围
     */
    private List<? extends SaveViewLimitDTO> viewLimitDTOS;

    private String projectId;

    private String taskId;
    /**
     * 下发范围方案id
     */
    private Long programmeId;


    /**
     * 项目阶段id/主题id
     */
    @Schema(description = "项目阶段id/主题id")
    private String phaseId;

    /**
     * 日程id
     */
    @Schema(description = "日程id")
    private String scheduleId;

}
