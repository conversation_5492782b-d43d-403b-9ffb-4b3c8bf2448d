package com.wunding.learn.evaluation.api.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2022/12/12
 */
@Data
@Accessors(chain = true)
public class EvalQuestionItemFeignDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private String id;


    /**
     * 问题id
     */
    private String questionId;


    /**
     * 评估id
     */
    private String evalId;


    /**
     * 选择项名称
     */
    private String itemName;


    /**
     * 排序
     */
    private Integer sortNo;


    /**
     * 是否可用
     */
    private Integer isAvailable;


    /**
     * 是否删除
     */
    private Integer isDel;


    /**
     * 分值
     */
    private BigDecimal mark;


    /**
     * 打分说明
     */
    private String intro;


    /**
     * 提问人
     */
    private String createBy;


    /**
     * 提问日期
     */
    private Date createTime;


    /**
     * 修改人
     */
    private String updateBy;


    /**
     * 修改日期
     */
    private Date updateTime;

}
