package com.wunding.learn.evaluation.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 学习项目平均分
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">wgr</a>
 * @date 2023/5/24 15:32
 */
@Data
@Accessors(chain = true)
public class ProjectEvalAvgScore {

    @Schema(description = "评估ID")
    private String  projectId;

    @Schema(description = "讲师评估平均分")
    private BigDecimal lecturerAvgScore;

    @Schema(description = "班级评估平均分")
    private BigDecimal projectAvgScore;

}
