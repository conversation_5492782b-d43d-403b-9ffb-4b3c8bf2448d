package com.wunding.learn.evaluation.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 评估对象
 *
 * <AUTHOR>
 * @since 2022/8/10 10:58
 */
@Data
@Accessors(chain = true)
@Schema(name = "EvalDTO", description = "评估对象")
public class EvalDTO {

    @Schema(description = "评估主键")
    private String id;

    @Schema(description = "评估对象")
    private String evaluationObject;

    @Schema(description = "评估名称")
    private String evalName;

    @Schema(description = "开始时间")
    private Date startTime;

    @Schema(description = "结束时间")
    private Date endTime;

    @Schema(description = "参与人数")
    private Long recordCount;

    @Schema(description = "平均得分")
    private BigDecimal avgScore;

    @Schema(description = "来源")
    private Integer isTrain;

    @Schema(description = "模板ID")
    private String templetId;

    @Schema(description = "是否发布")
    private Integer isPublish;

    @Schema(description = "收到评估数量")
    private Integer assessedCount;

    @Schema(description = "分制")
    private Long scoreType;
}
