package com.wunding.learn.evaluation.api.dto;

import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2022/12/12
 */
@Data
@Accessors(chain = true)
public class EvalQuestionFeignDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    private String evalId;

    private Integer questionNo;

    private String questionName;

    private String questionCategory;

    private Integer questionType;

    private List<EvalQuestionItemFeignDTO> questionOptions;
}
