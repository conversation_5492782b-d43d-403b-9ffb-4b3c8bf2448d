package com.wunding.learn.evaluation.api.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 评估参与信息DTO
 *
 * <AUTHOR>
 * @date 2022/12/9
 */
@Data
@Accessors(chain = true)
public class LecturerEvaluateInfoDTO implements Serializable {

    public static final long serialVersionUID = 1L;

    /**
     * 评估ID
     */
    private String id;

    /**
     * 评估标题
     */
    private String evalName;

    /**
     * 评估开始时间
     */
    private Date startime;

    /**
     * 评估截止时间
     */
    private Date endTime;

    /**
     * 评估对象
     */
    private String evaluateObject;

    /**
     * 评估类型 1 学习项目 2 讲师授课记录(讲师)
     */
    private Integer evaluationType;


    /**
     * 参与人数
     */
    private Integer finishedCount;

    /**
     * 未参与人数
     */
    private Integer notFinishedCount;

    /**
     * 讲师分
     */
    private BigDecimal lectureScore;

    /**
     * 课程分
     */
    private BigDecimal courseScore;

    /**
     * 项目平均分
     */
    private BigDecimal projectScore;

    /**
     * 得分
     */
    private BigDecimal score;
}
