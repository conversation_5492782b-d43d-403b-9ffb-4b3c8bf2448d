package com.wunding.learn.evaluation.api.dto;

import com.wunding.learn.common.viewlimit.dto.ViewLimitTypeDTO;
import com.wunding.learn.user.api.dto.viewlimit.ViewLimitBaseInfoDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 评估编辑预览对象
 *
 * <AUTHOR> aixinrong
 * @since : 2022/7/12 9:49
 */
@Data
@Schema(name = "EvaluationEditDTO", description = "评估编辑预览对象")
public class EvaluationInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "评估id")
    private String id;

    @Schema(description = "评估名称")
    private String evalName;

    @Schema(description = "评估简介")
    private String memo;

    @Schema(description = "开始时间")
    private Date startTime;

    @Schema(description = "结束时间")
    private Date endTime;

    @Schema(description = "发布状态", hidden = true)
    private Integer isPublish;

    @Schema(description = "模板编号")
    private String sourceId;

    @Schema(description = "评估来源  0评估库 1手动上传")
    private Integer isSource;

    @Schema(description = "评估类型 1仅项目评估 2讲师授课明细评估")
    private Integer evaluationType;

    @Schema(description = "评估对象")
    private String evaluationObject;

    @Schema(description = "项目阶段id")
    private String phaseId;

    @Schema(description = "是否显示评估结果,0不显示1显示")
    private Integer isEvaluationResults;

    @Schema(description = "下发范围类型 1-项目所有成员 2-指定人员")
    private Integer viewLimitType;

    @Schema(description = "下发范围")
    private List<ViewLimitTypeDTO> viewLimit;

    @Schema(description = "评估上传地址")
    private String fileUrl;

    @Schema(description = "评估文件名")
    private String fileName;

    @Schema(description = "评估上传地址(相对路径)")
    private String filePath;


    @Schema(description = "源名称")
    private String sourceName;

    @Schema(description = "下发基本信息")
    private ViewLimitBaseInfoDTO limit;

    @Schema(description = "创建人id")
    private String createBy;

    @Schema(description = "日程id")
    private String scheduleId;

    @Schema(description = "分制")
    private Long scoreType;
}
