package com.wunding.learn.evaluation.api.dto;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2022/12/12
 */
@Data
@Accessors(chain = true)
public class EvalRecordSaveDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键id
     */
    private String id;
    /**
     * 评估id
     */
    private String evalId;
    /**
     * 评估id
     */
    private String columnId;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 是否完成
     */
    private Integer isFinish;
    /**
     * 完成时间
     */
    private Date finishTime;


    public EvalRecordSaveDTO() {
    }


    public EvalRecordSaveDTO(String id, String evalId, String columnId, String userId, Date finishTime,
        Integer isFinish) {
        this.id = id;
        this.evalId = evalId;
        this.columnId = columnId;
        this.userId = userId;
        this.finishTime = finishTime;
        this.isFinish = isFinish;
    }

}
