package com.wunding.learn.evaluation.api.dto;

import com.wunding.learn.common.dto.SaveViewLimitDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 评估信息
 *
 * <AUTHOR>
 * @date 2022/12/7
 */
@Accessors(chain = true)
@Data
public class EvaluationFeignSaveDTO implements Serializable {

    public static final long serialVersionUID = 1L;

    /**
     * 评估ID
     */
    private String id;

    /**
     * 评估名称
     */
    private String evalName;

    /**
     * 评估简介
     */
    private String memo;

    /**
     * 评估开始时间
     */
    private Date startTime;

    /**
     * 评估结束时间
     */
    private Date endTime;

    /**
     * 是否发布
     */
    private Integer isPublish;

    /**
     * 评估类型 1学习项目应用 2 讲师应用添加
     */
    private Integer evaluationType;

    /**
     * 资源来源
     */
    private Integer isTrain;

    /**
     * 评估对象
     */
    private String evaluationObject;

    /**
     * 评估结果是否显示
     */
    private Integer isEvaluationResults;

    /**
     * 评估方式 默认内训模板:4, 默认售后模板:5, 其他评估模板/手动上传:2, 评估库选择:6, 不需要评估:3
     */
    private Integer isSource;

    /**
     * 评估来源 默认内训模板:4, 默认售后模板:5, 其他评估模板/手动上传:2, 评估库选择:6, 不需要评估:3
     */
    private Integer sourceType;

    /**
     * 评估分制 10-10分制,5-5分制
     */
    private Long scoreType;

    /**
     * 源评估id
     */
    private String sourceEvalId;

    /**
     * 模板ID
     */
    private String templetId;

    /**
     * 是否入评估库
     */
    private Integer isAddMould;

    /**
     * 评估上传模板路径
     */
    private String excelFilePath;

    /**
     * 评估上传模板名称
     */
    private String excelFileName;

    /**
     * 项目阶段id
     */
    private String phaseId;

    private String projectId;

    private String taskId;

    @Schema(description = "评估可见范围 1项目成员 2指定成员")
    private Integer viewLimitType;

    /**
     * 下发范围
     */
    private List<? extends SaveViewLimitDTO> viewLimitDTOS;

    /**
     * 下发范围方案id
     */
    private Long programmeId;

    /**
     * 日程id
     */
    private String scheduleId;
}
