package com.wunding.learn.evaluation.api.query;

import io.swagger.v3.oas.annotations.Parameter;
import java.util.Collection;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2022/12/13
 */
@Data
@Accessors(chain = true)
public class UserEvalStatusInfoQuery {

    private Collection<String> evalIds;

    private String userId;

    private String stageId;

    private Integer isComplete;

    /**
     * 是否过期0：未过期 1：过期 允许空
     */
    @Parameter(description = "是否过期0：未过期 1：过期 允许空")
    private Integer isExpired;
}
