package com.wunding.learn.evaluation.api.query;

import com.wunding.learn.common.bean.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Collection;
import lombok.Data;

/**
 * @Author: suchenyu @Date: 2022/12/8 11:10
 */
@Data
@Schema(name = "EvaluationProjectQuery", description = "学习项目评估管理列表查询对象")
public class EvaluationProjectQuery extends BaseEntity implements Serializable {

    @Schema(description = "项目id")
    private String projectId;

    @Schema(description = "讲师授课明细id集合")
    private Collection<String> examinationIds;

    @Schema(description = "当前登陆用户orgId", hidden = true)
    private String currentOrgId;

    @Schema(description = "当前登陆用户userId", hidden = true)
    private String currentUserId;

    @Schema(description = "train下的所有项目的ID")
    private Collection<String> projectIds;
}
