package com.wunding.learn.evaluation.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2022/12/13
 */
@Data
@Accessors(chain = true)
public class LearningCalendarTaskFeignDTO implements Serializable, Comparable<LearningCalendarTaskFeignDTO> {

    private static final long serialVersionUID = 3234217876292057752L;
    @Schema(description = "任务ID")
    private String id;

    @Schema(description = "资源ID")
    private String activityId;

    @Schema(description = "是否属于学习项目里的任务 0不是 1是")
    private Integer isProjectTask;

    @Schema(description = "任务名称")
    private String title;

    @Schema(description = "资源类型 course exam  exercise survey live project sign evaluation")
    private String flag;

    @Schema(description = "标准图标url")
    private String image;

    @Schema(description = "任务开始时间")
    private Date startTime;

    @Schema(description = "任务结束时间")
    private Date endTime;

    @Schema(description = "任务完成状态 0尚未进行 1正在进行")
    private Integer status;

    @Schema(description = "用户参与状态 尚未进行 0, 正在进行(暂不配置) 1, 已经完成 2")
    private Integer userStatus;

    @Schema(description = "是否是学习项目 0=不是  1是")
    private Integer isOperation;

    @Override
    public int compareTo(LearningCalendarTaskFeignDTO o) {
        return this.startTime.compareTo(o.startTime);
    }
}
