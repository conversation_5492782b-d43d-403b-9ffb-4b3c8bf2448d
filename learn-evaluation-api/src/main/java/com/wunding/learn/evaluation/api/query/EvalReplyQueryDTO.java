package com.wunding.learn.evaluation.api.query;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.Data;

@Data
@Schema(name = "EvalReplyQueryDTO", description = "评估回答内容表查询对象")
public class EvalReplyQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "评估id")
    private String evalId;

    @Schema(description = "回答人")
    private String answerBy;

}

