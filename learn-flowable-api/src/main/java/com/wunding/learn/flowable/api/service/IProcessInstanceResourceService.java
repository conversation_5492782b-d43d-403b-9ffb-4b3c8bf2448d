package com.wunding.learn.flowable.api.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.wunding.learn.flowable.api.model.ProcessInstanceResource;

/**
 * <p> 资源流程实例表 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">qrh</a>
 * @since 2024-10-28
 */
public interface IProcessInstanceResourceService extends IService<ProcessInstanceResource> {


    ProcessInstanceResource getProcessInstanceResourceByResourceId(String resourceId);
}
