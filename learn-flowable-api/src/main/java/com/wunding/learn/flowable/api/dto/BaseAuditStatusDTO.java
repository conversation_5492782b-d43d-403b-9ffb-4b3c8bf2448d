package com.wunding.learn.flowable.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p> 基本审核状态dto
 *
 * <AUTHOR> href="mailto:<EMAIL>">chenhouhua</a>
 * @since 2025-06-10
 */
@Data
@Accessors(chain = true)
public class BaseAuditStatusDTO {

    @Schema(description = "0-草稿，1-审批中，2-审核通过，3-审批不通过，4-驳回，5-不涉及", hidden = true)
    private Integer auditStatus;

    @Schema(description = "0-草稿，1-审批中，2-审核通过，3-审批不通过，4-驳回，5-不涉及")
    private String auditStatusStr;

    @Schema(description = "申请时间")
    private Date registerTime;

}
