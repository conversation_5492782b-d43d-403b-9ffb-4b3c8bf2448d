package com.wunding.learn.flowable.api.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p> <p> 通用完成结果dto
 *
 * <AUTHOR> href="mailto:<EMAIL>">chenhouhua</a>
 * @since 2024-10-11
 */
@Data
@Accessors(chain = true)
@Schema(name = "CompleteResultDTO", description = "通用完成结果dto")
public class CompleteResultDTO {

    @Schema(description = "流程实例id")
    private String instanceId;

    @Schema(description = "当前任务id")
    private String currTaskId;

    @Schema(description = "是否存在下一步操作")
    private Boolean hasNextTask;

    @Schema(description = "是否是最后一步审核", hidden = true)
    @JsonIgnore
    private Boolean isFinished;

    @Schema(description = "下一步实例任务id")
    private String nextInstanceTaskId;

    @Schema(description = "下一步流程定义任务id")
    private String nextDefinitionTaskId;

    @Schema(description = "当前审核处理是否成功")
    private Boolean operateResult;

    @Schema(description = "审核操作")
    private String operate;

    @Schema(description = "下一个节点的审核角色")
    private String assigneeRole;

    @Schema(description = "下一个节点的审核人")
    private String assignee;

    @Schema(description = "资源id", hidden = true)
    @JsonIgnore
    private String resourceId;

    @Schema(description = "资源类型", hidden = true)
    @JsonIgnore
    private String resourceType;

    @Schema(description = "剩余审计数量")
    private Integer remainingAuditQuantity;
}
