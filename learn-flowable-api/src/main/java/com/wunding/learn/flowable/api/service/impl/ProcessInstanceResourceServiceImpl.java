package com.wunding.learn.flowable.api.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.enums.process.ProcessStatusEnum;
import com.wunding.learn.common.mybatis.service.impl.BaseServiceImpl;
import com.wunding.learn.flowable.api.mapper.ProcessInstanceResourceMapper;
import com.wunding.learn.flowable.api.model.ProcessInstanceResource;
import com.wunding.learn.flowable.api.service.IProcessInstanceResourceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>  资源流程实例表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">qrh</a>
 * @since 2024-10-28
 */
@Slf4j
@Service("processInstanceResourceService")
public class ProcessInstanceResourceServiceImpl extends
    BaseServiceImpl<ProcessInstanceResourceMapper, ProcessInstanceResource> implements
    IProcessInstanceResourceService {

    @Override
    public ProcessInstanceResource getProcessInstanceResourceByResourceId(String resourceId) {
        return getBaseMapper().selectOne(
            new LambdaQueryWrapper<ProcessInstanceResource>()
                .eq(ProcessInstanceResource::getResourceId, resourceId)
                .eq(ProcessInstanceResource::getStatus, ProcessStatusEnum.RUNNING.getStatus())
                .eq(ProcessInstanceResource::getIsAvailable, GeneralJudgeEnum.CONFIRM.getValue())
                .eq(ProcessInstanceResource::getIsDel, GeneralJudgeEnum.NEGATIVE.getValue())
        );
    }
}
