package com.wunding.learn.flowable.api.query;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.wunding.learn.common.bean.BaseEntity;
import com.wunding.learn.common.enums.ResourceTypeEnum;
import io.swagger.v3.oas.annotations.Parameter;
import java.util.Collection;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p> 我审批资源查询
 *
 * <AUTHOR> href="mailto:<EMAIL>">chenhouhua</a>
 * @since 2024-10-17
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class MyApprovalResourceQuery extends BaseEntity {

    @Parameter(description = "userId")
    private String userId;

    /**
     * 查询我审批的数据 (0:进查询当前需要我审核的数据，1:查询需要我审核和我已经审核过的数据)
     */
    @Parameter(description = "queryHistory")
    private Integer queryHistory;

    /**
     * 资源类型 {@link ResourceTypeEnum}
     */
    @Parameter(description = "resourceType", hidden = true)
    private String resourceType;

    /**
     * 资源id (如果做子查询,传递一个查询条件,格式为 "主表别名.关联主键id" )
     */
    @Parameter(description = "resourceId")
    private String resourceId;

    @JsonIgnore
    @Parameter(description = "userRoleList", hidden = true)
    private List<String> userRoleList;

    @JsonIgnore
    @Parameter(description = "userOrgList", hidden = true)
    private List<String> userOrgList;

    @JsonIgnore
    @Parameter(description = "userOrgIdList", hidden = true)
    private List<String> userOrgIdList;

    @JsonIgnore
    @Parameter(description = "instanceIds", hidden = true)
    private Collection<String> instanceIds;

    @JsonIgnore
    @Parameter(description = "instanceStatus", hidden = true)
    private List<String> instanceStatus;

    @JsonIgnore
    @Parameter(description = "instanceStatusOrder", hidden = true)
    private List<Integer> instanceStatusOrder;

    @JsonIgnore
    @Parameter(description = "instanceResourceType", hidden = true)
    private List<String> instanceResourceType;

    @Parameter(description = "资源名称")
    private String resourceName;

}
