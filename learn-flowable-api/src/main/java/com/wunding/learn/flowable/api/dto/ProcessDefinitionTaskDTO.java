package com.wunding.learn.flowable.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p> 流程定义任务dto
 *
 * <AUTHOR> href="mailto:<EMAIL>">chenhouhua</a>
 * @since 2024-10-11
 */
@Data
@Accessors(chain = true)
@Schema(name = "ProcessDefinitionTaskDTO", description = "审核流程定义DTO")
public class ProcessDefinitionTaskDTO {

    @Schema(description = "流程定义id")
    private String definitionId;

    @Schema(description = "流程任务id")
    private String taskId;

    @Schema(description = "审核环节")
    private String taskName;

    @Schema(description = "审核角色id")
    private String assigneeRole;

    @Schema(description = "审核角色（包含自动审批）")
    private String assigneeRoleName;

    @Schema(description = "特定审核用户")
    private String assigneeName;

    @Schema(description = "特定审核用户")
    private String assignee;

    @Schema(description = "审核顺序")
    private Integer sort;

}