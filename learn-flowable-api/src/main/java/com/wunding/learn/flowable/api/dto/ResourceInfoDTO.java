package com.wunding.learn.flowable.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p> 资源信息dto
 *
 * <AUTHOR> href="mailto:<EMAIL>">chenhouhua</a>
 * @since 2025-06-13
 */
@Data
@Schema(name = "ResourceInfoDTO", description = "审核流程资源信息-通用")
public class ResourceInfoDTO {

    @Schema(description = "资源id")
    private String resourceId;

    @Schema(description = "资源名称")
    private String resourceName;

    @Schema(description = "资源编号")
    private String resourceCode;

    @Schema(description = "审核状态0-草稿，1-审批中，2-审核通过，3-审批不通过，4-驳回，5-不涉及")
    private Integer auditStatus;

    @Schema(description = "审核状态0-草稿，1-审批中，2-审核通过，3-审批不通过，4-驳回，5-不涉及")
    private String auditStatusName;

    @Schema(description = "摘要内容")
    private String summary;

}
