package com.wunding.learn.flowable.api.model;

import java.util.Date;

/**
 * <p> 基础审核状态
 *
 * <AUTHOR> href="mailto:<EMAIL>">chenhouhua</a>
 * @since 2025-06-10
 */
public interface BaseAuditStatus {


    /**
     * 获取id
     *
     * @return {@link String }
     */
    String getId();

    /**
     * 获取审核状态
     *
     * @return {@link Integer }
     */
    Integer getAuditStatus();

    /**
     * 获取审核提交时间
     *
     * @return {@link Date }
     */
    Date getRegisterTime();

    /**
     * 获取资源名称
     *
     * @return {@link String }
     */
    String getResourceName();

    /**
     * 设置审核状态 {@link com.wunding.learn.flowable.api.constant.AuditStatusEnum}
     */
    void setResourceAuditStatus(Integer auditStatus);

    /**
     * 获取类别id
     *
     * @return {@link String }
     */
    String getCategoryId();

    /**
     * 是否发布
     *
     * @param isPublish 正在发布
     */
    void setResourceIsPublish(Integer isPublish);

}
