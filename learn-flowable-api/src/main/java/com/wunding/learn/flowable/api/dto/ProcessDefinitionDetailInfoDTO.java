package com.wunding.learn.flowable.api.dto;

import com.wunding.learn.common.enums.other.CategoryTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p> 审核流程查询DTO
 *
 * <AUTHOR> href="mailto:<EMAIL>">chenhouhua</a>
 * @since 2024-10-11
 */
@Data
@Schema(name = "ProcessDefinitionDetailInfoDTO", description = "审核流程查询DTO")
public class ProcessDefinitionDetailInfoDTO {

    /**
     * 主键
     */
    @Schema(description = "主键")
    private String id;


    /**
     * 流程的类型
     */
    @Schema(description = "流程的类型")
    private String processType;


    /**
     * 流程的名称
     */
    @Schema(description = "流程的名称")
    private String processName;


    /**
     * 流程的版本号
     */
    @Schema(description = "流程的版本号")
    private Integer version;


    /**
     * 免审核的角色（最多支持3个）
     */
    @Schema(description = "免审核的角色（最多支持3个）")
    private String exemptRoles;


    /**
     * 是否启用
     */
    @Schema(description = "是否启用")
    private Integer isAvailable;

}
