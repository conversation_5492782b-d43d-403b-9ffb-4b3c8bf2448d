package com.wunding.learn.flowable.api.constant;

import com.wunding.learn.common.context.user.UserThreadContext;

/**
 * 课程缓存key相关常量
 *
 * <AUTHOR> href="mailto:<EMAIL>">yuxueyi</a>
 * @version V7.0.0
 * @since 2022/5/11  11:25
 */
public enum ProcessRedisKeyEnum {

    /**
     * 课程编号
     */
    PROCESS_CODE_NUM("ProcessCodeNum");


    private String key;

    ProcessRedisKeyEnum(String key) {
        this.key = key;
    }

    public String getKey() {
        return key + ":" + UserThreadContext.getTenantId();
    }
}
