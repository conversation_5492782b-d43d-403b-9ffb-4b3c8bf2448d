package com.wunding.learn.flowable.api.feign;

import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.dto.CourseProcessDTO;
import com.wunding.learn.common.dto.CourseProcessQueryDTO;
import com.wunding.learn.flowable.api.dto.CompleteOperateParamDTO;
import com.wunding.learn.flowable.api.dto.CompleteResultDTO;
import com.wunding.learn.flowable.api.dto.MyApprovalResourceDTO;
import com.wunding.learn.flowable.api.dto.ProcessDefinitionDetailDTO;
import com.wunding.learn.flowable.api.dto.ProcessDefinitionDetailInfoDTO;
import com.wunding.learn.flowable.api.dto.ProcessDefinitionFeignDTO;
import com.wunding.learn.flowable.api.dto.ProcessDefinitionTaskDTO;
import com.wunding.learn.flowable.api.dto.ProcessInstanceResourceFeignDTO;
import com.wunding.learn.flowable.api.dto.ProcessListBaseDTO;
import com.wunding.learn.flowable.api.dto.StartProcessInstanceDTO;
import com.wunding.learn.flowable.api.query.MyApprovalResourceQuery;
import com.wunding.learn.flowable.api.query.ProcessQueryDTO;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <p> 过程假装
 *
 * <AUTHOR> href="mailto:<EMAIL>">chenhouhua</a>
 * @since 2024-10-11
 */
@FeignClient(url = "${learn.service.learn-flowable-service}", name = "learn-flowable-service", path = "/user")
public interface ProcessFeign {

    /**
     * 启动流程实例
     *
     * @param startProcessInstanceDTO 启动流程实例dto
     * @return {@link CompleteResultDTO }
     */
    @PostMapping(value = "/startProcessInstance")
    CompleteResultDTO startProcessInstance(@RequestBody StartProcessInstanceDTO startProcessInstanceDTO);

    /**
     * 获取流程sql
     *
     * @param query 查询
     * @return {@link String }
     */
    @PostMapping(value = "/getProcessSql")
    String getProcessSql(@RequestBody MyApprovalResourceQuery query);

    /**
     * 获取当前用户的可审核资源id
     *
     * @param query 查询
     * @return {@link List }<{@link String }>
     */
    @PostMapping(value = "/getProcessingResource")
    PageInfo<MyApprovalResourceDTO> getProcessingResource(@RequestBody MyApprovalResourceQuery query);


    /**
     * 完成当前任务(审核处理)
     *
     * @param completeOperateParamDTO 完成任务dto
     * @return {@link Boolean }
     */
    @PostMapping(value = "/completeTaskOperate")
    CompleteResultDTO completeCurrentTaskOperate(@RequestBody CompleteOperateParamDTO completeOperateParamDTO);


    /**
     * 取消当前审核实例(预留)
     *
     * @param resourceIds 资源id
     * @return {@link Boolean }
     */
    @PostMapping(value = "/cancelProcessInstance")
    void cancelProcessInstance(@RequestBody Collection<String> resourceIds);


    /**
     * 根据资源id获取当前用户审核状态底色
     *
     * @param resourceIds 资源id
     * @return {@link Boolean }
     */
    @PostMapping(value = "/getAuditStatusByResourceIds")
    Map<String, Integer> getAuditStatusByResourceIds(@RequestBody Collection<String> resourceIds);


    /**
     * 按类型获取流程定义
     *
     * @param type 类型
     * @return {@link CompleteResultDTO }
     */
    @PostMapping(value = "/getProcessDefinitionByType")
    ProcessDefinitionDetailInfoDTO getProcessDefinitionByType(@RequestParam("type") String type);


    /**
     * 获取最新的审批实例
     * @return {@link MyApprovalResourceDTO}
     */
    @GetMapping(value = "/getMyApprovalResourceDTO")
    MyApprovalResourceDTO getMyApprovalResourceDTO(@RequestParam("resourceId") String resourceId, @RequestParam("type") String type);


    /**
     * 根据id获取审批实例
     * @return {@link MyApprovalResourceDTO}
     */
    @GetMapping(value = "/getMyApprovalResourceDTOById")
    MyApprovalResourceDTO getMyApprovalResourceDTOById(@RequestParam("id") String id);

    /**
     * 通过资源id获取任务信息
     *
     * @param resourceId 资源id
     * @return {@link CompleteResultDTO }
     */
    @GetMapping(value = "/getTaskInfoByResourceId")
    CompleteResultDTO getTaskInfoByResourceId(String resourceId);

    /**
     * 获取流程定义详情
     * @param definitionId 流程定义id
     * @return ProcessDefinitionDetailDTO
     */
    @GetMapping(value = "/getProcessDefinitionDetail")
    ProcessDefinitionDetailDTO getProcessDefinitionDetail(@RequestParam("definitionId") String definitionId);

    /**
     * 获取审核流程列表
     *
     * @param processQueryDTO 查询对象
     * @return 列表基本信息
     */
    @PostMapping(value = "/v2/getProcessDefinitionList")
    PageInfo<ProcessListBaseDTO> getProcessDefinitionList(@RequestBody ProcessQueryDTO processQueryDTO);

    /**
     * 获取审核流程列表
     *
     * @param courseProcessQueryDTO 查询对象
     * @return 列表基本信息
     * @deprecated 废弃建议使用 {@link #getProcessDefinitionList(ProcessQueryDTO processQueryDTO)}
     */
    @PostMapping(value = "/getProcessDefinitionList")
    PageInfo<CourseProcessDTO> getProcessDefinitionList(@RequestBody CourseProcessQueryDTO courseProcessQueryDTO);

    @GetMapping(value = "/isNeedAuditResource")
    Boolean isNeedAuditResource(@RequestParam("resourceId") String resourceId);

    @PostMapping(value = "/getNearestProcessDefinitionByProcessContentId")
    ProcessDefinitionFeignDTO getNearestProcessDefinitionByProcessContentId(@RequestBody List<String> categoryIdList);

    @GetMapping(value = "/getProcessDefinitionTaskByProcessDefinitionId")
    ProcessDefinitionTaskDTO getProcessDefinitionTaskByProcessDefinitionId(@RequestParam("processDefinitionId") String processDefinitionId);

    @PostMapping(value = "/saveProcessInstanceResource")
    void saveProcessInstanceResource(@RequestBody ProcessInstanceResourceFeignDTO dto);

    @GetMapping(value = "/deleteProcessInstanceResourceByResourceId")
    void deleteProcessInstanceByResourceId(@RequestParam("resourceId") String resourceId);

    @GetMapping(value = "/getProcessCodeMax")
    String getProcessCodeMax();

    @PostMapping(value = "/deleteProcessInstanceAndTaskByResourceId")
    void deleteProcessInstanceAndTaskByResourceId(@RequestBody Collection<String> resourceIds);

    @GetMapping(value = "/getProcessInstanceTaskByResourceId")
    String getProcessInstanceTaskByResourceId(@RequestParam("resourceId")String resourceId);

    @GetMapping(value = "/checkCourseCategoryHasProcess")
    Boolean checkCourseCategoryHasProcess(@RequestParam("id")String id);

    @GetMapping(value = "/getProcessInstanceById")
    ProcessInstanceResourceFeignDTO getProcessInstanceById(@RequestParam("instanceId") String instanceId);
}
