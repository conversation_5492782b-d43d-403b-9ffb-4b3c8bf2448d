package com.wunding.learn.flowable.api.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("process_instance_resource")
@Schema(name = "ProcessInstanceResource", description = "资源流程实例表")
public class ProcessInstanceResource implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Schema(description = "主键id")
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 流程实例编号
     */
    @Schema(description = "流程实例编号")
    @TableField("process_code")
    private String processCode;

    /**
     * 流程定义id
     */
    @Schema(description = "流程定义id")
    @TableField("process_definition_id")
    private String processDefinitionId;

    /**
     * 资源id
     */
    @Schema(description = "资源id")
    @TableField("resource_id")
    private String resourceId;


    /**
     * 审核申请人id
     */
    @Schema(description = "审核申请人id")
    @TableField("applicant_user_id")
    private String applicantUserId;

    /**
     * 审核申请类型，0-新建申请，1-修改申请
     */
    @Schema(description = "审核申请类型，0-新建申请，1-修改申请")
    @TableField("process_apply_type")
    private Integer processApplyType;

    /**
     * 当前审核角色
     */
    @Schema(description = "当前审核角色")
    @TableField(value = "current_assignee_role")
    private String currentAssigneeRole;



    /**
     * 当前审核人
     */
    @Schema(description = "当前审核人")
    @TableField("current_assignee")
    private String currentAssignee;

    /**
     * 需要审核的组织id
     */
    @Schema(description = "需要审核的组织id")
    @TableField("org_id")
    private String orgId;


    /**
     * 流程实例的状态
     */
    @Schema(description = "流程实例的状态，RUNNING（运行中），COMPLETED（已完成），CANCELLED（取消）")
    @TableField(value = "status")
    private String status;


    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;


    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 更新人
     */
    @Schema(description = "更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;


    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    /**
     * 是否删除[0:否 1:是]
     */
    @Schema(description = "是否删除[0:否 1:是]")
    @TableField("is_del")
    @TableLogic(value = "0", delval = "1")
    private Integer isDel;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用")
    @TableField("is_available")
    private Integer isAvailable;

}
