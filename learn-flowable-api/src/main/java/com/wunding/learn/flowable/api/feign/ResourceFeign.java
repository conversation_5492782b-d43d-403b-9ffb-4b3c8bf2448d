package com.wunding.learn.flowable.api.feign;


import com.wunding.learn.flowable.api.dto.ResourceInfoDTO;
import java.util.Collection;
import java.util.Map;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <p> 资源实现类的标准定义
 * <p>
 * 需要搭配 {@link AutoRegisterResourceBean} 声明哪些类实现了这个接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">chenhouhua</a>
 * @since 2025-06-10
 */
public interface ResourceFeign {

    @PostMapping(value = "/getResourceInfo")
    Map<String, ResourceInfoDTO> getResourceInfo(@RequestBody Collection<String> resourceIdList);

}
