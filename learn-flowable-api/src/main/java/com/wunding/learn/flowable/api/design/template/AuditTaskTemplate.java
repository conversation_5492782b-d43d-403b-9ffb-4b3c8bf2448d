package com.wunding.learn.flowable.api.design.template;

import static com.wunding.learn.common.util.string.StringUtil.newId;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.wunding.learn.common.category.model.Categorys;
import com.wunding.learn.common.category.service.ICategorysService;
import com.wunding.learn.common.constant.flowable.FlowableErrorNoEnum;
import com.wunding.learn.common.constant.other.AvailableEnum;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.enums.process.ProcessStatusEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.flowable.api.constant.ProcessRedisKeyEnum;
import com.wunding.learn.flowable.api.dto.CompleteOperateParamDTO;
import com.wunding.learn.flowable.api.dto.CompleteResultDTO;
import com.wunding.learn.flowable.api.dto.ProcessDefinitionFeignDTO;
import com.wunding.learn.flowable.api.dto.ProcessDefinitionTaskDTO;
import com.wunding.learn.flowable.api.feign.ProcessFeign;
import com.wunding.learn.flowable.api.model.ProcessInstanceResource;
import com.wunding.learn.flowable.api.model.ProcessInstanceTaskResource;
import com.wunding.learn.flowable.api.service.IProcessInstanceResourceService;
import com.wunding.learn.flowable.api.service.IProcessInstanceTaskResourceService;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * <p> <p> 审核任务模板
 * </br>
 * <font color="red"><b>警告：</b></font> 修改文件的方法逻辑后,最好自己跑一下单元测试能不能通过,并且保证自己新增加的代码能够被单元测试覆盖
 *
 * <AUTHOR> href="mailto:<EMAIL>">chenhouhua</a>
 * @since 2024-11-07
 */
@Slf4j
public abstract class AuditTaskTemplate<T extends CompleteOperateParamDTO> {

    // 通过子类构造器注入，所以这里没有实现注入的方式
    protected ProcessFeign processFeign;
    protected IProcessInstanceResourceService processInstanceResourceService;
    protected IProcessInstanceTaskResourceService processInstanceTaskResourceService;
    protected ICategorysService categorysService;
    protected RedisTemplate<String, Object> redisTemplate;
    private static final String REGEX = "(^/)|(/$)";

    /**
     * 审核流程处理
     * </br>
     * 针对批量处理不友好,后面有机会再优化,暂时不会有很大的性能问题
     *
     * @param completeOperateParamDTO 完整操作参数dto
     */
    public void processResult(T completeOperateParamDTO) {
        preValidate(completeOperateParamDTO);
        // 向审核流发起审核处理操作
        CompleteResultDTO resultDTO = completeCurrentTaskOperate(completeOperateParamDTO);
        // 资源获取到当前正在审核的实例
        ProcessInstanceResource processInstanceResource = getProcessInstanceResource(completeOperateParamDTO);
        if (Objects.isNull(processInstanceResource)) {
            throw new BusinessException(FlowableErrorNoEnum.STATUS_HAS_CHANGED_PLEASE_TRY_AGAIN);
        }
        // 更新当前进行中实例任务的状态
        updatePreviousTaskStatus(processInstanceResource);

        // 将实例的当前状态修改
        processInstanceResource.setCurrentAssigneeRole(resultDTO.getAssigneeRole());
        processInstanceResource.setCurrentAssignee(resultDTO.getAssignee());
        // 判断审核是否已经结束
        if (isFinalTask(resultDTO)) {
            ProcessStatusEnum processStatusEnum = ProcessStatusEnum.getProcessStatusEnumByOperate(
                completeOperateParamDTO.getAuditOperate());
            // 设置资源的实例完成状态
            processInstanceResource.setStatus(processStatusEnum.getStatus());
            // 审核结束更新资源的本身的装填
            finishUpdateAuditStatus(completeOperateParamDTO, processInstanceResource);
        } else {
            // 审核未结束,添加下一个环境的审核任务
            String instanceId = processInstanceResource.getId();
            // 添加新的实例任务信息
            ProcessInstanceTaskResource processInstanceTaskResource = new ProcessInstanceTaskResource().setId(newId());
            processInstanceTaskResource.setAssignee(resultDTO.getAssignee());
            processInstanceTaskResource.setAssigneeRole(resultDTO.getAssigneeRole());
            processInstanceTaskResource.setProcessInstanceId(instanceId);
            processInstanceTaskResource.setStatus(ProcessStatusEnum.RUNNING.getStatus());
            addNewTask(processInstanceTaskResource);
        }
        // 更新当前审核实例
        afterUpdateInstanceResource(processInstanceResource);
    }

    protected abstract void finishUpdateAuditStatus(T completeOperateParamDTO,
        ProcessInstanceResource processInstanceResource);

    protected void preValidate(T completeOperateParamDTO) {
        // 校验
    }

    protected CompleteResultDTO completeCurrentTaskOperate(T completeOperateParamDTO) {
        return processFeign.completeCurrentTaskOperate(completeOperateParamDTO);
    }

    protected ProcessInstanceResource getProcessInstanceResource(T completeOperateParamDTO) {
        return processInstanceResourceService.getOne(
            Wrappers.<ProcessInstanceResource>lambdaQuery()
                .eq(ProcessInstanceResource::getResourceId, completeOperateParamDTO.getResourceId())
                .eq(ProcessInstanceResource::getStatus, ProcessStatusEnum.RUNNING.getStatus()));
    }

    protected void updatePreviousTaskStatus(ProcessInstanceResource processInstanceResource) {
        // 获取流程实例的ID
        String instanceId = processInstanceResource.getId();
        // 获取当前日期和时间
        Date curDate = new Date();

        // 执行更新操作，仅当任务状态为运行中且更新时间小于当前时间时，将其状态更新为已完成
        boolean update = processInstanceTaskResourceService.lambdaUpdate()
            .set(ProcessInstanceTaskResource::getStatus, ProcessStatusEnum.COMPLETED.getStatus())
            .set(ProcessInstanceTaskResource::getUpdateBy, UserThreadContext.getUserId())
            .set(ProcessInstanceTaskResource::getUpdateTime, curDate)
            .eq(ProcessInstanceTaskResource::getProcessInstanceId, instanceId)
            .eq(ProcessInstanceTaskResource::getStatus, ProcessStatusEnum.RUNNING.getStatus())
            // 必须更新时间小于自己的数据
            .lt(ProcessInstanceTaskResource::getUpdateTime, curDate)
            .update();

        // 如果更新操作不成功，说明状态已变更，抛出异常提示用户状态已变更，请尝试重新操作
        if (!update) {
            throw new BusinessException(FlowableErrorNoEnum.STATUS_HAS_CHANGED_PLEASE_TRY_AGAIN);
        }
    }

    private boolean isFinalTask(CompleteResultDTO resultDTO) {
        return Boolean.FALSE.equals(resultDTO.getHasNextTask());
    }

    protected void addNewTask(ProcessInstanceTaskResource processInstanceTaskResource) {
        processInstanceTaskResourceService.save(processInstanceTaskResource);
    }

    protected void afterUpdateInstanceResource(ProcessInstanceResource processInstanceResource) {
        processInstanceResourceService.lambdaUpdate()
            .eq(ProcessInstanceResource::getId, processInstanceResource.getId())
            .eq(ProcessInstanceResource::getUpdateTime, processInstanceResource.getUpdateTime())
            .update(processInstanceResource);
    }


    protected void addResourceProcessInstanceResource(T resource,
        Integer processApplyType,
        ProcessDefinitionFeignDTO processDefinitionFeignDTO) {
        // 删除之前的审核实例,这样做可以避免不涉及的资源出现在全部申请列表里面
        processFeign.deleteProcessInstanceByResourceId(resource.getResourceId());
        log.info("resource: {}", resource);
        if (Objects.isNull(processDefinitionFeignDTO)) {
            return;
        }
        // 获取流程任务信息
        ProcessDefinitionTaskDTO taskDTO = processFeign.getProcessDefinitionTaskByProcessDefinitionId(
            processDefinitionFeignDTO.getId());

        // 生成新的申请实例
        ProcessInstanceResource newInstance = new ProcessInstanceResource();
        newInstance.setId(newId());
        newInstance.setProcessCode(generateProcessCode());
        newInstance.setProcessDefinitionId(processDefinitionFeignDTO.getId());
        newInstance.setResourceId(resource.getResourceId());
        newInstance.setApplicantUserId(UserThreadContext.getUserId());
        newInstance.setProcessApplyType(processApplyType);
        newInstance.setCurrentAssigneeRole(taskDTO.getAssigneeRole());
        newInstance.setCurrentAssignee(taskDTO.getAssignee());
        newInstance.setStatus(ProcessStatusEnum.RUNNING.getStatus());
        newInstance.setIsAvailable(AvailableEnum.AVAILABLE.getValue());
        processInstanceResourceService.save(newInstance);
    }


    /**
     * 获取分类的审核定义
     *
     * @param categoryId 类别ID
     * @return {@link ProcessDefinitionFeignDTO }
     */
    protected ProcessDefinitionFeignDTO getProcessDefinitionFeignDTO(String categoryId) {
        ProcessDefinitionFeignDTO processDefinitionFeignDTO;
        Categorys category = categorysService.getById(categoryId);
        String levelPath = category.getLevelPath();
        String[] parts = levelPath.replaceAll(REGEX, "").split("/");
        List<String> categoryIdList = new ArrayList<>();
        for (int i = parts.length - 1; i >= 0; i--) {
            categoryIdList.add(parts[i]);
        }
        processDefinitionFeignDTO = processFeign.getNearestProcessDefinitionByProcessContentId(categoryIdList);
        return processDefinitionFeignDTO;
    }


    private String generateProcessCode() {
        Object v = redisTemplate.opsForValue().get(ProcessRedisKeyEnum.PROCESS_CODE_NUM.getKey());
        if (v == null) {
            initProcessCode();
        }
        return "P" + redisTemplate.opsForValue().increment(ProcessRedisKeyEnum.PROCESS_CODE_NUM.getKey(), 1);
    }


    private void initProcessCode() {
        // 查询当前最大的审批流程编号
        String processCodeMax = processFeign.getProcessCodeMax();
        long currNum = 1000000L;
        if (StringUtils.isEmpty(processCodeMax)) {
            redisTemplate.opsForValue().set(ProcessRedisKeyEnum.PROCESS_CODE_NUM.getKey(), currNum);
        } else {
            Long redisNum = null;
            try {
                currNum = Long.parseLong(processCodeMax.substring(1));
                Object v = redisTemplate.opsForValue().get(ProcessRedisKeyEnum.PROCESS_CODE_NUM.getKey());
                if (v instanceof Integer) {
                    redisNum = ((Integer) v).longValue();
                } else if (v instanceof Long) {
                    redisNum = (Long) v;
                }
            } catch (NumberFormatException e) {
                log.error("发生异常", e);
                currNum = 1000000L;
            }
            if (redisNum == null || redisNum < currNum) {
                redisTemplate.opsForValue().set(ProcessRedisKeyEnum.PROCESS_CODE_NUM.getKey(), currNum);
            }
        }
    }

}
