package com.wunding.learn.flowable.api.design.template;

import static com.wunding.learn.common.util.string.StringUtil.newId;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.category.model.Categorys;
import com.wunding.learn.common.category.service.ICategorysService;
import com.wunding.learn.common.constant.course.CourseErrorNoEnum;
import com.wunding.learn.common.constant.flowable.FlowableErrorNoEnum;
import com.wunding.learn.common.constant.other.AvailableEnum;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.enums.process.ProcessDefinitionTypeEnum;
import com.wunding.learn.common.enums.process.ProcessStatusEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.flowable.api.constant.AuditStatusEnum;
import com.wunding.learn.flowable.api.constant.ProcessRedisKeyEnum;
import com.wunding.learn.flowable.api.dto.CompleteResultDTO;
import com.wunding.learn.flowable.api.dto.ProcessDefinitionDetailDTO;
import com.wunding.learn.flowable.api.dto.ProcessDefinitionFeignDTO;
import com.wunding.learn.flowable.api.dto.ProcessDefinitionTaskDTO;
import com.wunding.learn.flowable.api.dto.ProcessListBaseDTO;
import com.wunding.learn.flowable.api.dto.StartProcessInstanceDTO;
import com.wunding.learn.flowable.api.feign.ProcessFeign;
import com.wunding.learn.flowable.api.model.BaseAuditStatus;
import com.wunding.learn.flowable.api.model.ProcessInstanceResource;
import com.wunding.learn.flowable.api.model.ProcessInstanceTaskResource;
import com.wunding.learn.flowable.api.query.ProcessQueryDTO;
import com.wunding.learn.flowable.api.service.IProcessInstanceResourceService;
import com.wunding.learn.flowable.api.service.IProcessInstanceTaskResourceService;
import com.wunding.learn.user.api.service.UserFeign;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * <p> 提交审核模板 </p>
 *
 * <font color="red"><b>警告：</b></font> 修改文件的方法逻辑后,最好自己跑一下单元测试能不能通过,并且保证自己新增加的代码能够被单元测试覆盖
 *
 * <AUTHOR> href="mailto:<EMAIL>">chenhouhua</a>
 * @since 2025-06-10
 */
@Slf4j
public abstract class ApplyAuditTemplate<T extends BaseAuditStatus> {

    // 通过子类构造器注入，所以这里没有实现注入的方式
    protected ProcessFeign processFeign;
    protected IProcessInstanceResourceService processInstanceResourceService;
    protected IProcessInstanceTaskResourceService processInstanceTaskResourceService;
    protected ICategorysService categorysService;
    protected UserFeign userFeign;
    protected RedisTemplate<String, Object> redisTemplate;

    private static final String REGEX = "(^/)|(/$)";

    /**
     * 提交审核流程处理
     *
     * @param resourceId                资源id
     * @param processDefinitionTypeEnum 流程定义类型枚举
     */
    public void applyAudit(String resourceId, ProcessDefinitionTypeEnum processDefinitionTypeEnum) {
        T resource = getResource(resourceId);
        if (resource.getAuditStatus() == AuditStatusEnum.REJECTED.getCode()) {
            throw new BusinessException(FlowableErrorNoEnum.ERR_AUDIT_REJECT_FINISH);
        }
        if (resource.getAuditStatus() != AuditStatusEnum.DRAFT.getCode()) {
            throw new BusinessException(FlowableErrorNoEnum.EXIST_AUDIT_APPLY);
        }
        ProcessInstanceResource instanceResource = processInstanceResourceService.getProcessInstanceResourceByResourceId(
            resourceId);

        // 删除原有的user库中审批流程及任务（理论上不需要，可能又处理了逻辑，这样删除容易找不到资源的历史审核实例）
        processFeign.deleteProcessInstanceAndTaskByResourceId(List.of(resourceId));

        // 生成审批流程任务
        ProcessInstanceTaskResource processInstanceTaskResource = new ProcessInstanceTaskResource().setId(newId());
        //开启审核 - 同步审核实例到user库
        CompleteResultDTO completeResultDTO = startTransferProcess(resource, instanceResource,
            processDefinitionTypeEnum);
        if (Boolean.TRUE.equals(completeResultDTO.getOperateResult()) && Boolean.FALSE.equals(
            completeResultDTO.getHasNextTask())) {
            resource.setResourceAuditStatus(AuditStatusEnum.APPROVED.getCode());
            updateResource(resource);
        } else {
            //保存流程实例任务
            processInstanceTaskResource.setProcessInstanceId(instanceResource.getId());
            processInstanceTaskResource.setAssignee(completeResultDTO.getAssignee());
            processInstanceTaskResource.setAssigneeRole(completeResultDTO.getAssigneeRole());
            processInstanceTaskResource.setStatus(ProcessStatusEnum.RUNNING.getStatus());
            processInstanceTaskResource.setIsDel(GeneralJudgeEnum.NEGATIVE.getValue());
            processInstanceTaskResource.setIsAvailable(GeneralJudgeEnum.CONFIRM.getValue());
            processInstanceTaskResourceService.saveOrUpdate(processInstanceTaskResource);
            resource.setResourceAuditStatus(AuditStatusEnum.IN_REVIEW.getCode());
            updateResource(resource);
        }
    }


    /**
     * 撤销审核
     *
     * @param resourceId 资源id
     */
    public void cancelAudit(String resourceId) {
        // 1: 查询信息
        T resource = getResource(resourceId);
        if (StringUtils.isEmpty(resource.getCategoryId())) {
            throw new BusinessException(FlowableErrorNoEnum.ERR_HAS_NOT_CATEGORY);
        }
        if (resource.getAuditStatus() != AuditStatusEnum.IN_REVIEW.getCode()) {
            throw new BusinessException(FlowableErrorNoEnum.ERR_HAS_AUDIT_FINISH);
        }
        // 2: 撤销审核流程实例
        processInstanceResourceService.remove(
            new LambdaQueryWrapper<ProcessInstanceResource>().eq(ProcessInstanceResource::getResourceId,
                resourceId));
        processFeign.deleteProcessInstanceByResourceId(resourceId);
        // 3: 查询当前课程分类，判断是否需要审核
        Categorys category = categorysService.getById(resource.getCategoryId());
        if (Objects.isNull(category)) {
            // 分类不存在,课程不涉及审核
            resource.setResourceAuditStatus(AuditStatusEnum.NOT_APPLICABLE.getCode());
        } else {
            ProcessDefinitionFeignDTO processDefinitionFeignDTO = getProcessDefinitionFeignDTO(category.getId());
            // 5: 生成新的流程案例
            if (Objects.nonNull(processDefinitionFeignDTO)) {
                ProcessDefinitionTaskDTO taskDTO = processFeign.getProcessDefinitionTaskByProcessDefinitionId(
                    processDefinitionFeignDTO.getId());
                resource.setResourceAuditStatus(AuditStatusEnum.DRAFT.getCode());
                resource.setResourceIsPublish(AvailableEnum.NOT_AVAILABLE.getValue());
                ProcessInstanceResource dto = new ProcessInstanceResource();
                dto.setId(newId());
                dto.setProcessCode(generateProcessCode());
                dto.setProcessDefinitionId(processDefinitionFeignDTO.getId());
                dto.setResourceId(resourceId);
                dto.setApplicantUserId(UserThreadContext.getUserId());
                dto.setProcessApplyType(1);
                dto.setCurrentAssigneeRole(taskDTO.getAssigneeRole());
                dto.setCurrentAssignee(taskDTO.getAssignee());
                dto.setStatus(ProcessStatusEnum.RUNNING.getStatus());
                dto.setIsAvailable(AvailableEnum.AVAILABLE.getValue());
                processInstanceResourceService.save(dto);
            }
        }
        updateResource(resource);
    }

    /**
     * 获取信息审核状态
     *
     * @param resourceId 资源id
     * @return {@link Integer }
     */
    public Integer getResourceAuditStatus(String resourceId) {
        T resource = getResource(resourceId);
        if (Objects.isNull(resource)) {
            throw new BusinessException(CourseErrorNoEnum.ERR_COURSE_NULL);
        }
        return resource.getAuditStatus();
    }

    /**
     * 获取审核列表
     *
     * @param processQueryDTO 流程查询dto
     * @return {@link PageInfo }<{@link ProcessListBaseDTO }>
     */
    public PageInfo<ProcessListBaseDTO> getProcessList(ProcessQueryDTO processQueryDTO) {
        // 调用用户模块，获取所有未删除的课程审核流程
        PageInfo<ProcessListBaseDTO> courseProcessList = processFeign.getProcessDefinitionList(processQueryDTO);
        // 设置审核流程的审核内容
        courseProcessList.getList().forEach(dto -> {
            dto.setProcessScenes(Arrays.asList(dto.getProcessScene().split(",")));
            Categorys categorys = categorysService.getById(dto.getProcessContentId());
            dto.setProcessContent(Objects.isNull(categorys) ? "（该课程分类已被删除）" : categorys.getCategoryName());
        });
        return courseProcessList;
    }

    /**
     * 审核流程配置详情
     *
     * @param definitionId 定义id
     * @return {@link ProcessDefinitionDetailDTO }
     */
    public ProcessDefinitionDetailDTO detail(String definitionId) {
        // 获取审核流程详情
        ProcessDefinitionDetailDTO processDefinitionDetailDTO = processFeign.getProcessDefinitionDetail(definitionId);
        if (Objects.isNull(processDefinitionDetailDTO)) {
            throw new BusinessException(FlowableErrorNoEnum.ERR_PROCESS_DEFINITION_NOT_EXIST);
        }
        // 获取流程的审核内容
        Optional.ofNullable(categorysService.getById(processDefinitionDetailDTO.getProcessContentId()))
            .map(Categorys::getCategoryName).ifPresent(processDefinitionDetailDTO::setProcessContent);
        return processDefinitionDetailDTO;
    }


    /**
     * 插入资源场景审核处理
     *
     * @param resource 资源
     */
    public void insertResourceProcessSceneHandle(T resource) {
        ProcessDefinitionFeignDTO processDefinitionFeignDTO = null;
        String categoryId = resource.getCategoryId();
        if (!StringUtils.isEmpty(categoryId)) {
            processDefinitionFeignDTO = getProcessDefinitionFeignDTO(categoryId);
        }

        // 设置审核状态
        setAuditStatusForSave(resource, processDefinitionFeignDTO, UserThreadContext.getUserId());
    }

    /**
     * 更新资源场景审核处理
     *
     * @param newResource 新资源
     * @param oldResource 旧资源
     */
    public void updateResourceProcessSceneHandle(T newResource, T oldResource) {
        if (!isNeedToReAudit(oldResource.getCategoryId(), newResource.getCategoryId())) {
            return;
        }
        // TODO 如果有必要这里可以埋点更新instance中资源的名称
        String resourceId = newResource.getId();
        ProcessDefinitionFeignDTO processDefinitionFeignDTO = getProcessDefinitionFeignDTO(newResource.getCategoryId());
        if (Objects.nonNull(processDefinitionFeignDTO)) {
            if (processDefinitionFeignDTO.getProcessScene().contains("update")) {
                newResource.setResourceIsPublish(AvailableEnum.NOT_AVAILABLE.getValue());
                newResource.setResourceAuditStatus(AuditStatusEnum.DRAFT.getCode());
                processInstanceResourceService.remove(
                    new LambdaQueryWrapper<ProcessInstanceResource>().eq(ProcessInstanceResource::getResourceId,
                        resourceId));
                processFeign.deleteProcessInstanceByResourceId(resourceId);
                addResourceProcessInstanceResource(newResource, 1, processDefinitionFeignDTO);
            } else {
                processInstanceResourceService.remove(
                    new LambdaQueryWrapper<ProcessInstanceResource>().eq(ProcessInstanceResource::getResourceId,
                        resourceId));
                processFeign.deleteProcessInstanceByResourceId(resourceId);
                newResource.setResourceAuditStatus(AuditStatusEnum.NOT_APPLICABLE.getCode());
            }
        }
        // 变更的分类不存在审核时，课程审核设置为不涉及
        if (processDefinitionFeignDTO == null) {
            processFeign.deleteProcessInstanceByResourceId(resourceId);
            processInstanceResourceService.remove(
                new LambdaQueryWrapper<ProcessInstanceResource>().eq(ProcessInstanceResource::getResourceId,
                    resourceId));
            newResource.setResourceAuditStatus(AuditStatusEnum.NOT_APPLICABLE.getCode());
        }

    }

    private boolean isNeedToReAudit(String oldCategoryId, String thisCategoryId) {
        // 分类不修改时，前端不传分类id过来
        // true-需要重新审核
        return !StringUtils.isEmpty(thisCategoryId) && !StringUtils.equals(oldCategoryId, thisCategoryId);
    }

    /**
     * 按资源更新(需要各自资源模块进行实现)
     *
     * @param resource 资源
     */
    protected abstract void updateResource(T resource);

    /**
     * 获取资源信息(需要各自资源模块进行实现)
     *
     * @param resourceId 资源id
     * @return {@link T }
     */
    protected abstract T getResource(String resourceId);

    /**
     * 设置保存审核状态
     *
     * @param resource                  资源
     * @param processDefinitionFeignDTO 审核流程定义
     * @param userId                    用户id
     */
    void setAuditStatusForSave(T resource,
        ProcessDefinitionFeignDTO processDefinitionFeignDTO,
        String userId) {
        if (processDefinitionFeignDTO == null) {
            resource.setResourceAuditStatus(AuditStatusEnum.NOT_APPLICABLE.getCode());
        } else if (!processDefinitionFeignDTO.getProcessScene().contains("insert")) {
            resource.setResourceAuditStatus(AuditStatusEnum.NOT_APPLICABLE.getCode());
        } else {
            String exemptRoles = processDefinitionFeignDTO.getExemptRoles();
            List<String> exemptRolesList = JsonUtil.json2List(exemptRoles, String.class);
            List<String> roleIdByUserId = userFeign.getRoleIdByUserId(userId);
            boolean isExemptRoleUser = roleIdByUserId.stream().noneMatch(exemptRolesList::contains);
            if (!isExemptRoleUser) {
                resource.setResourceAuditStatus(AuditStatusEnum.APPROVED.getCode());
            } else {
                resource.setResourceAuditStatus(AuditStatusEnum.DRAFT.getCode());
                addResourceProcessInstanceResource(resource, 0, processDefinitionFeignDTO);
            }
        }
    }

    /**
     * 添加资源流程实例资源</br> （按理说这里不应该初始化一个审核的实例，但是为了获取此刻的审核流程的配置，所以才会提前进行审核实例的初始化）
     *
     * @param resource                  资源
     * @param processApplyType          流程应用类型
     * @param processDefinitionFeignDTO 流程定义假装数据
     */
    void addResourceProcessInstanceResource(T resource,
        Integer processApplyType,
        ProcessDefinitionFeignDTO processDefinitionFeignDTO) {
        log.info("resource: " + resource);
        if (Objects.isNull(processDefinitionFeignDTO)) {
            return;
        }
        // 获取流程任务信息
        ProcessDefinitionTaskDTO taskDTO = processFeign.getProcessDefinitionTaskByProcessDefinitionId(
            processDefinitionFeignDTO.getId());

        // 生成新的申请实例
        ProcessInstanceResource newInstance = new ProcessInstanceResource();
        newInstance.setId(newId());
        newInstance.setProcessCode(generateProcessCode());
        newInstance.setProcessDefinitionId(processDefinitionFeignDTO.getId());
        newInstance.setResourceId(resource.getId());
        newInstance.setApplicantUserId(UserThreadContext.getUserId());
        newInstance.setProcessApplyType(processApplyType);
        newInstance.setCurrentAssigneeRole(taskDTO.getAssigneeRole());
        newInstance.setCurrentAssignee(taskDTO.getAssignee());
        newInstance.setStatus(ProcessStatusEnum.RUNNING.getStatus());
        newInstance.setIsAvailable(AvailableEnum.AVAILABLE.getValue());
        processInstanceResourceService.save(newInstance);
    }


    /**
     * 获取分类的审核定义
     *
     * @param categoryId 类别ID
     * @return {@link ProcessDefinitionFeignDTO }
     */
    ProcessDefinitionFeignDTO getProcessDefinitionFeignDTO(String categoryId) {
        ProcessDefinitionFeignDTO processDefinitionFeignDTO;
        Categorys category = categorysService.getById(categoryId);
        String levelPath = category.getLevelPath();
        String[] parts = levelPath.replaceAll(REGEX, "").split("/");
        List<String> categoryIdList = new ArrayList<>();
        for (int i = parts.length - 1; i >= 0; i--) {
            categoryIdList.add(parts[i]);
        }
        processDefinitionFeignDTO = processFeign.getNearestProcessDefinitionByProcessContentId(categoryIdList);
        return processDefinitionFeignDTO;
    }

    /**
     * 开启审核
     *
     * @param resource                  资源
     * @param instanceResource          审核实例
     * @param processDefinitionTypeEnum 流程定义类型枚举
     * @return {@link CompleteResultDTO } 响应结果
     */
    CompleteResultDTO startTransferProcess(T resource, ProcessInstanceResource instanceResource,
        ProcessDefinitionTypeEnum processDefinitionTypeEnum) {
        String processCode = instanceResource.getProcessCode();
        String definitionId = instanceResource.getProcessDefinitionId();
        Integer processApplyType = instanceResource.getProcessApplyType();
        //走审核流程
        StartProcessInstanceDTO startProcessInstanceDTO = new StartProcessInstanceDTO();
        startProcessInstanceDTO.setProcessCode(processCode);
        startProcessInstanceDTO.setDefinitionType(processDefinitionTypeEnum.getType());
        startProcessInstanceDTO.setResourceId(resource.getId());
        startProcessInstanceDTO.setResourceName(resource.getResourceName());
        startProcessInstanceDTO.setResourceType(processDefinitionTypeEnum.getType());
        startProcessInstanceDTO.setApplicantUserId(UserThreadContext.getUserId());
        startProcessInstanceDTO.setDefinitionId(definitionId);
        startProcessInstanceDTO.setProcessApplyType(processApplyType);
        return processFeign.startProcessInstance(startProcessInstanceDTO);
    }


    private String generateProcessCode() {
        Object v = redisTemplate.opsForValue().get(ProcessRedisKeyEnum.PROCESS_CODE_NUM.getKey());
        if (v == null) {
            initProcessCode();
        }
        return "P" + redisTemplate.opsForValue().increment(ProcessRedisKeyEnum.PROCESS_CODE_NUM.getKey(), 1);
    }

    private void initProcessCode() {
        // 查询当前最大的审批流程编号
        String processCodeMax = processFeign.getProcessCodeMax();
        long currNum = 1000000L;
        if (StringUtils.isEmpty(processCodeMax)) {
            redisTemplate.opsForValue().set(ProcessRedisKeyEnum.PROCESS_CODE_NUM.getKey(), currNum);
        } else {
            Long redisNum = null;
            try {
                currNum = Long.parseLong(processCodeMax.substring(1));
                Object v = redisTemplate.opsForValue().get(ProcessRedisKeyEnum.PROCESS_CODE_NUM.getKey());
                if (v instanceof Integer) {
                    redisNum = ((Integer) v).longValue();
                } else if (v instanceof Long) {
                    redisNum = (Long) v;
                }
            } catch (NumberFormatException e) {
                log.error("发生异常", e);
                currNum = 1000000L;
            }
            if (redisNum == null || redisNum < currNum) {
                redisTemplate.opsForValue().set(ProcessRedisKeyEnum.PROCESS_CODE_NUM.getKey(), currNum);
            }
        }
    }
}

