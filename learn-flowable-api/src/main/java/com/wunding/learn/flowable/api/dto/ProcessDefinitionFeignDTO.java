package com.wunding.learn.flowable.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * </p> 流程定义表
 *
 * <AUTHOR> href="mailto:<EMAIL>">chenhouhua</a>
 * @since 2024-10-11
 */
@Data
@Schema(name = "ProcessDefinitionDTO", description = "流程定义表")
public class ProcessDefinitionFeignDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    @Schema(description = "主键")
    private String id;


    /**
     * 流程的类型(资源类型)
     */
    @Schema(description = "流程的类型(资源类型)")
    private String processType;

    /**
     * 流程的审核内容id （例：课程分类id）
     */
    @Schema(name = "流程的审核内容id（分类id）")
    private String processContentId ;

    /**
     * 流程的审核内容类型 （审核内容类型： 课程分类）
     */
    @Schema(name = "流程的审核内容类型（审核内容类型： 课程分类）")
    private String processContentType ;

    /**
     * 流程的名称 (统一资源审核流程名称相同，例：课程审核)
     */
    @Schema(description = "流程的名称")
    private String processName;

    /**
     * 流程场景（新增，修改）
     */
    @Schema(description = "流程场景（新增，修改）")
    private String processScene;

    /**
     * 流程的版本号
     */
    @Schema(description = "流程的版本号")
    private Integer version;


    /**
     * 免审核的角色（最多支持3个）
     */
    @Schema(description = "免审核的角色（最多支持3个）")
    private String exemptRoles;


    /**
     * 是否启用
     */
    @Schema(description = "是否启用")
    private Integer isAvailable;

    /**
     * 新增人
     */
    @Schema(description = "新增人")
    private String createBy;


    /**
     * 新增时间
     */
    @Schema(description = "新增时间")
    private Date createTime;


    /**
     * 最后编辑人
     */
    @Schema(description = "最后编辑人")
    private String updateBy;


    /**
     * 最后编辑时间
     */
    @Schema(description = "最后编辑时间")
    private Date updateTime;


}
