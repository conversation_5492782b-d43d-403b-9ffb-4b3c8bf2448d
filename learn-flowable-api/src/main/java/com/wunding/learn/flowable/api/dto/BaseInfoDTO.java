package com.wunding.learn.flowable.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p> 通用DTO数据
 *
 * <AUTHOR> href="mailto:<EMAIL>">chenhouhua</a>
 * @since 2024-10-12
 */
@Data
@Accessors(chain = true)
@Schema(name = "BaseDTOInfo", description = "基础DTO数据")
public class BaseInfoDTO {

    @Schema(description = "id")
    private String id;

    @Schema(description = "名称")
    private String name;

}