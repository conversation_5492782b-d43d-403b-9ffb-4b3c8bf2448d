package com.wunding.learn.flowable.api.query;

import com.wunding.learn.common.bean.BaseEntity;
import com.wunding.learn.common.enums.process.ProcessDefinitionTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <p> 流程查询dto
 *
 * <AUTHOR> href="mailto:<EMAIL>">chenhouhua</a>
 * @since 2025-06-11
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(name = "ProcessQueryDTO", description = "审核列表请求对象")
public class ProcessQueryDTO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 审核类型 {@link ProcessDefinitionTypeEnum}
     */
    private String processType;


}
