package com.wunding.learn.flowable.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(name = "MyApprovalResourceDTO", description = "我的审核资源DTO")
public class MyApprovalResourceDTO {

    @Schema(description = "审核实例id")
    private String instanceId;

    @Schema(description = "审核任务id")
    private String taskId;

    @Schema(description = "审核的资源id")
    private String resourceId;

    @Schema(description = "已经被当前用户审核")
    private Integer reviewedByCurrentUser;

    @Schema(description = "当前审核申请所处状态")
    private String status;

    @Schema(description = "审核实例扩展字段")
    private String extra;

}
