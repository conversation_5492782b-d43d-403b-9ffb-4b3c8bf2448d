package com.wunding.learn.flowable.api.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.flowable.api.model.ProcessInstanceResource;
import com.wunding.learn.flowable.api.model.ProcessInstanceTaskResource;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import org.apache.ibatis.annotations.Property;

/**
 * <p> 流程实例任务资源表 Mapper 接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">qrh</a>
 * @since 2024-10-28
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")})
public interface ProcessInstanceTaskResourceMapper extends BaseMapper<ProcessInstanceTaskResource> {


}
