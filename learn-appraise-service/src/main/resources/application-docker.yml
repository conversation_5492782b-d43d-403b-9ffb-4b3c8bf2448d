# 应用服务 WEB 访问端口
server:
  port: 28016
# 应用名称
spring:

  # 数据库设置
  datasource:
    driverClassName: com.mysql.cj.jdbc.Driver
    url: ************************************************************************************
    username: wdxuexi
    password: learnTest

  #redis
  # Redis服务器地址
  data:
    redis:
      host: redis
      # Redis服务器连接端口
      #port: 6379
      port: 6379
      # Redis数据库索引（默认为0）
      database: 5
      # Redis服务器连接密码（默认为空）
      #    password: 123456
      password: M0eHdhs9kk4VKLjRAb7J41
      # 连接超时时间（毫秒）
      timeout: 10000


    #rabbitmq 配置
  rabbitmq:
    host: rabbitmq
    port: 5672
    virtual-host: /
    username: guest
    password: guest


management:
  tracing:
    sampling:
      probability: 1.0
    enabled: true
    propagation:
      type: B3
  otlp:
    metrics:
      export:
        enabled: true
  zipkin:
    tracing:
      endpoint: http://jaeger-collector.observability.svc:14250

opentelemetry:
  traces:
    exporter: otlp
  service:
    name: ${spring.application.name}
  jaeger:
    endpoint: http://jaeger-collector.observability.svc:4317
    protocol: grpc
  instrumentation:
    feign:
      enabled: true
    spring-webmvc:
      enabled: true

xxl:
  job:
    admin:
      addresses: http://job:8080/xxl-job-admin
    executor:
      appName: ${spring.application.name}
      ip:
      port: 9999
      logPath: /data/applogs/xxl-job/jobhandler
      logRetentionDays: -1
    accessToken: vopeqn943epfaspdf


learn:
  service:
    learn-file-service: "http://file:8080"
    learn-user-service: "http://user:8080"
    learn-lecturer-service: "http://lecturer:8080"
    learn-course-service: "http://course:8080"
    learn-push-service: "http://push:8080"

app:
  signKey: bladexisapowerfulmicroservicearchitectureupgradedandoptimizedfromacommercialproject
  single:
    - api


#seata:
#  client:
#    undo:
#      log-serialization: kryo
#  config:
#    type: file
#  application-id: ${spring.application.name}
#  #  enable-auto-data-source-proxy: false
#  registry:
#    type: file
#  service:
#    grouplist:
#      default: seata:8091
#    vgroup-mapping:
#      springboot-seata-group: default
#  # seata 事务组编号 用于TC集群名
#  tx-service-group: springboot-seata-group


debug: false
############# springDoc配置 #############
springdoc:
  version: '@springdoc.version@'
  api-docs:
    groups:
      enabled: true
    enabled: true
  swagger-ui:
    operationsSorter: function
    tagsSorter: alpha
    docExpansion: none
    # 兼容一下老swagger地址路径
    path: /swagger-ui.html
  group-configs:
    - group: api
      packages-to-scan: com.wunding.learn.appraise.service.client.rest
    - group: web
      packages-to-scan: com.wunding.learn.appraise.service.admin.rest
  # 不配置下面 get请求为对象会变为 json
  default-flat-param-object: true
  disable-i18n: true
