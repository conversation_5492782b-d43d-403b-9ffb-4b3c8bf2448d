package com.wunding.learn.appraise.service.component;

import com.wunding.learn.appraise.service.model.AppraiseFileViewLimit;
import com.wunding.learn.appraise.service.service.IAppraiseFileViewLimitService;
import com.wunding.learn.common.viewlimit.constant.LimitTable;
import com.wunding.learn.common.viewlimit.service.impl.BaseViewLimitServiceImpl;
import java.util.List;
import jakarta.annotation.Resource;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * 课程下发范围处理服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">yuxueyi</a>
 * @version V7.0.0
 * @since 2022/5/13  18:17
 */
@Component("appraiseFileViewLimitComponent")
public class AppraiseFileViewLimitComponent extends BaseViewLimitServiceImpl<AppraiseFileViewLimit> implements
    CommandLineRunner {

    @Resource
    private IAppraiseFileViewLimitService appraiseFileViewLimitService;

    public AppraiseFileViewLimitComponent() {
        super(LimitTable.TB_APPRAISE_FILE_VIEWLIMIT);
    }

    @Override
    public void run(String... args) {
        // 待处理
    }

    @Override
    public void saveBatch(List<AppraiseFileViewLimit> baseViewLimits) {
        appraiseFileViewLimitService.saveBatch(baseViewLimits);
    }

    @Override
    public void removeBatchByIds(List<AppraiseFileViewLimit> baseViewLimits) {
        appraiseFileViewLimitService.removeBatchByIds(baseViewLimits);
    }
}
