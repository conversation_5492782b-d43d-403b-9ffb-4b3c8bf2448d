<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.appraise.service.mapper.MeetingRefereeMapper">
    <!-- 开启二级缓存 -->
    <!--
<cache type="com.wunding.learn.common.mybatis.cache.MyBatisPlusRedisCache"/>
-->

    <!-- 使用缓存 -->
    <cache-ref namespace="com.wunding.learn.appraise.service.mapper.MeetingRefereeMapper"/>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wunding.learn.appraise.service.model.MeetingReferee">
        <!--@Table meeting_referee-->
        <result column="id" jdbcType="VARCHAR"
          property="id"/>
        <result column="meeting_id" jdbcType="VARCHAR"
          property="appraiseId"/>
        <result column="user_id" jdbcType="VARCHAR"
          property="userId"/>
        <result column="user_name" jdbcType="VARCHAR"
          property="userName"/>
        <result column="login_name" jdbcType="VARCHAR"
          property="loginName"/>
        <result column="org_id" jdbcType="VARCHAR"
          property="orgId"/>
        <result column="level_path" jdbcType="VARCHAR"
          property="levelPath"/>
        <result column="level_path_name" jdbcType="VARCHAR"
          property="levelPathName"/>
        <result column="weight" jdbcType="INTEGER"
          property="weight"/>
        <result column="is_anonymous" jdbcType="INTEGER"
          property="isAnonymous"/>
        <result column="is_del" jdbcType="INTEGER"
          property="isDel"/>
        <result column="create_by" jdbcType="VARCHAR"
          property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP"
          property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR"
          property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP"
          property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , meeting_id, user_id, user_name, login_name, org_id, level_path, level_path_name, weight, is_anonymous, is_del, create_by, create_time, update_by, update_time
    </sql>

    <!--嵌套子查询-待优化-->
    <select  id="queryPage" resultType="com.wunding.learn.appraise.service.admin.dto.MeetingRefereeDTO"
      useCache="false">
        select * from (
        select
        tar.id,
        tar.meeting_id as appraiseId,
        tar.user_id,
        g.id as org_id,
        tar.weight,
        tar.is_anonymous,
        tar.login_name loginName,
        g.level_path_name orgName,
        tar.user_name fullName,(
        select
        count( trps.id )
        from
        meeting_referee_provider_score trps  inner join meeting_provider mp on trps.provider_id=mp.id and mp.is_del=0
        where
        trps.referee_id = tar.id
        and trps.meeting_id = #{params.appraiseId}
        ) as appraisedUserCount,
        ((
        select
        count( id )
        from
        meeting_provider tap
        where
        tap.meeting_id = #{params.appraiseId}
        and tap.is_del = 0
        ) - ( select count( trps.id ) from meeting_referee_provider_score trps
            inner join meeting_provider mp on trps.provider_id=mp.id and mp.is_del=0
            where trps.referee_id = tar.id and
        trps.meeting_id = #{params.appraiseId})) as notAppraisedUserCount
        from
        meeting_referee tar
        left join sys_org g on g.id = tar.org_id
        <where>
            and tar.meeting_id = #{params.appraiseId}
            and tar.is_del = 0
            <if test="params.userIdList != null and params.userIdList.size() > 0">
                and tar.user_id in
                <foreach item="item" collection="params.userIdList" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="params.orgId != null and '' != params.orgId">
                and g.id = #{params.orgId}
            </if>
        </where>
        ) a
        <where>
            <if test="params.isFinish != null ">
                <if test="params.isFinish == 1">
                    and a.notAppraisedUserCount = 0
                </if>
                <if test="params.isFinish == 0">
                    and a.notAppraisedUserCount > 0
                </if>
            </if>
        </where>
    </select>

    <select id="selectAppraiseId" resultType="java.lang.String">
        select
        distinct meeting_id
        from
        meeting_referee tar
        <where>
            and (
            instr(tar.user_name,#{keyWord}) > 0 or instr(tar.login_name,#{keyWord}) > 0
            )
        </where>
    </select>
</mapper>
