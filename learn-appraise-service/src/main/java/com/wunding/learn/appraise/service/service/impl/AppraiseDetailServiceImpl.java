package com.wunding.learn.appraise.service.service.impl;


import static com.wunding.learn.common.util.math.NumberOperationUtils.ONE_HUNDRED_NUM;

import com.alibaba.excel.util.ListUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.wunding.learn.appraise.service.admin.dto.AppraiseDetailDTO;
import com.wunding.learn.appraise.service.admin.dto.AppraiseDetailDTO.TemplateCollect;
import com.wunding.learn.appraise.service.admin.dto.AppraiseDetailListQuery;
import com.wunding.learn.appraise.service.admin.dto.AppraiseHistoryDetailDTO;
import com.wunding.learn.appraise.service.admin.dto.AppraiseHistoryListQuery;
import com.wunding.learn.appraise.service.admin.dto.AppraiseShowDetailDTO;
import com.wunding.learn.appraise.service.admin.dto.AppraiseShowListDataQuery;
import com.wunding.learn.appraise.service.constant.AppraiseConstants;
import com.wunding.learn.appraise.service.model.AppraiseTemplate;
import com.wunding.learn.appraise.service.service.AppraiseDetailService;
import com.wunding.learn.appraise.service.service.IAppraiseProviderService;
import com.wunding.learn.appraise.service.service.IAppraiseTemplateService;
import com.wunding.learn.appraise.service.service.IProviderSingleScoreService;
import com.wunding.learn.appraise.service.service.IRefereeProviderScoreService;
import com.wunding.learn.common.constant.other.CommonConstants;
import com.wunding.learn.common.constant.other.DelEnum;
import com.wunding.learn.common.util.bean.SpringUtil;
import com.wunding.learn.common.util.date.DateHelper;
import com.wunding.learn.common.util.string.TranslateUtil;
import com.wunding.learn.file.api.component.ExportComponent;
import com.wunding.learn.file.api.constant.ExportBizType;
import com.wunding.learn.file.api.constant.ExportFileNameEnum;
import com.wunding.learn.file.api.dto.AbstractExportDataDTO;
import com.wunding.learn.file.api.dto.AbstractExportNoEntityDataDTO;
import com.wunding.learn.file.api.dto.IExportDataDTO;
import com.wunding.learn.file.api.dto.IExportNoEntityDataDTO;
import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 评价详情
 *
 * <AUTHOR>
 * @date 2021/12/5 12:00
 */
@Service("appraiseDetailServiceI")
public class AppraiseDetailServiceImpl implements AppraiseDetailService {

    private static final String APPRAISEDETAILSERVICE_BEAN_NAME = "appraiseDetailServiceI";

    @Resource
    private IAppraiseTemplateService appraiseTemplateService;
    @Resource
    private IProviderSingleScoreService providerSingleScoreService;
    @Resource
    @Lazy
    private IAppraiseProviderService appraiseProviderService;
    @Resource
    private IRefereeProviderScoreService refereeProviderScoreService;
    @Resource
    private ExportComponent exportComponent;

    @Override
    public List<AppraiseTemplate> getTableHead(AppraiseDetailDTO appraiseDetailVo) {
        LambdaQueryWrapper<AppraiseTemplate> templateLambdaQueryWrapper = Wrappers.lambdaQuery();
        templateLambdaQueryWrapper.eq(AppraiseTemplate::getAppraiseId, appraiseDetailVo.getAppraiseId());
        templateLambdaQueryWrapper.eq(AppraiseTemplate::getIsDel, DelEnum.NOT_DELETE.getValue());
        templateLambdaQueryWrapper.orderByAsc(AppraiseTemplate::getType);
        if (appraiseDetailVo.getType() != null) {
            templateLambdaQueryWrapper.eq(AppraiseTemplate::getType, appraiseDetailVo.getType());
        }
        templateLambdaQueryWrapper.select(AppraiseTemplate::getId, AppraiseTemplate::getTitle);
        templateLambdaQueryWrapper.orderByAsc(AppraiseTemplate::getNum);
        return appraiseTemplateService.list(templateLambdaQueryWrapper);
    }

    @Override
    public PageInfo<Map<String, Object>> queryPage(AppraiseDetailListQuery query) {
        AppraiseDetailDTO appraiseDetailVo = new AppraiseDetailDTO();

        BeanUtils.copyProperties(query, appraiseDetailVo);

        PageInfo<AppraiseDetailDTO> pageInfo = PageMethod.startPage(query.getPageNo(), query.getPageSize())
            .doSelectPageInfo(() -> providerSingleScoreService.queryPage(appraiseDetailVo));

        List<Map<String, Object>> data = new ArrayList<>();
        for (AppraiseDetailDTO appraiseDetailDTO : pageInfo.getList()) {
            Map<String, Object> map = new HashMap<>();
            map.put("appraiseId", appraiseDetailDTO.getAppraiseId());
            map.put("providerId", appraiseDetailDTO.getProviderId());
            map.put("providerName", appraiseDetailDTO.getProviderName());
            map.put("type", appraiseDetailDTO.getType());
            map.put("totalScore", appraiseDetailDTO.getTotalScore()
                .divide(new BigDecimal(ONE_HUNDRED_NUM), AppraiseConstants.APPRAISE_SCORE_PRECISION,
                    BigDecimal.ROUND_HALF_DOWN).toString());
            map.put("status", appraiseDetailDTO.getStatus());
            map.put("goDownNum", appraiseDetailDTO.getGoDownNum());
            map.put("curRefereeNum", appraiseDetailDTO.getCurRefereeNum());
            map.put("updateTime", appraiseDetailDTO.getUpdateTime());
            for (TemplateCollect templateCollect : appraiseDetailDTO.getTemplateCollects()) {
                map.put(templateCollect.getTemplateId(), null == templateCollect.getScore() ? "" :
                    templateCollect.getScore()
                        .divide(new BigDecimal(ONE_HUNDRED_NUM), AppraiseConstants.APPRAISE_SCORE_PRECISION,
                            BigDecimal.ROUND_HALF_DOWN).toString());
            }
            data.add(map);
        }

        PageInfo<Map<String, Object>> page = new PageInfo<>(data);
        page.setPageNum(pageInfo.getPageNum());
        page.setPageSize(pageInfo.getPageSize());

        return page;
    }

    @Override
    public String getUserNameById(String providerId) {
        return appraiseProviderService.getUserNameById(providerId);
    }

    @Override
    public PageInfo<Map<String, Object>> queryShowPage(AppraiseShowListDataQuery query) {
        AppraiseShowDetailDTO appraiseShowDetailVo = new AppraiseShowDetailDTO();
        BeanUtils.copyProperties(query, appraiseShowDetailVo);
        PageInfo<AppraiseShowDetailDTO> pageInfo = PageMethod.startPage(query.getPageNo(), query.getPageSize())
            .doSelectPageInfo(() -> providerSingleScoreService.queryShowPage(appraiseShowDetailVo));

        // 处理数据
        List<AppraiseShowDetailDTO> appraiseShowDetailDTOList = pageInfo.getList();
        // 封装转换后的结果集
        List<Map<String, Object>> data = new ArrayList<>();
        // 结果为空返回
        if (CollectionUtils.isNotEmpty(appraiseShowDetailDTOList)) {

            for (AppraiseShowDetailDTO showDetailVo : appraiseShowDetailDTOList) {
                Map<String, Object> map = new HashMap<>(12);
                map.put("refereeId", showDetailVo.getRefereeId());
                map.put("refereeName", showDetailVo.getRefereeName());

                // 将分数除以100 保留两位小数
                BigDecimal score = showDetailVo.getTotalScore();
                if (score != null) {
                    map.put("totalScore", showDetailVo.getTotalScore()
                        .divide(new BigDecimal(ONE_HUNDRED_NUM), AppraiseConstants.APPRAISE_SCORE_PRECISION,
                            BigDecimal.ROUND_HALF_DOWN).toString());
                }

                // 添加判断，防止空指针
                Date updateTime = showDetailVo.getUpdateTime();
                if (updateTime != null) {
                    map.put("updateTime", showDetailVo.getUpdateTime());
                }

                // 获取当前被评人所有的被评单项 如果其单项题型为1 评分则封装评分，为2 封装评语
                handleAppraiseShowDetail(showDetailVo, map);
                data.add(map);
            }
        }

        PageInfo<Map<String, Object>> page = new PageInfo<>(data);
        page.setPageNum(pageInfo.getPageNum());
        page.setPageSize(pageInfo.getPageSize());

        return page;
    }

    private void handleAppraiseShowDetail(AppraiseShowDetailDTO showDetailVo, Map<String, Object> map) {
        List<TemplateCollect> templateCollects = showDetailVo.getTemplateCollects();

        for (TemplateCollect templateCollect : templateCollects) {
            if (templateCollect.getType() == AppraiseConstants.TEMPLATE_TYPE_SCORE) {
                map.put(templateCollect.getTemplateId(), templateCollect.getScore()
                    .divide(new BigDecimal(ONE_HUNDRED_NUM), AppraiseConstants.APPRAISE_SCORE_PRECISION,
                        BigDecimal.ROUND_HALF_DOWN).toString());
            } else if (templateCollect.getType() == AppraiseConstants.TEMPLATE_TYPE_QUESTIONS) {
                map.put(templateCollect.getTemplateId(), templateCollect.getRemark());
            }
        }
    }


    /**
     * 获取打分历史
     *
     * @param query 查询参数
     */
    @Override
    public PageInfo<AppraiseHistoryDetailDTO> queryHistoryPage(AppraiseHistoryListQuery query) {

        AppraiseHistoryDetailDTO appraiseHistoryDetailVo = new AppraiseHistoryDetailDTO();
        BeanUtils.copyProperties(query, appraiseHistoryDetailVo);

        if (StringUtils.isNotBlank(query.getRefereeIds())) {
            appraiseHistoryDetailVo.setRefereeIdsVo(
                TranslateUtil.translateBySplit(query.getRefereeIds(), String.class));
        }

        PageInfo<AppraiseHistoryDetailDTO> page = PageMethod.startPage(query.getPageNo(), query.getPageSize())
            .doSelectPageInfo(() -> providerSingleScoreService.queryHistoryPage(appraiseHistoryDetailVo));

        // 将分数除以100 保留两位小数，向下取整
        List<AppraiseHistoryDetailDTO> result = page.getList();
        for (AppraiseHistoryDetailDTO historyDetailVo : result) {
            historyDetailVo.setScored(historyDetailVo.getWeightedScore()
                .divide(new BigDecimal(historyDetailVo.getWeight()), AppraiseConstants.APPRAISE_SCORE_PRECISION,
                    BigDecimal.ROUND_HALF_DOWN).toString());
            historyDetailVo.setWeightedScored(historyDetailVo.getWeightedScore()
                .divide(new BigDecimal(ONE_HUNDRED_NUM), AppraiseConstants.APPRAISE_SCORE_PRECISION,
                    BigDecimal.ROUND_HALF_DOWN).toString());
        }
        page.setList(result);
        return page;
    }

    /**
     * 评委评分记录删除
     *
     * @param ids
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(String ids) {
        if (StringUtils.isEmpty(ids)) {
            return;
        }
        String[] split = ids.split(CommonConstants.A_COMMA_IN_ENGLISH);
        for (String id : split) {
            refereeProviderScoreService.deleteRefereeProviderScore(id);
        }
    }

    @Override
    public void exportData(AppraiseDetailListQuery query) {
        AppraiseDetailDTO appraiseDetailVo = new AppraiseDetailDTO();
        appraiseDetailVo.setAppraiseId(query.getAppraiseId());
        //被评单项类型 题型(1:打分,2:问答)
        appraiseDetailVo.setType(1);
        //通过查询标签id与标签名 获得表头
        List<AppraiseTemplate> tableHead = getTableHead(appraiseDetailVo);

        IExportNoEntityDataDTO exportDataDTO = new AbstractExportNoEntityDataDTO<AppraiseDetailService, AppraiseDetailDTO>(
            query) {
            @Override
            protected AppraiseDetailService getBean() {
                return SpringUtil.getBean(APPRAISEDETAILSERVICE_BEAN_NAME, AppraiseDetailService.class);
            }

            @Override
            protected List<List<Object>> getPageInfo() {
                AppraiseDetailDTO appraiseDetailVo = new AppraiseDetailDTO();

                BeanUtils.copyProperties(query, appraiseDetailVo);

                PageInfo<AppraiseDetailDTO> pageInfo = PageMethod.startPage(query.getPageNo(), query.getPageSize())
                    .doSelectPageInfo(() -> {
                        providerSingleScoreService.queryPage(appraiseDetailVo);
                    });
                List<List<Object>> data = ListUtils.newArrayList();
                if (Objects.nonNull(pageInfo.getList())) {
                    for (AppraiseDetailDTO appraiseDetailDTO : pageInfo.getList()) {
                        List<Object> detailDTOS = ListUtils.newArrayList();
                        detailDTOS.add(appraiseDetailDTO.getProviderName());
                        detailDTOS.add(appraiseDetailDTO.getTotalScore()
                            .divide(new BigDecimal(ONE_HUNDRED_NUM), AppraiseConstants.APPRAISE_SCORE_PRECISION,
                                BigDecimal.ROUND_HALF_DOWN).toString());
                        detailDTOS.add(appraiseDetailDTO.getCurRefereeNum());

                        if (tableHead != null && tableHead.size() > 0) {
                            handleTableHead(appraiseDetailDTO, detailDTOS, tableHead);
                        }
                        detailDTOS.add(appraiseDetailDTO.getUpdateTime() == null ? "" :
                            DateHelper.formatDate(appraiseDetailDTO.getUpdateTime(), DateHelper.YYYYMMDD_HHMMSS));
                        data.add(detailDTOS);
                    }
                }
                return data;
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.AppraiseDetail;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.AppraiseDetail.getType();
            }

        };

        List<List<String>> head = new ArrayList<>(1);
        head.add(Arrays.asList("被评人"));
        head.add(Arrays.asList("总分"));
        head.add(Arrays.asList("已评价人数"));
        if (Objects.nonNull(tableHead)) {
            tableHead.forEach(appraiseTemplate -> head.add(Arrays.asList(appraiseTemplate.getTitle())));
        }
        head.add(Arrays.asList("完成时间"));
        exportComponent.exportNoEntityRecord(exportDataDTO, head);
    }

    private void handleTableHead(AppraiseDetailDTO appraiseDetailDTO, List<Object> detailDTOS,
        List<AppraiseTemplate> tableHead) {
        Map<String, TemplateCollect> map = appraiseDetailDTO.getTemplateCollects().stream()
            .collect(Collectors.toMap(TemplateCollect::getTemplateId, Function.identity()));
        TemplateCollect templateCollect;
        for (int i = 0; i < tableHead.size(); i++) {
            templateCollect = map.get(tableHead.get(i).getId());
            if (templateCollect != null) {
                Object score = (templateCollect.getScore() != null) ?
                    templateCollect.getScore().divide(new BigDecimal(ONE_HUNDRED_NUM),
                        AppraiseConstants.APPRAISE_SCORE_PRECISION, BigDecimal.ROUND_HALF_DOWN)
                        + "" : templateCollect.getRemark();
                detailDTOS.add(score == null ? "" : score);
            } else {
                detailDTOS.add("");
            }
        }
    }

    @Override
    public void exportDetailData(AppraiseShowListDataQuery query) {

        IExportNoEntityDataDTO exportDataDTO = new AbstractExportNoEntityDataDTO<AppraiseDetailService, AppraiseShowDetailDTO>(
            query) {
            @Override
            protected AppraiseDetailService getBean() {
                return SpringUtil.getBean(APPRAISEDETAILSERVICE_BEAN_NAME, AppraiseDetailService.class);
            }

            @Override
            protected List<List<Object>> getPageInfo() {
                AppraiseShowDetailDTO appraiseShowDetailVo = new AppraiseShowDetailDTO();
                BeanUtils.copyProperties(query, appraiseShowDetailVo);
                PageInfo<AppraiseShowDetailDTO> pageInfo = PageMethod.startPage(query.getPageNo(), query.getPageSize())
                    .doSelectPageInfo(() -> providerSingleScoreService.queryShowPage(appraiseShowDetailVo));
                AppraiseDetailDTO appraiseDetailVo = new AppraiseDetailDTO();
                appraiseDetailVo.setAppraiseId(appraiseShowDetailVo.getAppraiseId());
                List<AppraiseTemplate> tableHead = getBean().getTableHead(appraiseDetailVo);
                List<List<Object>> data = ListUtils.newArrayList();
                if (Objects.nonNull(pageInfo.getList())) {
                    for (AppraiseShowDetailDTO appraiseShowDetailDTO : pageInfo.getList()) {
                        List<Object> detailDTOS = ListUtils.newArrayList();
                        detailDTOS.add(appraiseShowDetailDTO.getRefereeName());
                        detailDTOS.add(
                            DateHelper.formatDate(appraiseShowDetailDTO.getUpdateTime(), DateHelper.YYYYMMDD_HHMMSS));
                        detailDTOS.add(appraiseShowDetailDTO.getTotalScore()
                            .divide(new BigDecimal(ONE_HUNDRED_NUM), AppraiseConstants.APPRAISE_SCORE_PRECISION,
                                BigDecimal.ROUND_HALF_DOWN).toString());
                        if (Objects.nonNull(tableHead)) {
                            handleDetailTableHead(tableHead, appraiseShowDetailDTO, detailDTOS);
                        }
                        data.add(detailDTOS);
                    }
                }
                return data;
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.AppraiseShowDetail;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.AppraiseShowDetail.getType();
            }

        };

        List<List<String>> head = new ArrayList<>(1);
        head.add(Arrays.asList("评价人"));
        head.add(Arrays.asList("完成时间"));
        head.add(Arrays.asList("总分"));
        AppraiseDetailDTO appraiseDetailVo = new AppraiseDetailDTO();
        appraiseDetailVo.setAppraiseId(query.getAppraiseId());
        //根据评价表id获取评分项id与标题
        List<AppraiseTemplate> tableHead = getTableHead(appraiseDetailVo);
        if (Objects.nonNull(tableHead)) {
            tableHead.forEach(appraiseTemplate -> head.add(Arrays.asList(appraiseTemplate.getTitle())));
        }

        exportComponent.exportNoEntityRecord(exportDataDTO, head);
    }

    private void handleDetailTableHead(List<AppraiseTemplate> tableHead, AppraiseShowDetailDTO appraiseShowDetailDTO,
        List<Object> detailDTOS) {
        Map<String, TemplateCollect> collect = appraiseShowDetailDTO.getTemplateCollects().stream()
            .collect(
                Collectors.toMap(TemplateCollect::getTemplateId,
                    templateCollect -> templateCollect));
        for (AppraiseTemplate appraiseTemplate : tableHead) {
            TemplateCollect templateCollect = collect.get(appraiseTemplate.getId());
            if (templateCollect.getType() == AppraiseConstants.TEMPLATE_TYPE_SCORE) {
                detailDTOS.add(templateCollect.getScore()
                    .divide(new BigDecimal(ONE_HUNDRED_NUM),
                        AppraiseConstants.APPRAISE_SCORE_PRECISION,
                        BigDecimal.ROUND_HALF_DOWN).toString());
            } else if (templateCollect.getType() == AppraiseConstants.TEMPLATE_TYPE_QUESTIONS) {
                detailDTOS.add(templateCollect.getRemark());
            }
        }
    }

    @Override
    public void exportHistoryData(AppraiseHistoryListQuery query) {

        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<AppraiseDetailService, AppraiseHistoryDetailDTO>(
            query) {

            @Override
            protected AppraiseDetailService getBean() {
                return SpringUtil.getBean(APPRAISEDETAILSERVICE_BEAN_NAME, AppraiseDetailService.class);
            }

            @Override
            protected PageInfo<AppraiseHistoryDetailDTO> getPageInfo() {
                return getBean().queryHistoryPage((AppraiseHistoryListQuery) pageQueryDTO);

            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.AppraiseHistory;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.AppraiseHistory.getType();
            }

        };

        exportComponent.exportRecord(exportDataDTO);
    }
}
