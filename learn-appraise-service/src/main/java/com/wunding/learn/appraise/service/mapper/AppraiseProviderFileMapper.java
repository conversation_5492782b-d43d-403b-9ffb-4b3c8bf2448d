package com.wunding.learn.appraise.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.appraise.service.admin.dto.AppraiseProviderFileDTO;
import com.wunding.learn.appraise.service.model.AppraiseProviderFile;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;

/**
 * <p> 学员材料表  Mapper 接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">xiao8</a>
 * @since 2022-08-08
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")
})
public interface AppraiseProviderFileMapper extends BaseMapper<AppraiseProviderFile> {

    List<AppraiseProviderFileDTO> queryPage(@Param("params") AppraiseProviderFileDTO appraiseProviderFileVo);

    AppraiseProviderFileDTO getAppraiseProviderFileById(String id);

    int countAppraiseProviderFile(@Param("params") AppraiseProviderFileDTO appraiseProviderFileVo);

    /**
     * 更新被评人转码状态
     *
     * @param id
     * @param status
     * @return
     */
    int updateAppraiseProviderFileTransformStatus(@Param("id") String id, @Param("status") int status);

    /**
     * *获取材料提交结束时间
     * @param id
     * @return
     */
    Date getEndTime(String id);

    /**
     * *获取评价的id
     * @param id
     * @return
     */
    String getAppraiseId(String id);
}
