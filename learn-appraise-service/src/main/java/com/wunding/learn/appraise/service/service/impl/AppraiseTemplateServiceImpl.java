package com.wunding.learn.appraise.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.appraise.service.mapper.AppraiseTemplateMapper;
import com.wunding.learn.appraise.service.model.AppraiseTemplate;
import com.wunding.learn.appraise.service.service.IAppraiseTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p> 评价内容模板 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">xiao8</a>
 * @since 2022-08-08
 */
@Slf4j
@Service("appraiseTemplateService")
public class AppraiseTemplateServiceImpl extends ServiceImpl<AppraiseTemplateMapper, AppraiseTemplate> implements
    IAppraiseTemplateService {

}
