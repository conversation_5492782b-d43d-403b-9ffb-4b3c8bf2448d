package com.wunding.learn.appraise.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.appraise.service.admin.dto.AppraiseDetailDTO;
import com.wunding.learn.appraise.service.admin.dto.AppraiseHistoryDetailDTO;
import com.wunding.learn.appraise.service.admin.dto.AppraiseShowDetailDTO;
import com.wunding.learn.appraise.service.constant.AppraiseConstants;
import com.wunding.learn.appraise.service.mapper.AppraiseScoreMapper;
import com.wunding.learn.appraise.service.mapper.AppraiseTemplateMapper;
import com.wunding.learn.appraise.service.mapper.ProviderSingleScoreMapper;
import com.wunding.learn.appraise.service.model.AppraiseTemplate;
import com.wunding.learn.appraise.service.model.ProviderSingleScore;
import com.wunding.learn.appraise.service.service.IProviderSingleScoreService;
import com.wunding.learn.common.util.string.StringUtil;
import java.util.List;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p> 被评价人单项汇总表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">xiao8</a>
 * @since 2022-08-09
 */
@Slf4j
@Service("providerSingleScoreService")
public class ProviderSingleScoreServiceImpl extends
    ServiceImpl<ProviderSingleScoreMapper, ProviderSingleScore> implements IProviderSingleScoreService {

    @Resource
    private AppraiseScoreMapper appraiseScoreMapper;

    @Resource
    private AppraiseTemplateMapper appraiseTemplateMapper;

    @Override
    public List<AppraiseDetailDTO> queryPage(AppraiseDetailDTO appraiseDetailVo) {
        return baseMapper.queryPage(appraiseDetailVo);
    }

    @Override
    public List<AppraiseShowDetailDTO> queryShowPage(AppraiseShowDetailDTO appraiseShowDetailVo) {
        return baseMapper.queryShowPage(appraiseShowDetailVo);
    }

    @Override
    public List<AppraiseHistoryDetailDTO> queryHistoryPage(AppraiseHistoryDetailDTO appraiseHistoryDetailVo) {
        return baseMapper.queryHistoryPage(appraiseHistoryDetailVo);
    }

    /**
     * 汇总单项汇总表
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void sumProviderSingleScore(String appraiseId,
        String providerId) {

        LambdaQueryWrapper<AppraiseTemplate> appraiseTemplateLambdaQueryWrapper = new LambdaQueryWrapper<>();
        appraiseTemplateLambdaQueryWrapper.eq(AppraiseTemplate::getAppraiseId, appraiseId);
        appraiseTemplateLambdaQueryWrapper.eq(AppraiseTemplate::getType, AppraiseConstants.TEMPLATE_TYPE_SCORE);
        appraiseTemplateLambdaQueryWrapper.orderByAsc(AppraiseTemplate::getNum);
        List<AppraiseTemplate> appraiseTemplates =
            appraiseTemplateMapper.selectList(appraiseTemplateLambdaQueryWrapper);
        for (AppraiseTemplate appraiseTemplate : appraiseTemplates) {

            //已评人数
            int countRefereeNum = appraiseScoreMapper.countRefereeNum(providerId, appraiseId, appraiseTemplate.getId());
            if (0 == countRefereeNum) {
                //删除单项汇总记录并直接返回
                LambdaQueryWrapper<ProviderSingleScore> removeParams = new LambdaQueryWrapper<>();
                removeParams.eq(ProviderSingleScore::getAppraiseId, appraiseId);
                removeParams.eq(ProviderSingleScore::getProviderId, providerId);
                removeParams.eq(ProviderSingleScore::getTemplateId, appraiseTemplate.getId());
                remove(removeParams);
                continue;
            }

            //单项平均分
            int sumSingleScore = appraiseScoreMapper.sumSingleScore(providerId, appraiseId, appraiseTemplate.getId());

            ProviderSingleScore saveOrUpdateProviderSingleScore = new ProviderSingleScore();
            saveOrUpdateProviderSingleScore.setAppraiseId(appraiseId);
            saveOrUpdateProviderSingleScore.setTemplateId(appraiseTemplate.getId());
            saveOrUpdateProviderSingleScore.setScore(sumSingleScore);
            saveOrUpdateProviderSingleScore.setRefereeNum(countRefereeNum);
            saveOrUpdateProviderSingleScore.setProviderId(providerId);

            LambdaQueryWrapper<ProviderSingleScore> providerSingleScoreLambdaQueryWrapper = new LambdaQueryWrapper<>();
            providerSingleScoreLambdaQueryWrapper.eq(ProviderSingleScore::getAppraiseId, appraiseId);
            providerSingleScoreLambdaQueryWrapper.eq(ProviderSingleScore::getProviderId, providerId);
            providerSingleScoreLambdaQueryWrapper.eq(ProviderSingleScore::getTemplateId, appraiseTemplate.getId());
            ProviderSingleScore singleScoreServiceOne = getOne(providerSingleScoreLambdaQueryWrapper);
            if (null == singleScoreServiceOne) {
                saveOrUpdateProviderSingleScore.setId(StringUtil.newId());
                save(saveOrUpdateProviderSingleScore);
            } else {
                saveOrUpdateProviderSingleScore.setId(singleScoreServiceOne.getId());
                updateById(saveOrUpdateProviderSingleScore);
            }
        }
    }
}
