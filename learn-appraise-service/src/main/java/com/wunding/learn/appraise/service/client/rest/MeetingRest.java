package com.wunding.learn.appraise.service.client.rest;


import com.github.pagehelper.PageInfo;
import com.wunding.learn.appraise.service.client.dto.AppraiseDTO;
import com.wunding.learn.appraise.service.client.dto.AppraiseQuery;
import com.wunding.learn.appraise.service.client.dto.MaterialsPreviewDTO;
import com.wunding.learn.appraise.service.client.dto.MeetingClientDetailDTO;
import com.wunding.learn.appraise.service.service.IMeetingService;
import com.wunding.learn.common.bean.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;

/**
 * <AUTHOR>
 * @title: AppraiseController
 * @projectName devlop-learn
 * @description: 答辩会议数据接口
 * @date 2021/11/30 11:15
 */
@Tag(description = "答辩会议模块》答辩会议数据接口", name = "clientMeetingRest")
@RestController("clientMeetingRest")
@RequestMapping(value = "${module.appraise.contentPath:/}meeting")
public class MeetingRest {

    @Resource
    private IMeetingService meetingService;

    /**
     * 获取答辩会议列表
     *
     * @return
     */
    @Operation(summary = "获取答辩会议列表", description = "此接口用于答辩会议列表查询，包含进行中和已结束")
    @GetMapping("/getMeetingList")
    public Result<PageInfo<AppraiseDTO>> getMeetingList(AppraiseQuery appraiseAo) {
        return meetingService.getAppraiseList(appraiseAo.getPageNo(), appraiseAo.getPageSize(), appraiseAo);
    }

    /**
     * 获取答辩会议详情
     *
     * @return
     */
    @Operation(summary = "获取答辩会议详情", description = "此接口用于获取答辩会议详情")
    @GetMapping("/getMeetingDetail")
    public Result<MeetingClientDetailDTO> getMeetingDetail(
        @Parameter(description = "答辩会议id", required = true) String appraiseId) {
        return meetingService.getClientAppraiseDetail(appraiseId);
    }


    /**
     * 查询预览详情
     *
     * @return
     */
    @Operation(summary = "查询预览详情", description = "查询预览详情")
    @GetMapping("/getMeetingMaterialsPreview")
    public Result<MaterialsPreviewDTO> getMeetingMaterialsPreview(
            @Parameter(description = "被评人上传的材料id", required = true) String providerFileId) {
        return meetingService.getMaterialsPreviewVO(providerFileId);
    }

}
