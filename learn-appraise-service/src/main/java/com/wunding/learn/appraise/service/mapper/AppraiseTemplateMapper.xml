<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.appraise.service.mapper.AppraiseTemplateMapper">

        <!-- 开启二级缓存 -->
        <!--
    <cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
    -->

        <!-- 使用缓存 -->
        <cache-ref namespace="com.wunding.learn.appraise.service.mapper.AppraiseTemplateMapper"/>

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.wunding.learn.appraise.service.model.AppraiseTemplate">
            <!--@Table appraise_template-->
                    <id column="id" jdbcType="VARCHAR" property="id"/>
                    <result column="appraise_id" jdbcType="VARCHAR"
                            property="appraiseId"/>
                    <result column="num" jdbcType="INTEGER"
                            property="num"/>
                    <result column="category" jdbcType="VARCHAR"
                            property="category"/>
                    <result column="title" jdbcType="VARCHAR"
                            property="title"/>
                    <result column="content" jdbcType="VARCHAR"
                            property="content"/>
                    <result column="type" jdbcType="INTEGER"
                            property="type"/>
                    <result column="description" jdbcType="VARCHAR"
                            property="description"/>
                    <result column="is_del" jdbcType="INTEGER"
                            property="isDel"/>
                    <result column="create_by" jdbcType="VARCHAR"
                            property="createBy"/>
                    <result column="create_time" jdbcType="TIMESTAMP"
                            property="createTime"/>
                    <result column="update_by" jdbcType="VARCHAR"
                            property="updateBy"/>
                    <result column="update_time" jdbcType="TIMESTAMP"
                            property="updateTime"/>
                    <result column="upper_limit" jdbcType="INTEGER"
                            property="upperLimit"/>
                    <result column="lower_limit" jdbcType="INTEGER"
                            property="lowerLimit"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, appraise_id, num, category, title, content, type, description, is_del, create_by, create_time, update_by, update_time, upper_limit, lower_limit
        </sql>

        <select id="selectCategoryList" resultType="java.lang.String">
                select
                        temp.category
                from
                        (
                                select
                                        *
                                from
                                        appraise_template
                                where
                                        is_del = 0
                                  and appraise_id = #{appraiseId}
                                  and `type` = #{type}
                                order by
                                        num
                        ) temp
                group by
                        temp.category
                order by  min(temp.num)
        </select>

</mapper>
