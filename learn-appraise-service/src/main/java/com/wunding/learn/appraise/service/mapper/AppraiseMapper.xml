<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.appraise.service.mapper.AppraiseMapper">
    <!-- 开启二级缓存 -->
    <!--
<cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
-->

    <!-- 使用缓存 -->
    <cache-ref namespace="com.wunding.learn.appraise.service.mapper.AppraiseMapper"/>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wunding.learn.appraise.service.model.Appraise">
        <!--@Table appraise-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="code" jdbcType="VARCHAR"
          property="code"/>
        <result column="type" jdbcType="INTEGER"
          property="type"/>
        <result column="material_begin_time" jdbcType="TIMESTAMP"
          property="materialBeginTime"/>
        <result column="material_end_time" jdbcType="TIMESTAMP"
          property="materialEndTime"/>
        <result column="judge_begin_time" jdbcType="TIMESTAMP"
          property="judgeBeginTime"/>
        <result column="judge_end_time" jdbcType="TIMESTAMP"
          property="judgeEndTime"/>
        <result column="sponsor_org" jdbcType="VARCHAR"
          property="sponsorOrg"/>
        <result column="address" jdbcType="VARCHAR"
          property="address"/>
        <result column="title" jdbcType="VARCHAR"
          property="title"/>
        <result column="go_down_num" jdbcType="INTEGER"
          property="goDownNum"/>
        <result column="view_type" jdbcType="INTEGER"
          property="viewType"/>
        <result column="cover_image" jdbcType="VARCHAR"
          property="coverImage"/>
        <result column="description" jdbcType="VARCHAR"
          property="description"/>
        <result column="activity_description" jdbcType="LONGVARCHAR"
          property="activityDescription"/>
        <result column="org_id" jdbcType="VARCHAR"
          property="orgId"/>
        <result column="is_publish" jdbcType="INTEGER"
          property="isPublish"/>
        <result column="is_del" jdbcType="INTEGER"
          property="isDel"/>
        <result column="is_online" jdbcType="INTEGER"
          property="isOnline"/>
        <result column="score" jdbcType="DECIMAL"
          property="score"/>
        <result column="create_by" jdbcType="VARCHAR"
          property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP"
          property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR"
          property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP"
          property="updateTime"/>
        <result column="publish_by" jdbcType="VARCHAR"
          property="publishBy"/>
        <result column="publish_time" jdbcType="TIMESTAMP"
          property="publishTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        code,
        type,
        material_begin_time,
        material_end_time,
        judge_begin_time,
        judge_end_time,
        sponsor_org,
        address,
        title,
        go_down_num,
        view_type,
        cover_image,
        description,
        activity_description,
        is_publish,
        is_del,
        create_by,
        create_time,
        update_by,
        update_time,
        publish_by,
        publish_time,
        is_online,
        score,
        is_material
    </sql>

    <!--嵌套子查询-待优化-->
    <select id="queryPage" resultType="com.wunding.learn.appraise.service.admin.dto.AppraiseDTO" useCache="false">
        SELECT distinct ta.id,
                        ta.`code`,
                        ta.type,
                        ta.material_begin_time,
                        ta.material_end_time,
                        ta.judge_begin_time,
                        ta.judge_end_time,
                        ta.sponsor_org,
                        ta.address,
                        ta.title,
                        ta.view_type,
                        ta.cover_image,
                        ta.description,
                        ta.activity_description,
                        ta.go_down_num,
                        ta.is_publish,
                        ta.update_time,
                        ta.create_time,
                        (select count(1)
                         from appraise_referee
                         where appraise_id = ta.id
                           and is_del = 0) refereeUserCount,
                        (select count(1)
                         from appraise_provider
                         where appraise_id = ta.id
                           and is_del = 0) providerUserCount,
                        ta.org_id,
                        so.org_name,
                        ta.is_material
        FROM appraise ta
                 left join appraise_provider ap on ap.appraise_id = ta.id
                 left join appraise_referee ar on ar.appraise_id = ta.id
                 left join sys_org so on ta.org_id = so.id
        <where>
            and ta.is_del = 0
                  AND ta.referenced_type = 0
            <if test="params.typeList !=null and params.typeList.size() > 0">
                and ta.type in
                <foreach collection="params.typeList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="params.managerAreaOrgIds != null and params.managerAreaOrgIds.size() > 0">
                and (
                <foreach collection="params.managerAreaOrgIds" item="levelPath" open="(" close=")" separator="or">
                    so.level_path like concat(#{levelPath}, '%')
                </foreach>
                or ta.create_by = #{params.currentUserId}
                    )
            </if>
            <if test="params.title != null and '' != params.title">
                and INSTR(ta.title, #{params.title}) > 0
            </if>
            <if test="params.code != null and '' != params.code">
                and INSTR(ta.code, #{params.code}) > 0
            </if>
            <if test="params.isPublish != null">
                and ta.is_publish = #{params.isPublish}
            </if>
            <if test="params.materialBeginTime != null  and params.materialEndTime != null">
                and ta.material_begin_time >= #{params.materialBeginTime}
                and #{params.materialEndTime} >= ta.material_end_time
            </if>
            <if test="params.appraiser != null and params.appraiser.size > 0">
                and ar.user_id in
                <foreach collection="params.appraiser" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="params.beAppraiser != null and params.beAppraiser.size > 0">
                and ap.user_id in
                <foreach collection="params.beAppraiser" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        order by ta.update_time desc, ta.is_publish desc, ta.create_time desc
    </select>

    <!--嵌套子查询-待优化-->
    <select id="getAppraiseListVo" resultType="com.wunding.learn.appraise.service.admin.dto.AppraiseDTO"
      useCache="false">
        select *
        from (select ta.id,
                     ta.`code`,
                     ta.type,
                     ta.material_begin_time,
                     ta.material_end_time,
                     ta.judge_begin_time,
                     ta.judge_end_time,
                     ta.sponsor_org,
                     ta.address,
                     ta.title,
                     ta.view_type,
                     ta.cover_image,
                     ta.description,
                     ta.activity_description,
                     ta.go_down_num,
                     ta.is_publish,
                     ta.is_material,
                     case
                         when ta.is_material = 0 then 0
                         when (select count(1)
                               from appraise_file_type taft
                                        inner join appraise_provider_file tapf
                                                   on taft.appraise_id = ta.id and tapf.file_type_id = taft.id and
                                                      tapf.appraise_id = ta.id and taft.required = 1)
                             =
                              (select count(1)
                               from appraise_file_type taft
                               where taft.appraise_id = ta.id
                                 and taft.required = 1
                                 and taft.is_del = 0) then 0
                         else 1 end as isLackMaterials,
                     ta.create_time
              from appraise ta
                       inner join appraise_provider tap on tap.appraise_id = ta.id and tap.is_del = 0 and tap.user_id =
                                                                                                          #{params.currentUserId}
              where ta.is_del = 0
                and ta.is_publish = 1
                and ta.referenced_type = 0

              union

              select ta.id,
                     ta.`code`,
                     ta.type,
                     ta.material_begin_time,
                     ta.material_end_time,
                     ta.judge_begin_time,
                     ta.judge_end_time,
                     ta.sponsor_org,
                     ta.address,
                     ta.title,
                     ta.view_type,
                     ta.cover_image,
                     ta.description,
                     ta.activity_description,
                     ta.go_down_num,
                     ta.is_publish,
                     ta.is_material,
                     0 isLackMaterials,
                     ta.create_time
              from appraise ta
                       inner join appraise_referee tar on tar.appraise_id = ta.id and tar.is_del = 0 and tar.user_id =
                                                                                                         #{params.currentUserId}
              where ta.is_del = 0
                and ta.is_publish = 1
                and ta.referenced_type = 0) temp
        <where>
            1 = 1
            <if test="params.title != null and '' != params.title">
                and instr(temp.title, #{params.title}) > 0
            </if>
            <if test="params.queryType == 1">
                and now() >= temp.material_begin_time
                and temp.judge_end_time >= now()
            </if>

            <if test="params.queryType == 2">
                and now() > temp.judge_end_time
            </if>

            <if test="params.startTime != null and params.endTime != null">
                and #{params.startTime} >= temp.material_begin_time
                and temp.judge_end_time >= #{params.endTime}
            </if>
            <if test="params.isLackMaterials != null and params.isLackMaterials == 1">
                and temp.isLackMaterials = #{params.isLackMaterials}
            </if>
        </where>
        order by temp.create_time desc
    </select>

    <select id="getDitch" resultType="com.wunding.learn.common.dto.CerDitchDTO">
        select id,
               title            name,
               (case
                    when now() > material_end_time then - 1
                    when now() &lt; material_begin_time then 0
                    else 1 end) state
        from appraise
        where is_del = 0
          and is_publish = 1
          and id = #{contentId}
    </select>

    <select id="getCertificationContentList" resultType="com.wunding.learn.common.dto.CertificationContentDTO">
        select a.id,
               a.title               as name,
               a.material_begin_time as startTime,
               a.is_del
        from appraise a
        <where>
            a.id in
            <foreach item="id" collection="batchIds" separator="," open="(" close=")" index="">
                #{id}
            </foreach>
        </where>
    </select>

    <select id="getAppraiseIdpTreeByYear" resultType="com.wunding.learn.appraise.service.client.dto.UserIdpAppraiseVO"
      useCache="false">
        select a.id,
               a.title,
               a.material_end_time,
               a.material_begin_time,
               a.judge_begin_time,
               case ap.status when null then 0 else 1 end as isoperation,
               case ap.status when 1 then 1 else 0 end    as isfinish
        from appraise a
                 inner join appraise_provider ap on a.id = ap.appraise_id and ap.is_del = 0
        where a.is_del = 0
          and a.is_publish = 1
          and ap.user_id = #{params.userId}
          and ((date_format(a.material_begin_time, '%Y') = #{params.year}) or
               (a.material_end_time is null and (date_format(a.judge_begin_time, '%Y') = #{params.year})))
        order by a.material_begin_time desc
    </select>

    <select id="getAppraiseIsDelById" parameterType="java.lang.String" resultType="com.wunding.learn.common.dto.ResourceDeleteInfoDTO">
        select id,
             is_del isDel,
             update_time updateTime,
             update_by updateBy
        from appraise
        where id = #{id}
    </select>
</mapper>
