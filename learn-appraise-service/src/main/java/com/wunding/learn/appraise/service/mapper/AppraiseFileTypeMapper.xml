<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.appraise.service.mapper.AppraiseFileTypeMapper">
    <!-- 开启二级缓存 -->
    <!--
<cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
-->

    <!-- 使用缓存 -->
    <cache-ref namespace="com.wunding.learn.appraise.service.mapper.AppraiseFileTypeMapper"/>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wunding.learn.appraise.service.model.AppraiseFileType">
        <!--@Table appraise_file_type-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="appraise_id" jdbcType="VARCHAR"
          property="appraiseId"/>
        <result column="title" jdbcType="VARCHAR"
          property="title"/>
        <result column="example_file" jdbcType="VARCHAR"
          property="exampleFile"/>
        <result column="description" jdbcType="VARCHAR"
          property="description"/>
        <result column="transform_status" jdbcType="INTEGER"
          property="transformStatus"/>
        <result column="example_file_type" jdbcType="VARCHAR"
          property="exampleFileType"/>
        <result column="mime" jdbcType="VARCHAR"
          property="mime"/>
        <result column="file_type" jdbcType="VARCHAR"
          property="fileType"/>
        <result column="required" jdbcType="INTEGER"
          property="required"/>
        <result column="is_save_lib" jdbcType="INTEGER"
          property="isSaveLib"/>
        <result column="file_max_size" jdbcType="INTEGER"
          property="fileMaxSize"/>
        <result column="sort" jdbcType="INTEGER"
          property="sort"/>
        <result column="is_del" jdbcType="INTEGER"
          property="isDel"/>
        <result column="view_type" jdbcType="INTEGER"
          property="viewType"/>
        <result column="create_by" jdbcType="VARCHAR"
          property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP"
          property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR"
          property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP"
          property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, appraise_id, title, example_file, description, transform_status, example_file_type, mime, file_type, required, is_save_lib, file_max_size, sort, is_del, view_type, create_by, create_time, update_by, update_time
    </sql>
    <select id="queryPage" resultType="com.wunding.learn.appraise.service.admin.dto.AppraiseFileTypeListDTO">
        select
        id,
        appraise_id,
        title,
        file_type,
        required,
        is_save_lib,
        file_max_size,
        sort,
        description
        from appraise_file_type
        <where>
            and appraise_id = #{params.appraiseId}
            and is_del = 0
        </where>
    </select>

    <select id="getAppraiseFileTypeById" resultType="com.wunding.learn.appraise.service.admin.dto.AppraiseFileTypeDTO"
      useCache="false">
        select f.currentpath as      exampleFile,
               f.filename            exampleFileName,
               taf.example_file_type exampleFileType,
               taf.mime
        from appraise_file_type taf
                 left join files f on f.categoryid = taf.id
        where taf.id = #{id}
          and f.categorytype = 'APPRAISE_EXAMPLE_FILE'
    </select>
</mapper>
