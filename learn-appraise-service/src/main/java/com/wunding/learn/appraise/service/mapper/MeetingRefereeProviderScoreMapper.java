package com.wunding.learn.appraise.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.appraise.service.model.MeetingRefereeProviderScore;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import com.wunding.learn.appraise.service.model.RefereeProviderScore;
import java.math.BigDecimal;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;

/**
 * <p>  Mapper 接口
 *
 * <AUTHOR> href="mailto:"></a>
 * @since 2024-01-22
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")})
public interface MeetingRefereeProviderScoreMapper extends BaseMapper<MeetingRefereeProviderScore> {

    Integer sumRefereeProviderScore(@Param("providerId") String providerId, @Param("appraiseId") String appraiseId);
}
