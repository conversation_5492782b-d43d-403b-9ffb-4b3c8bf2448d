package com.wunding.learn.appraise.service.service.impl;

import static com.wunding.learn.common.util.math.NumberOperationUtils.ONE_HUNDRED_NUM;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.wunding.learn.appraise.service.admin.dto.AppraiseProviderFileDTO;
import com.wunding.learn.appraise.service.admin.dto.AppraiseProviderFileListQuery;
import com.wunding.learn.appraise.service.constant.AppraiseConstants;
import com.wunding.learn.appraise.service.dao.AppraiseProviderFileDao;
import com.wunding.learn.appraise.service.mapper.AppraiseProviderFileMapper;
import com.wunding.learn.appraise.service.model.AppraiseProviderFile;
import com.wunding.learn.appraise.service.service.IAppraiseProviderFileService;
import com.wunding.learn.common.constant.lecture.LecturerErrorNoEnum;
import com.wunding.learn.common.constant.other.CommonConstants;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.util.bean.SpringUtil;
import com.wunding.learn.common.util.date.DateHelper;
import com.wunding.learn.common.util.date.DateUtil;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.file.api.component.ExportComponent;
import com.wunding.learn.file.api.constant.ExportBizType;
import com.wunding.learn.file.api.constant.ExportFileNameEnum;
import com.wunding.learn.file.api.constant.FileBizType;
import com.wunding.learn.file.api.dto.AbstractExportDataDTO;
import com.wunding.learn.file.api.dto.FileDownloadDTO;
import com.wunding.learn.file.api.dto.IExportDataDTO;
import com.wunding.learn.file.api.dto.NamePath;
import com.wunding.learn.file.api.dto.ZipDownloadDTO;
import com.wunding.learn.file.api.service.FileFeign;
import com.wunding.learn.user.api.dto.OrgShowDTO;
import com.wunding.learn.user.api.service.OrgFeign;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <p> 学员材料表  服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">xiao8</a>
 * @since 2022-08-08
 */
@Slf4j
@Service("appraiseProviderFileService")
public class AppraiseProviderFileServiceImpl extends
    ServiceImpl<AppraiseProviderFileMapper, AppraiseProviderFile> implements IAppraiseProviderFileService {

    private static final String TAG_ID_SEPARATOR = ",";

    private static final String STATUS = "status";

    @Resource
    private FileFeign fileFeign;
    @Resource
    private ExportComponent exportComponent;
    @Resource
    private AppraiseProviderFileMapper appraiseProviderFileMapper;
    @Resource
    private OrgFeign orgFeign;

    @Resource(name = "appraiseProviderFileDao")
    private AppraiseProviderFileDao appraiseProviderFileDao;

    @Override
    public Boolean delete(String ids) {
        if (StringUtils.isEmpty(ids)) {
            return false;
        } else {
            boolean result = true;
            String[] split = ids.split(CommonConstants.A_COMMA_IN_ENGLISH);
            for (String id : split) {
                //判断评价是否过提交时间
                String appraiseId = appraiseProviderFileMapper.getAppraiseId(id);
                log.info("材料:" + appraiseId);
                Date endTime = appraiseProviderFileMapper.getEndTime(appraiseId);
                Date nowDate = new Date();
                if (nowDate.before(endTime)) {
                    return false;
                }
                
                // 查询删除文件的名称
                AppraiseProviderFileDTO fileDto = getAppraiseProviderFileById(id);
                String fileName = Optional.ofNullable(fileDto).isPresent() ? fileDto.getFileName() : "";

                // 执行删除操作，同时记录业务日志
                appraiseProviderFileDao.delAppraiseProviderFile(id, fileName);
            }
            return result;
        }
    }

    @Override
    public PageInfo<AppraiseProviderFileDTO> queryPage(AppraiseProviderFileListQuery query) {
        AppraiseProviderFileDTO appraiseProviderFileVo = new AppraiseProviderFileDTO();
        BeanUtils.copyProperties(query, appraiseProviderFileVo);
        if (StringUtils.isNotEmpty(query.getFullName())) {
            appraiseProviderFileVo.setUserIds(Arrays.asList(query.getFullName().split(TAG_ID_SEPARATOR)));
        }
        PageInfo<AppraiseProviderFileDTO> pageInfo = PageMethod.startPage(query.getPageNo(), query.getPageSize())
            .doSelectPageInfo(() -> baseMapper.queryPage(appraiseProviderFileVo));
        Set<String> orgIdSet = pageInfo.getList().stream().map(AppraiseProviderFileDTO::getOrgId)
            .collect(Collectors.toSet());
        Map<String, OrgShowDTO> orgShowDTOMap = orgFeign.getOrgShowDTO(orgIdSet);
        for (AppraiseProviderFileDTO appraiseProviderFileDTO : pageInfo.getList()) {
            Optional.ofNullable(fileFeign.getSourceFileInfo(appraiseProviderFileDTO.getId(),
                FileBizType.APPRAISE_UPLOAD_FILE.toString())).ifPresent(fileInfo -> {
                appraiseProviderFileDTO.setFileId(fileInfo.getId());
                appraiseProviderFileDTO.setFileName(fileInfo.getName());
                appraiseProviderFileDTO.setFileSize(fileInfo.getFileSize());
                appraiseProviderFileDTO.setPath(fileInfo.getUrl());
            });
            Optional.ofNullable(orgShowDTOMap.get(appraiseProviderFileDTO.getOrgId())).ifPresent(orgShowDTO -> {
                appraiseProviderFileDTO.setOrgName(orgShowDTO.getOrgShortName());
                appraiseProviderFileDTO.setOrgPath(orgShowDTO.getLevelPathName());
            });
        }
        return pageInfo;

    }

    @Override
    public AppraiseProviderFileDTO getAppraiseProviderFileById(String id) {
        AppraiseProviderFileDTO dbAppraise = getBaseMapper().getAppraiseProviderFileById(id);
        NamePath file = fileFeign.getFileNamePath(id, "APPRAISE_UPLOAD_FILE");
        if (file != null) {
            dbAppraise.setId(id);
            dbAppraise.setFileId(file.getId());
            dbAppraise.setFileSize(file.getFileSize());
            dbAppraise.setFileType("APPRAISE_UPLOAD_FILE");
            dbAppraise.setFileName(file.getName());
            dbAppraise.setPath(file.getPath());
        }
        return dbAppraise;
    }

    @Override
    public List<AppraiseProviderFileDTO> getAppraiseProviderFileById(String[] ids) {
        List<AppraiseProviderFileDTO> appraiseProviderFileVos = new ArrayList<>();
        for (String id : ids) {
            AppraiseProviderFileDTO appraiseProviderFileVo = getAppraiseProviderSourceFileById(
                id);
            appraiseProviderFileVos.add(appraiseProviderFileVo);
        }
        return appraiseProviderFileVos;
    }

    @Override
    public AppraiseProviderFileDTO getAppraiseProviderSourceFileById(String id) {
        AppraiseProviderFileDTO dbAppraise = getBaseMapper().getAppraiseProviderFileById(id);
        NamePath file = fileFeign.getSourceFileInfo(id, FileBizType.APPRAISE_UPLOAD_FILE.toString());
        if (file == null) {
            // 兜底操作，兼容处理
            file = fileFeign.getFileNamePath(id, FileBizType.APPRAISE_UPLOAD_FILE.toString());
        }
        if (file != null) {
            dbAppraise.setId(id);
            dbAppraise.setFileId(file.getId());
            dbAppraise.setFileSize(file.getFileSize());
            dbAppraise.setFileType(FileBizType.APPRAISE_UPLOAD_FILE.toString());
            dbAppraise.setFileName(file.getName());
            dbAppraise.setPath(file.getPath());
        }
        return dbAppraise;
    }


    @Override
    public void downloadZipPack(String items) {
        String[] ids = items.split(",");
        List<AppraiseProviderFileDTO> providerFileVos = getAppraiseProviderFileById(ids);
        ZipDownloadDTO zipDownloadDTO = new ZipDownloadDTO();
        zipDownloadDTO.setZipName(DateUtil.formatToStr(new Date(), DateHelper.YYYYMMDD2) + "_评价材料列表");
        zipDownloadDTO.setTempPath(StringUtil.newId());
        List<FileDownloadDTO> fileDownloadDTOList = new ArrayList<>();
        //用户未上传文件时抛出异常
        if (CollectionUtils.isEmpty(providerFileVos)) {
            throw new BusinessException(LecturerErrorNoEnum.ERR_MATERIAL_NOT_EXIST);
        }
        for (AppraiseProviderFileDTO appraiseProviderFileDTO : providerFileVos) {
            if (!StringUtils.isEmpty(appraiseProviderFileDTO.getPath())) {
                FileDownloadDTO fileDownloadDTO = new FileDownloadDTO();
                fileDownloadDTO.setCategoryId(appraiseProviderFileDTO.getId());
                fileDownloadDTO.setCategoryType(FileBizType.APPRAISE_UPLOAD_FILE.name());
                fileDownloadDTO.setLoginName("");
                fileDownloadDTO.setUserName("");
                fileDownloadDTOList.add(fileDownloadDTO);
            }
        }
        zipDownloadDTO.setFileDownloadDTOList(fileDownloadDTOList);
        fileFeign.compressorFileList(StringUtil.newId(), "AppraiseDownloadZip", zipDownloadDTO);
    }

    @Override
    public void exportData(AppraiseProviderFileListQuery query) {

        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<IAppraiseProviderFileService, AppraiseProviderFileDTO>(
            query) {
            @Override
            protected IAppraiseProviderFileService getBean() {
                return SpringUtil.getBean("appraiseProviderFileService", IAppraiseProviderFileService.class);
            }

            @Override
            protected PageInfo<AppraiseProviderFileDTO> getPageInfo() {
                return getBean().queryPage((AppraiseProviderFileListQuery) pageQueryDTO);

            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.AppraiseProviderFile;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.AppraiseProviderFile.getType();
            }

            @Override
            protected void afterCreateBeanMap(Map<String, Object> map) {
                Object status = map.get(STATUS);
                if (Objects.equals(status, 1)) {
                    map.put(STATUS, "已完成");
                } else {
                    map.put(STATUS, "未开始");
                }
                BigDecimal score = (BigDecimal) map.get("score");
                map.put("score", score
                    .divide(new BigDecimal(ONE_HUNDRED_NUM), AppraiseConstants.APPRAISE_SCORE_PRECISION,
                        BigDecimal.ROUND_HALF_DOWN).toString());

                long fileSize = (long) map.get("fileSize");
                BigDecimal bigDecimal = new BigDecimal(fileSize);
                map.put("fileSize", bigDecimal.divide(new BigDecimal("1048546"), 3, BigDecimal.ROUND_HALF_UP) + "MB");
            }
        };

        exportComponent.exportRecord(exportDataDTO);
    }
}
