package com.wunding.learn.appraise.service.client.rest;


import com.github.pagehelper.PageInfo;
import com.wunding.learn.appraise.api.dto.AppraiseRefereeDTO;
import com.wunding.learn.appraise.service.client.dto.AppraiseRefereeDetailDTO;
import com.wunding.learn.appraise.service.client.dto.AppraiseScoreListDTO;
import com.wunding.learn.appraise.service.client.dto.ParticipatorDTO;
import com.wunding.learn.appraise.service.service.IAppraiseService;
import com.wunding.learn.common.bean.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @title: AppraiseRefereeController
 * @projectName devlop-learn
 * @description: 评委相关操作入口
 * @date 2021/12/515:15
 */
@Tag(description = "评价模块》评委相关操作接口", name = "clientAppraiseRefereeRest")
@RequestMapping("${module.appraise.contentPath:/}referee")
@RestController("clientAppraiseRefereeRest")
public class AppraiseRefereeRest {

    @Resource
    private IAppraiseService appraiseService;

    /**
     * 分页查询待评人列表
     *
     * @return
     */
    @Operation(summary = "评委分页查询待评人列表", description = "评委分页查询待评人列表", security = @SecurityRequirement(name = "apiKey"))
    @GetMapping("/getProviders")
    public Result<PageInfo<ParticipatorDTO>> getProviders(
        @Parameter(required = true) int pageNo, @Parameter(required = true) int pageSize,
        @Parameter(description = "评价id", required = true) String appraiseId) {
        return appraiseService.getProviders(pageNo, pageSize, appraiseId);
    }

    /**
     * 评委查看被评人提交材料
     */
    @Operation(summary = "评委查看被评人提交材料", description = "评委查看被评人提交材料", security = @SecurityRequirement(name = "apiKey"))
    @GetMapping("/getAppraiseReferees")
    public Result<AppraiseRefereeDTO> getAppraiseReferees(
        @Parameter(description = "评价id", required = true) String appraiseId,
        @Parameter(description = "被评人id", required = true) String providerId) {
        return appraiseService.getAppraiseReferees(appraiseId, providerId);
    }

    /**
     * 评委查询评价明细
     */
    @Operation(summary = "评委查询评价明细", description = "评委查询评价明细", security = @SecurityRequirement(name = "apiKey"))
    @GetMapping("/getBeAppraiseRefereeDetail")
    public Result<AppraiseRefereeDetailDTO> getBeAppraiseRefereeDetail(
        @Parameter(description = "评价id", required = true) String appraiseId,
        @Parameter(description = "被评人id", required = true) String providerId) {
        return appraiseService.getBeAppraiseRefereeDetail(appraiseId, providerId);
    }

    /**
     * 评委提交评价
     */
    @Operation(summary = "评委提交评价", description = "评委提交评价", security = @SecurityRequirement(name = "apiKey"))
    @PostMapping("/submitAppraiseScore")
    public Result<Void> submitAppraiseScore(
        @Parameter(name = "appraiseScoreListAO", description = "请求参数", required = true) @RequestBody
            AppraiseScoreListDTO appraiseScoreListAO) {
        return appraiseService.submitAppraiseScore(appraiseScoreListAO);
    }
}
