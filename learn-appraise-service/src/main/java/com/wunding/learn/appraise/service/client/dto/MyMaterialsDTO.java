package com.wunding.learn.appraise.service.client.dto;

import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @title: MyMaterialsVO
 * @projectName devlop-learn
 * @description: 我的材料
 * @date 2021/12/119:26
 */
@Data
@Schema
public class MyMaterialsDTO {

    @Schema(description = "是否允许查看明细 1:允许,2:不允许,3 允许且匿名评委)")
    private Integer viewType;

    @Schema(description = "材料类型id")
    private List<MaterialsVO> materials = Lists.newArrayList();

    public MaterialsVO createMaterialsVO() {
        return new MaterialsVO();
    }

    public PermitType createPermitFileType() {
        return new PermitType();
    }

    @Data
    @Schema
    public class MaterialsVO {

        @Schema(description = "材料类型id")
        private String id;

        @Schema(description = "材料标题")
        private String title;

        @Schema(description = "是否必填 0-否 1-是")
        private Integer required;

        @Schema(description = "是否待入库课件 0-否 1-是")
        private Integer isSaveLib;

        @Schema(description = "材料支持类型")
        private List<PermitType> permitFileTypes;

        @Schema(description = "文件大小限制 单位 M")
        private Integer fileMaxSize;

        @Schema(description = "示例文件Url")
        private String exampleFileUrl;

        @Schema(description = "案例文件 转码状态 1-转码中；2-转换成功；3-转换失败")
        private Integer transformStatus;

        @Schema(description = "示例文件名")
        private String exampleFileName;

        @Schema(description = "示例文件类型")
        private String exampleFileType;

        @Schema(description = "被评人上传的材料id")
        private String providerFileId;

        @Schema(description = "被评人上传文件名")
        private String providerFileName;

        @Schema(description = "材料提供说明")
        private String description;
    }

    @Data
    @Schema
    public class PermitType {

        @Schema(description = "video-视频,audio-音频,image-图片,pdf-PDF,word-WORD文档")
        private String type;

        @Schema(description = "材料支持类型")
        private String permitFileType;
    }

}
