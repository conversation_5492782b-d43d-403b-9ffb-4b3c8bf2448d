package com.wunding.learn.appraise.service.enums;

import com.wunding.learn.common.context.user.UserThreadContext;

/**
 * <AUTHOR>
 */
public enum AppraiseRedisKeyEnum {

    APPRAISE_CODE_NUM("AppraiseCodeNum"),

    MEETING_CODE_NUM("MeetingCodeNum");

    private String key;

    AppraiseRedisKeyEnum(String key) {
        this.key = key;
    }

    public String getKey() {
        return key + ":" + UserThreadContext.getTenantId();
    }
}
