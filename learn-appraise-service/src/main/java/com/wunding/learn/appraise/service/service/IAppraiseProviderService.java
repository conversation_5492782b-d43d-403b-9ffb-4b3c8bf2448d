package com.wunding.learn.appraise.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.wunding.learn.appraise.service.admin.dto.AppraiseProviderDTO;
import com.wunding.learn.appraise.service.admin.dto.AppraiseProviderListQuery;
import com.wunding.learn.appraise.service.admin.dto.SaveAppraiseProviderUserDTO;
import com.wunding.learn.appraise.service.model.AppraiseProvider;
import com.wunding.learn.common.dto.ImportResultDTO;
import com.wunding.learn.common.dto.SaveViewLimitDTO;
import com.wunding.learn.common.dto.ViewLimitBeanDTO;
import java.util.List;
import java.util.Map;
import org.springframework.scheduling.annotation.Async;

/**
 * <p> 被评人（学员） 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">xiao8</a>
 * @since 2022-08-08
 */
public interface IAppraiseProviderService extends IService<AppraiseProvider> {

    /**
     * 被评人分页查询
     *
     * @param query 查询条件
     * @return
     */
    PageInfo<AppraiseProviderDTO> queryPage(AppraiseProviderListQuery query);

    /**
     * 根据关键字查询评价人关联的所有评审id
     *
     * @param keyWord 搜索关键字
     * @return
     */
    List<String> selectAppraiseId(String keyWord);

    /**
     * 根据被评人id查询被评人名字
     *
     * @param providerId 被评人id
     * @return
     */
    String getUserNameById(String providerId);

    /**
     * 添加被评人
     *
     * @param saveAppraiseProviderUserDTO 参数对象
     */
    void addUser(SaveAppraiseProviderUserDTO saveAppraiseProviderUserDTO);

    /**
     * 导入被评人
     *
     * @param appraiseId    评价id
     * @param excelFilePath 导入用户模板文件路径
     */
    ImportResultDTO importUser(String appraiseId, String excelFilePath);

    /**
     * 删除被评人
     *
     * @param id 被评人id
     */
    void delete(String id);

    /**
     * 导出被评人列表
     */
    @Async
    void exportData(AppraiseProviderListQuery query);

    /**
     * 获取评委和被评人
     *
     * @param id
     * @return
     */
    List<ViewLimitBeanDTO> getProviderAndRefereeByAppraiseId(String id);

    /**
     * 将认证的应持证人员的信息加到被评人中
     *
     * @param viewLimitChangeMap
     */
    void viewLimitChange(Map<String, List<SaveViewLimitDTO>> viewLimitChangeMap);
}
