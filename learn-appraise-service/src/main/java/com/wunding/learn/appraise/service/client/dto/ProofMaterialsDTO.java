package com.wunding.learn.appraise.service.client.dto;

import com.wunding.learn.file.api.dto.NamePath;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @title: AppraiseRefereeVO
 * @projectName devlop-learn
 * @description: 查询评价
 * @date 2021/12/4 11:25
 */
@Data
@Schema
public class ProofMaterialsDTO {

    @Schema(description = "举证要求ID")
    String proofId;

    @Schema(description = "举证要求内容")
    private String content;

    @Schema(description = "举证文件")
    private NamePath namePaths;

}
