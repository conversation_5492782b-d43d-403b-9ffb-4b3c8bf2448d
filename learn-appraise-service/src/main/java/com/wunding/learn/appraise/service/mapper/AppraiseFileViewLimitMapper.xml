<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.appraise.service.mapper.AppraiseFileViewLimitMapper">

        <!-- 开启二级缓存 -->
        <!--
    <cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
    -->

        <!-- 使用缓存 -->
        <cache-ref namespace="com.wunding.learn.appraise.service.mapper.AppraiseFileViewLimitMapper"/>

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.wunding.learn.appraise.service.model.AppraiseFileViewLimit">
            <!--@Table appraise_file_view_limit-->
                    <id column="id" jdbcType="VARCHAR" property="id"/>
                    <result column="content_id" jdbcType="VARCHAR"
                            property="contentId"/>
                    <result column="category_type" jdbcType="VARCHAR"
                            property="categoryType"/>
                    <result column="category_id" jdbcType="VARCHAR"
                            property="categoryId"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, content_id, category_type, category_id
        </sql>

</mapper>
