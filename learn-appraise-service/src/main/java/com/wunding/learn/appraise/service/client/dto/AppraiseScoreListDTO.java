package com.wunding.learn.appraise.service.client.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @title: AppraiseScoreAO
 * @projectName devlop-learn
 * @description: TODO
 * @date 2021/12/412:46
 */
@Data
public class AppraiseScoreListDTO {

    private static final long serialVersionUID = -6319526235025120020L;

    @Schema(description = "评价id", required = true)
    private String appraiseId;

    @Schema(description = "被评人id", required = true)
    private String providerId;

    @Schema(description = "评委评价内容", required = true)
    private List<AppraiseScoreDTO> appraiseScoreAOS;

}
