package com.wunding.learn.appraise.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import com.wunding.learn.appraise.service.model.MeetingTemplate;
import java.util.List;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;

/**
 * <p>  Mapper 接口
 *
 * <AUTHOR> href="mailto:"></a>
    * @since 2024-01-22
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")})
public interface MeetingTemplateMapper extends BaseMapper<MeetingTemplate> {

    /**
     *
     * @param appraiseId
     * @param
     * @return
     */
    List<String> selectCategoryList(@Param("appraiseId") String appraiseId, @Param("type") Integer type);
}
