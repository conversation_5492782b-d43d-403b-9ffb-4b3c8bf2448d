package com.wunding.learn.appraise.service.client.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;

/**
 * @Author: GYong
 * @Date: 2022/9/29 15:55
 */
@Data
@Schema(name = "IdpLearnMapExecDTO", description = "idp评价对象")
public class IdpAppraiseDTO {

    @Schema(description = "评价")
    private String flag = "appraise";

    @Schema(description = "评价ID")
    private String id;

    @Schema(description = "评价名称")
    private String proName;

    @Schema(description = "评价开始时间")
    private Date startTime;

    @Schema(description = "是否完成")
    private int isFinish;

    @Schema(description = "是否可以直接进入任务: 0不可以, 1可以")
    private int isOperation;

    @Schema(description = "desc")
    private String desc;

    @Schema(description = "status")
    private int status;

    @Schema(description = "用户参与的状态: 尚未进行 0, 正在进行 1, 已经完成 2 （1 这种中间状态，如果某项活动没有这种逻辑，则只存在0 和 2）")
    private int userStatus;
}
