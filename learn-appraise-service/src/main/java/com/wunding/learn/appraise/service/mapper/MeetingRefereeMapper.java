package com.wunding.learn.appraise.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.appraise.service.admin.dto.AppraiseRefereeDTO;
import com.wunding.learn.appraise.service.admin.dto.MeetingRefereeDTO;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import com.wunding.learn.appraise.service.model.MeetingReferee;
import java.util.List;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;

/**
 * <p>  Mapper 接口
 *
 * <AUTHOR> href="mailto:"></a>
    * @since 2024-01-22
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")})
public interface MeetingRefereeMapper extends BaseMapper<MeetingReferee> {


    /**
     * 评委分页查询
     *
     * @param appraiseRefereeVo 查询参数
     * @return
     */
    List<MeetingRefereeDTO> queryPage(@Param("params") MeetingRefereeDTO appraiseRefereeVo);

    /**
     * 根据关键字查询评价人关联的所有评审id
     *
     * @param keyWord 查询关键字
     * @return
     */
    List<String> selectAppraiseId(String keyWord);
}
