package com.wunding.learn.appraise.service.client.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

/**
 * @Author: aixinrong
 * @Date: 2022/10/19 14:58
 */
@Data
@Schema(name = "MonthIdpAppraiseDTO", description = "月份分类idp评价对象")
public class MonthIdpAppraiseDTO {

    @Schema(description = "月份")
    private Integer month;

    @Schema(description = "评价对象")
    private List<IdpAppraiseDTO> idpList;
}
