package com.wunding.learn.appraise.service.service.impl;


import com.alibaba.excel.util.ListUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.wunding.learn.appraise.service.admin.dto.*;
import com.wunding.learn.appraise.service.constant.AppraiseConstants;
import com.wunding.learn.appraise.service.model.AppraiseTemplate;
import com.wunding.learn.appraise.service.model.Meeting;
import com.wunding.learn.appraise.service.model.MeetingTemplate;
import com.wunding.learn.appraise.service.service.*;
import com.wunding.learn.common.constant.other.CommonConstants;
import com.wunding.learn.common.constant.other.DelEnum;
import com.wunding.learn.common.util.bean.SpringUtil;
import com.wunding.learn.common.util.date.DateHelper;
import com.wunding.learn.common.util.string.TranslateUtil;
import com.wunding.learn.file.api.component.ExportComponent;
import com.wunding.learn.file.api.constant.ExportBizType;
import com.wunding.learn.file.api.constant.ExportFileNameEnum;
import com.wunding.learn.file.api.dto.AbstractExportDataDTO;
import com.wunding.learn.file.api.dto.AbstractExportNoEntityDataDTO;
import com.wunding.learn.file.api.dto.IExportDataDTO;
import com.wunding.learn.file.api.dto.IExportNoEntityDataDTO;
import com.wunding.learn.user.api.dto.OrgShowDTO;
import com.wunding.learn.user.api.dto.UserDTO;
import com.wunding.learn.user.api.service.OrgFeign;
import com.wunding.learn.user.api.service.UserFeign;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.wunding.learn.common.util.math.NumberOperationUtils.ONE_HUNDRED_NUM;

/**
 * 评价详情
 *
 * <AUTHOR>
 * @date 2021/12/5 12:00
 */
@Service("meetingDetailServiceI")
public class MeetingDetailServiceImpl implements MeetingDetailService {

    private static final  String IS_PASS = "isPass";

    @Resource
    private IMeetingTemplateService meetingTemplateService;
    @Resource
    private IMeetingProviderSingleScoreService meetingProviderSingleScoreService;
    @Resource
    @Lazy
    private IMeetingProviderService meetingProviderService;
    @Resource
    private IMeetingRefereeProviderScoreService meetingRefereeProviderScoreService;
    @Resource
    private ExportComponent exportComponent;

    @Resource
    @Lazy
    private MeetingDetailService meetingDetailServiceI;

    @Resource
    @Lazy
    private IMeetingService meetingService;

    @Resource
    private UserFeign userFeign;

    @Resource
    private OrgFeign orgFeign;

    @Override
    public List<MeetingTemplate> getTableHead(AppraiseDetailDTO appraiseDetailVo) {
        LambdaQueryWrapper<MeetingTemplate> templateLambdaQueryWrapper = Wrappers.lambdaQuery();
        templateLambdaQueryWrapper.eq(MeetingTemplate::getAppraiseId, appraiseDetailVo.getAppraiseId());
        templateLambdaQueryWrapper.eq(MeetingTemplate::getIsDel, DelEnum.NOT_DELETE.getValue());
        templateLambdaQueryWrapper.orderByAsc(MeetingTemplate::getType);
        if (appraiseDetailVo.getType() != null) {
            templateLambdaQueryWrapper.eq(MeetingTemplate::getType, appraiseDetailVo.getType());
        }
        templateLambdaQueryWrapper.select(MeetingTemplate::getId, MeetingTemplate::getTitle);
        templateLambdaQueryWrapper.orderByAsc(MeetingTemplate::getNum);
        return meetingTemplateService.list(templateLambdaQueryWrapper);
    }

    @Override
    public PageInfo<MeetingDetailDTO> queryPage(AppraiseDetailListQuery query) {
        MeetingDetailDTO appraiseDetailVo = new MeetingDetailDTO();

        BeanUtils.copyProperties(query, appraiseDetailVo);
        String appraiseId = appraiseDetailVo.getAppraiseId();
        Meeting byId = meetingService.getById(appraiseId);

        PageInfo<MeetingDetailDTO> pageInfo = PageMethod.startPage(query.getPageNo(), query.getPageSize())
                .doSelectPageInfo(() -> meetingProviderSingleScoreService.queryPage(appraiseDetailVo));
        List<String> userId = pageInfo.getList().stream().map(MeetingDetailDTO::getUserId).collect(Collectors.toList());
        Map<String, UserDTO> userNameMapByIds = userFeign.getUserNameMapByIds(userId);
        for (MeetingDetailDTO providerVo : pageInfo.getList()) {
            Optional.ofNullable(userNameMapByIds.get(providerVo.getUserId())).ifPresent(userDTO -> {
                providerVo.setPostName(userDTO.getPostName());
                providerVo.setOrgId(userDTO.getOrgId());
                Map<String, OrgShowDTO> orgShowDTO = orgFeign.getOrgShowDTO(Collections.singleton(userDTO.getOrgId()));
                Optional.ofNullable(orgShowDTO.get(providerVo.getOrgId())).ifPresent(showOrg -> {
                    providerVo.setOrgName(showOrg.getOrgShortName());
                    providerVo.setLevelPathName(showOrg.getLevelPathName());
                    providerVo.setCPostName(byId.getPostName());
                });
            });

        }
        return pageInfo;
    }

    @Override
    public String getUserNameById(String providerId) {
        return meetingProviderService.getUserNameById(providerId);
    }

    @Override
    public PageInfo<Map<String, Object>> queryShowPage(AppraiseShowListDataQuery query) {
        MeetingShowDetailDTO appraiseShowDetailVo = new MeetingShowDetailDTO();
        BeanUtils.copyProperties(query, appraiseShowDetailVo);
        PageInfo<MeetingShowDetailDTO> pageInfo = PageMethod.startPage(query.getPageNo(), query.getPageSize())
                .doSelectPageInfo(() -> meetingProviderSingleScoreService.queryShowPage(appraiseShowDetailVo));

        // 处理数据
        List<MeetingShowDetailDTO> appraiseShowDetailDTOList = pageInfo.getList();
        // 封装转换后的结果集
        List<Map<String, Object>> data = new ArrayList<>();
        // 结果为空返回
        if (CollectionUtils.isNotEmpty(appraiseShowDetailDTOList)) {

            for (MeetingShowDetailDTO showDetailVo : appraiseShowDetailDTOList) {
                Map<String, Object> map = new HashMap<>(12);
                map.put("refereeId", showDetailVo.getRefereeId());
                map.put("refereeName", showDetailVo.getRefereeName());

                // 将分数除以100 保留两位小数
                BigDecimal score = showDetailVo.getTotalScore();
                if (score != null) {
                    map.put("totalScore", showDetailVo.getTotalScore()
                            .divide(new BigDecimal(ONE_HUNDRED_NUM), AppraiseConstants.APPRAISE_SCORE_PRECISION,
                                    BigDecimal.ROUND_HALF_DOWN).toString());
                }

                // 添加判断，防止空指针
                Date updateTime = showDetailVo.getUpdateTime();
                if (updateTime != null) {
                    map.put("updateTime", showDetailVo.getUpdateTime());
                }

                data.add(map);
            }
        }

        PageInfo<Map<String, Object>> page = new PageInfo<>(data);
        page.setPageNum(pageInfo.getPageNum());
        page.setPageSize(pageInfo.getPageSize());

        return page;
    }


    /**
     * 获取打分历史
     *
     * @param query 查询参数
     */
    @Override
    public PageInfo<AppraiseHistoryDetailDTO> queryHistoryPage(AppraiseHistoryListQuery query) {

        AppraiseHistoryDetailDTO appraiseHistoryDetailVo = new AppraiseHistoryDetailDTO();
        BeanUtils.copyProperties(query, appraiseHistoryDetailVo);

        if (StringUtils.isNotBlank(query.getRefereeIds())) {
            appraiseHistoryDetailVo.setRefereeIdsVo(
                    TranslateUtil.translateBySplit(query.getRefereeIds(), String.class));
        }

        PageInfo<AppraiseHistoryDetailDTO> page = PageMethod.startPage(query.getPageNo(), query.getPageSize())
                .doSelectPageInfo(() -> meetingProviderSingleScoreService.queryHistoryPage(appraiseHistoryDetailVo));

        // 将分数除以100 保留两位小数，向下取整
        List<AppraiseHistoryDetailDTO> result = page.getList();
        for (AppraiseHistoryDetailDTO historyDetailVo : result) {
            historyDetailVo.setScored(historyDetailVo.getWeightedScore()
                    .divide(new BigDecimal(historyDetailVo.getWeight()), AppraiseConstants.APPRAISE_SCORE_PRECISION,
                            BigDecimal.ROUND_HALF_DOWN).toString());
            historyDetailVo.setWeightedScored(historyDetailVo.getWeightedScore()
                    .divide(new BigDecimal(ONE_HUNDRED_NUM), AppraiseConstants.APPRAISE_SCORE_PRECISION,
                            BigDecimal.ROUND_HALF_DOWN).toString());
        }
        page.setList(result);
        return page;
    }

    /**
     * 评委评分记录删除
     *
     * @param ids
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(String ids) {
        if (StringUtils.isEmpty(ids)) {
            return;
        }
        String[] split = ids.split(CommonConstants.A_COMMA_IN_ENGLISH);
        for (String id : split) {
            meetingRefereeProviderScoreService.deleteRefereeProviderScore(id);
        }
    }

    @Override
    public void exportData(AppraiseDetailListQuery query) {
        AppraiseDetailDTO appraiseDetailVo = new AppraiseDetailDTO();
        appraiseDetailVo.setAppraiseId(query.getAppraiseId());
        //被评单项类型 题型(1:打分,2:问答)
        appraiseDetailVo.setType(1);

        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<MeetingDetailService, MeetingDetailDTO>(
                query) {
            @Override
            protected MeetingDetailService getBean() {
                return SpringUtil.getBean("meetingDetailServiceI", MeetingDetailService.class);
            }

            @Override
            protected PageInfo<MeetingDetailDTO> getPageInfo() {
                return meetingDetailServiceI.queryPage(query);
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.MeetingScoreDetail;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.MeetingScoreDetail.getType();
            }

            @Override
            protected void afterCreateBeanMap(Map<String, Object> map) {
                Object isPass = map.get(IS_PASS);
                if (isPass.equals(0)) {
                    map.put(IS_PASS, "未评");
                }
                if (isPass.equals(1)) {
                    map.put(IS_PASS, "通过");
                }
                if (isPass.equals(2)) {
                    map.put(IS_PASS, "未通过");
                }

            }

        };
        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    public void exportDetailData(AppraiseShowListDataQuery query) {

        IExportNoEntityDataDTO exportDataDTO = new AbstractExportNoEntityDataDTO<AppraiseDetailService, AppraiseShowDetailDTO>(
                query) {
            @Override
            protected AppraiseDetailService getBean() {
                return SpringUtil.getBean("appraiseDetailServiceI", AppraiseDetailService.class);
            }

            @Override
            protected List<List<Object>> getPageInfo() {
                MeetingShowDetailDTO appraiseShowDetailVo = new MeetingShowDetailDTO();
                BeanUtils.copyProperties(query, appraiseShowDetailVo);
                PageInfo<MeetingShowDetailDTO> pageInfo = PageMethod.startPage(query.getPageNo(), query.getPageSize())
                        .doSelectPageInfo(() -> meetingProviderSingleScoreService.queryShowPage(appraiseShowDetailVo));
                AppraiseDetailDTO appraiseDetailVo = new AppraiseDetailDTO();
                appraiseDetailVo.setAppraiseId(appraiseShowDetailVo.getAppraiseId());
                List<List<Object>> data = ListUtils.newArrayList();
                if (Objects.nonNull(pageInfo.getList())) {
                    for (MeetingShowDetailDTO appraiseShowDetailDTO : pageInfo.getList()) {
                        List<Object> detailDTOS = ListUtils.newArrayList();
                        detailDTOS.add(appraiseShowDetailDTO.getRefereeName());
                        detailDTOS.add(
                                DateHelper.formatDate(appraiseShowDetailDTO.getUpdateTime(), DateHelper.YYYYMMDD_HHMMSS));
                        detailDTOS.add(appraiseShowDetailDTO.getTotalScore()
                                .divide(new BigDecimal(ONE_HUNDRED_NUM), AppraiseConstants.APPRAISE_SCORE_PRECISION,
                                        BigDecimal.ROUND_HALF_DOWN).toString());
                        data.add(detailDTOS);
                    }
                }
                return data;
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.AppraiseShowDetail;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.AppraiseShowDetail.getType();
            }

        };

        List<List<String>> head = new ArrayList<>(1);
        head.add(Arrays.asList("评价人"));
        head.add(Arrays.asList("完成时间"));
        head.add(Arrays.asList("总分"));
        AppraiseDetailDTO appraiseDetailVo = new AppraiseDetailDTO();
        appraiseDetailVo.setAppraiseId(query.getAppraiseId());
        //根据评价表id获取评分项id与标题
        List<MeetingTemplate> tableHead = getTableHead(appraiseDetailVo);
        if (Objects.nonNull(tableHead)) {
            tableHead.forEach(appraiseTemplate -> head.add(Arrays.asList(appraiseTemplate.getTitle())));
        }

        exportComponent.exportNoEntityRecord(exportDataDTO, head);
    }

    @Override
    public void exportHistoryData(AppraiseHistoryListQuery query) {

        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<AppraiseDetailService, AppraiseHistoryDetailDTO>(
                query) {

            @Override
            protected AppraiseDetailService getBean() {
                return SpringUtil.getBean("appraiseDetailServiceI", AppraiseDetailService.class);
            }

            @Override
            protected PageInfo<AppraiseHistoryDetailDTO> getPageInfo() {
                return getBean().queryHistoryPage((AppraiseHistoryListQuery) pageQueryDTO);

            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.AppraiseHistory;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.AppraiseHistory.getType();
            }

        };

        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    public PageInfo<MeetingProviderScoreDetailDTO> queryPageListData(AppraiseDetailListQuery query) {
        PageInfo<MeetingProviderScoreDetailDTO> pageInfo = PageMethod.startPage(query.getPageNo(), query.getPageSize())
                .doSelectPageInfo(() -> meetingProviderSingleScoreService.queryPageListData(query.getAppraiseId()));

        return pageInfo;
    }
}
