package com.wunding.learn.appraise.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.wunding.learn.appraise.service.admin.dto.AppraiseRefereeDTO;
import com.wunding.learn.appraise.service.admin.dto.AppraiseRefereeListQuery;
import com.wunding.learn.appraise.service.admin.dto.SaveAppraiseRefereeUserDTO;
import com.wunding.learn.appraise.service.model.AppraiseReferee;
import com.wunding.learn.common.dto.ImportResultDTO;
import org.springframework.scheduling.annotation.Async;

/**
 * <p> 评价人（评委） 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">xiao8</a>
 * @since 2022-08-08
 */
public interface IAppraiseRefereeService extends IService<AppraiseReferee> {

    /**
     * 评委分页查询
     *
     * @param query 查询参数
     * @return
     */
    PageInfo<AppraiseRefereeDTO> queryPage(AppraiseRefereeListQuery query);

    /**
     * 添加评委
     *
     * @param saveAppraiseRefereeUserDTO 评价id + 用户id 逗号分割
     */
    void addUser(SaveAppraiseRefereeUserDTO saveAppraiseRefereeUserDTO);

    /**
     * 导入评委
     *
     * @param appraiseId    评价id
     * @param excelFilePath 导入的excel 文件路径
     */
    ImportResultDTO importUser(String appraiseId, String excelFilePath);

    /**
     * @param refereeId   评委id
     * @param isAnonymous 是否匿名 0-否 1-是
     */
    void changeIsAnonymous(String refereeId, Integer isAnonymous);

    /**
     * 修改权重
     *
     * @param refereeId 评委id
     * @param weight    权重
     */
    void changeWeight(String refereeId, Integer weight);

    /**
     * 删除评委
     *
     * @param ids 评委id 逗号分割
     */
    void delete(String ids);

    /**
     * 获取评价表中的评委总人数
     *
     * @param appraiseId 评价id
     * @return
     */
    int countByAppraiseId(String appraiseId);


    /**
     * 导出评委列表
     */
    @Async
    void exportData(AppraiseRefereeListQuery query);
}
