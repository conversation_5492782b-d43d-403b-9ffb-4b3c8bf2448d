package com.wunding.learn.appraise.service.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.wunding.learn.appraise.service.admin.dto.AppraiseFileTypeDTO;
import com.wunding.learn.appraise.service.admin.dto.AppraiseFileTypeListDTO;
import com.wunding.learn.appraise.service.admin.dto.AppraiseFileTypeListQuery;
import com.wunding.learn.appraise.service.admin.dto.AppraiseProviderFileDTO;
import com.wunding.learn.appraise.service.admin.dto.SaveAppraiseFileTypeDTO;
import com.wunding.learn.appraise.service.component.AppraiseFileViewLimitComponent;
import com.wunding.learn.appraise.service.dao.AppraiseFileTypeDao;
import com.wunding.learn.appraise.service.enums.AppraiseFileTypeEnum;
import com.wunding.learn.appraise.service.mapper.AppraiseFileTypeMapper;
import com.wunding.learn.appraise.service.mapper.AppraiseProviderFileMapper;
import com.wunding.learn.appraise.service.model.Appraise;
import com.wunding.learn.appraise.service.model.AppraiseFileType;
import com.wunding.learn.appraise.service.service.IAppraiseFileTypeService;
import com.wunding.learn.common.constant.appraise.AppraiseErrorNoEnum;
import com.wunding.learn.common.constant.http.WebConstantUtil;
import com.wunding.learn.common.constant.other.CommonConstants;
import com.wunding.learn.common.dto.IdName;
import com.wunding.learn.common.enums.file.TranscodeStatusEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.mq.event.TransCodeEvent;
import com.wunding.learn.common.mq.service.MqProducer;
import com.wunding.learn.common.mybatis.service.impl.BaseServiceImpl;
import com.wunding.learn.common.util.bean.SpringUtil;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.file.api.component.ExportComponent;
import com.wunding.learn.file.api.constant.ExportBizType;
import com.wunding.learn.file.api.constant.ExportFileNameEnum;
import com.wunding.learn.file.api.constant.FileBizType;
import com.wunding.learn.file.api.constant.FileResourceEnum;
import com.wunding.learn.file.api.dto.AbstractExportDataDTO;
import com.wunding.learn.file.api.dto.IExportDataDTO;
import com.wunding.learn.file.api.dto.NamePath;
import com.wunding.learn.file.api.dto.SaveFileDTO;
import com.wunding.learn.file.api.service.FileFeign;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p> 评价材料类型 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">xiao8</a>
 * @since 2022-08-08
 */
@Slf4j
@Service("appraiseFileTypeService")
public class AppraiseFileTypeServiceImpl extends
    BaseServiceImpl<AppraiseFileTypeMapper, AppraiseFileType> implements IAppraiseFileTypeService {

    @Resource
    private FileFeign fileFeign;

    @Resource
    private MqProducer mqProducer;

    @Resource
    private AppraiseFileViewLimitComponent appraiseFileViewLimitComponent;

    @Resource
    private ExportComponent exportComponent;

    @Resource
    private AppraiseProviderFileMapper appraiseProviderFileMapper;

    @Resource(name = "appraiseFileTypeDao")
    private AppraiseFileTypeDao typeDao;

    @Resource
    @Lazy
    private IAppraiseFileTypeService appraiseFileTypeServiceI;

    @Override
    public PageInfo<AppraiseFileTypeListDTO> queryPage(AppraiseFileTypeListQuery query) {
        AppraiseFileTypeDTO appraiseFileTypeVo = new AppraiseFileTypeDTO();
        appraiseFileTypeVo.setAppraiseId(query.getAppraiseId());
        PageInfo<AppraiseFileTypeListDTO> pageInfo = PageMethod.startPage(query.getPageNo(),
            query.getPageSize()).doSelectPageInfo(() -> baseMapper.queryPage(appraiseFileTypeVo));

        for (AppraiseFileTypeListDTO appraiseFileTypeListDTO : pageInfo.getList()) {
            if (StringUtils.isNotBlank(appraiseFileTypeListDTO.getFileType())) {
                String[] split = appraiseFileTypeListDTO.getFileType().split(",");
                List<IdName> list = new ArrayList<>();
                for (String s : split) {
                    list.add(new IdName(s, AppraiseFileTypeEnum.getSupportTypeByFileType(s)));
                }
                appraiseFileTypeListDTO.setFileTypeInfo(list);
            }
        }
        return pageInfo;
    }

    @Override
    public AppraiseFileTypeDTO getAppraiseFileTypeById(String id) {
        return baseMapper.getAppraiseFileTypeById(id);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void insertAppraiseFileTypeVo(SaveAppraiseFileTypeDTO saveAppraiseFileTypeDTO) {

        Boolean isInsert = false;
        //基础校验
        this.commonCheck(saveAppraiseFileTypeDTO);

        AppraiseFileType appraiseFileType = new AppraiseFileType();
        BeanUtils.copyProperties(saveAppraiseFileTypeDTO, appraiseFileType);
        String newId = StringUtil.newId();
        if (StringUtils.isBlank(saveAppraiseFileTypeDTO.getId())) {
            appraiseFileType.setId(newId);
            isInsert = true;
        }
        String exampleFileTempPath = null;

        String filesId = null;
        if (StringUtils.isNotEmpty(saveAppraiseFileTypeDTO.getExampleFile().getPath())) {
            exampleFileTempPath = saveAppraiseFileTypeDTO.getExampleFile().getPath();

            if (StringUtils.isEmpty(exampleFileTempPath)) {
                throw new BusinessException(AppraiseErrorNoEnum.ERR_EXAMPLE_FILE_NULL);
            }

            String mime = saveAppraiseFileTypeDTO.getMime();
            if (StringUtils.isEmpty(mime)) {
                throw new BusinessException(AppraiseErrorNoEnum.ERR_EXAMPLE_FILE_EXTEND_MIME_NOT_EXIST);
            }

            String exampleFileTempPathType = saveAppraiseFileTypeDTO.getExampleFileType();
            if (StringUtils.isEmpty(exampleFileTempPathType)) {
                throw new BusinessException(AppraiseErrorNoEnum.ERR_EXAMPLE_FILE_MIME_NOT_EXIST);
            }

            // 这里跟课件一样的处理方式
            String exampleFileName = saveAppraiseFileTypeDTO.getExampleFile().getName();
            SaveFileDTO fileInfo = fileFeign.saveFile(appraiseFileType.getId(),
                FileBizType.APPRAISE_EXAMPLE_FILE.toString(), exampleFileName, exampleFileTempPath);

            appraiseFileType.setExampleFile(fileInfo.getPath());
            appraiseFileType.setExampleFileType(exampleFileTempPathType);
            appraiseFileType.setMime(mime);

            if (FileResourceEnum.isNeedTransform(exampleFileTempPathType)) {
                // 视频和pdf需要转码
                appraiseFileType.setTransformStatus(TranscodeStatusEnum.TRANSFORMING.value);
                mqProducer.sendMsg(new TransCodeEvent(fileInfo.getId(), appraiseFileType.getId(),
                    FileBizType.APPRAISE_EXAMPLE_FILE.name(), exampleFileTempPath,
                    WebConstantUtil.CW_CONTENT_TYPE_TEXT_HTML, exampleFileTempPathType, null));
            } else {
                appraiseFileType.setTransformStatus(TranscodeStatusEnum.TRANSFORMED.value);
            }

        }

        if (isInsert) {
            typeDao.saveAppraiseFileType(appraiseFileType);
        } else {
            typeDao.updateAppraiseFileType(appraiseFileType);
        }

        // 保存评价课件入库范围
        if (Optional.ofNullable(saveAppraiseFileTypeDTO.getProgrammeId()).isPresent()) {
            appraiseFileViewLimitComponent.handleNewViewLimit(
                saveAppraiseFileTypeDTO.getProgrammeId(), appraiseFileType.getId());
        }
    }


    @Override
    public void updateAppraiseFileTypeVo(SaveAppraiseFileTypeDTO saveAppraiseFileTypeDTO) {
        checkAppriseFileTypeById(saveAppraiseFileTypeDTO.getId());
        appraiseFileTypeServiceI.insertAppraiseFileTypeVo(saveAppraiseFileTypeDTO);
    }

    @Override
    public SaveAppraiseFileTypeDTO getDtoById(String appraiseFileTypeId) {
        AppraiseFileType byId = getById(appraiseFileTypeId);
        SaveAppraiseFileTypeDTO saveAppraiseFileTypeDTO = new SaveAppraiseFileTypeDTO();
        if (byId == null) {
            return saveAppraiseFileTypeDTO;
        }
        BeanUtils.copyProperties(byId, saveAppraiseFileTypeDTO);
        NamePath fileNamePathInfo = fileFeign.getFileNamePathInfo(appraiseFileTypeId,
            FileBizType.APPRAISE_EXAMPLE_FILE.toString(), false);
        Optional.ofNullable(fileNamePathInfo).ifPresent(saveAppraiseFileTypeDTO::setExampleFile);
        return saveAppraiseFileTypeDTO;
    }

    private void commonCheck(SaveAppraiseFileTypeDTO saveAppraiseFileTypeDTO) {
        //校验材料支持类型
        if (StringUtils.isEmpty(saveAppraiseFileTypeDTO.getFileType())) {
            throw new BusinessException(AppraiseErrorNoEnum.PLEASE_CHOOSE_MATERIALS_SUPPORT_TYPE);
        }
        //校验限制条件：当已有参与记录时，不可新增材料规则，否则吐司提示“已有参与记录，不可新增”
        AppraiseProviderFileDTO dto = new AppraiseProviderFileDTO();
        dto.setAppraiseId(saveAppraiseFileTypeDTO.getAppraiseId());
        if (appraiseProviderFileMapper.countAppraiseProviderFile(dto) > 0) {
            throw new BusinessException(AppraiseErrorNoEnum.ERR_RECORD_EXIST);
        }
    }

    @Override
    public void delete(String ids, boolean flag) {
        if (StringUtils.isEmpty(ids)) {
            return;
        }

        String[] split = ids.split(CommonConstants.A_COMMA_IN_ENGLISH);
        List<String> list = Arrays.asList(split);
        for (String id : list) {
            checkAppriseFileTypeById(id);
        }
        fileFeign.deleteFileByBizIdListAndBizType(ids, FileBizType.APPRAISE_EXAMPLE_FILE.toString(),
            0);

        List<AppraiseFileType> appraiseFileTypes = listByIds(list);
        if (flag) {
            appraiseFileTypes.forEach(e -> typeDao.delAppraiseFileType(e));
        } else {
            appraiseFileTypes.forEach(e -> typeDao.delAppraiseFileTypeByMapper(e));
        }

    }

    private void checkAppriseFileTypeById(String id) {
        //限制条件：当选择的材料规则中已有被评审人上传材料时不可删除，否则吐司提示“选择的材料规则中已有被评审人上传材料，不可删除”
        AppraiseProviderFileDTO dto = new AppraiseProviderFileDTO();
        dto.setFileTypeId(id);
        if (appraiseProviderFileMapper.countAppraiseProviderFile(dto) > 0) {
            throw new BusinessException(AppraiseErrorNoEnum.ERR_APPRAISE_PROVIDER_FILE_EXIST);
        }
    }

    @Override
    public void exportData(AppraiseFileTypeListQuery query, Appraise appraise) {

        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<IAppraiseFileTypeService, AppraiseFileTypeListDTO>(
            query) {
            @Override
            protected IAppraiseFileTypeService getBean() {
                return SpringUtil.getBean("appraiseFileTypeService",
                    IAppraiseFileTypeService.class);
            }

            @Override
            protected PageInfo<AppraiseFileTypeListDTO> getPageInfo() {
                return getBean().queryPage((AppraiseFileTypeListQuery) pageQueryDTO);

            }

            @Override
            public ExportBizType getType() {
                if (2 == appraise.getType()) {
                    return ExportBizType.AppraiseFileType2;
                }
                return ExportBizType.AppraiseFileType;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.AppraiseFileType.getType();
            }

            @Override
            protected void afterCreateBeanMap(Map<String, Object> map) {
                List<Map<String, String>> fileTypeInfo = (List<Map<String, String>>) map.get(
                    "fileTypeInfo");
                StringBuilder stringBuilder = new StringBuilder();
                fileTypeInfo.forEach(
                    fileType -> stringBuilder.append(fileType.get("name")).append(","));
                map.put("fileType",
                    stringBuilder.deleteCharAt(stringBuilder.length() - 1).toString());
            }
        };

        exportComponent.exportRecord(exportDataDTO);
    }

}
