<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.appraise.service.mapper.RefereeProviderScoreMapper">

        <!-- 开启二级缓存 -->
        <!--
    <cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
    -->

        <!-- 使用缓存 -->
        <cache-ref namespace="com.wunding.learn.appraise.service.mapper.RefereeProviderScoreMapper"/>

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.wunding.learn.appraise.service.model.RefereeProviderScore">
            <!--@Table referee_provider_score-->
                    <id column="id" jdbcType="VARCHAR" property="id"/>
                    <result column="provider_id" jdbcType="VARCHAR"
                            property="providerId"/>
                    <result column="referee_id" jdbcType="VARCHAR"
                            property="refereeId"/>
                    <result column="appraise_id" jdbcType="VARCHAR"
                            property="appraiseId"/>
                    <result column="score" jdbcType="INTEGER"
                            property="score"/>
                    <result column="create_by" jdbcType="VARCHAR"
                            property="createBy"/>
                    <result column="create_time" jdbcType="TIMESTAMP"
                            property="createTime"/>
                    <result column="update_by" jdbcType="VARCHAR"
                            property="updateBy"/>
                    <result column="update_time" jdbcType="TIMESTAMP"
                            property="updateTime"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, provider_id, referee_id, appraise_id, score, create_by, create_time, update_by, update_time
        </sql>

    <select id="sumRefereeProviderScore" resultType="java.lang.Integer">
        select
            ifnull((
                       select
                           round( avg( score ), 0 )
                       from
                           referee_provider_score
                       where
                           appraise_id = #{appraiseId}
                         and provider_id = #{providerId}
                       group by
                           appraise_id,
                           provider_id
                   ),
                   0
                )
        from
            dual
    </select>

</mapper>
