<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.appraise.service.mapper.AppraiseScoreMapper">

        <!-- 开启二级缓存 -->
        <!--
    <cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
    -->

        <!-- 使用缓存 -->
        <cache-ref namespace="com.wunding.learn.appraise.service.mapper.AppraiseScoreMapper"/>

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.wunding.learn.appraise.service.model.AppraiseScore">
            <!--@Table appraise_score-->
                    <id column="id" jdbcType="VARCHAR" property="id"/>
                    <result column="provider_id" jdbcType="VARCHAR"
                            property="providerId"/>
                    <result column="referee_id" jdbcType="VARCHAR"
                            property="refereeId"/>
                    <result column="template_id" jdbcType="VARCHAR"
                            property="templateId"/>
                    <result column="weight" jdbcType="INTEGER"
                            property="weight"/>
                    <result column="appraise_id" jdbcType="VARCHAR"
                            property="appraiseId"/>
                    <result column="weight_score" jdbcType="INTEGER"
                            property="weightScore"/>
                    <result column="score" jdbcType="INTEGER"
                            property="score"/>
                    <result column="remark" jdbcType="VARCHAR"
                            property="remark"/>
                    <result column="is_del" jdbcType="INTEGER"
                            property="isDel"/>
                    <result column="create_by" jdbcType="VARCHAR"
                            property="createBy"/>
                    <result column="create_time" jdbcType="TIMESTAMP"
                            property="createTime"/>
                    <result column="update_by" jdbcType="VARCHAR"
                            property="updateBy"/>
                    <result column="update_time" jdbcType="TIMESTAMP"
                            property="updateTime"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, provider_id, referee_id, template_id, weight, appraise_id, weight_score, score, remark, is_del, create_by, create_time, update_by, update_time
        </sql>

        <select id="countRefereeNum" resultType="java.lang.Integer">
                select count(1) from
                        (select
                                 distinct referee_id
                         from appraise_score
                         where is_del = 0
                           and appraise_id = #{appraiseId}
                           and template_id = #{templateId}
                           and provider_id = #{providerId}
                        ) temp
        </select>

        <select id="sumSingleScore" resultType="java.lang.Integer">
                select
                        ifnull((
                                       select
                                               round( avg( weight_score ), 0 )
                                       from
                                               appraise_score
                                       where
                                               is_del = 0
                                         and appraise_id = #{appraiseId}
                                         and template_id = #{templateId}
                                         and provider_id = #{providerId}
                                       group by
                                               appraise_id,
                                               provider_id,
                                               template_id
                               ),
                               0
                                )
                from
                        dual
        </select>

        <select id="sumRefereeProviderScore" resultType="java.lang.Integer">
                select
                        ifnull((
                                       select
                                               round( avg( weight_score ), 0 )
                                       from
                                               appraise_score
                                       where
                                               is_del = 0
                                         and appraise_id = #{appraiseId}
                                         and referee_id = #{refereeId}
                                         and provider_id = #{providerId}
                                         and weight_score is not null
                                       group by
                                               appraise_id,
                                               provider_id,
                                               referee_id
                               ),
                               0
                                )
                from
                        dual
        </select>

        <select id="sumProviderScore" resultType="java.math.BigDecimal"  useCache="false">
                select
                        ifnull((
                                       select
                                               avg( trps.score / tar.weight )
                                       from
                                               referee_provider_score trps
                                                       inner join appraise_referee tar on tar.appraise_id = #{appraiseId}
                                                       and trps.appraise_id = tar.appraise_id
                                                       and trps.referee_id = tar.id
                                                       and trps.provider_id = #{providerId}
                                                       and tar.is_del = 0
                                       group by
                                               trps.provider_id
                               ),
                               0
                                )
        </select>

        <select id="sumProviderWeightScore" resultType="java.math.BigDecimal" useCache="false">
                select
                        ifnull((
                                       select
                                               avg( trps.score )
                                       from
                                               referee_provider_score trps
                                                       inner join appraise_referee tar on tar.appraise_id =#{appraiseId}
                                                       and trps.appraise_id = tar.appraise_id
                                                       and trps.referee_id = tar.id
                                                       and trps.provider_id = #{providerId}
                                                       and tar.is_del = 0
                                       group by
                                               trps.provider_id
                               ),
                               0
                                )
        </select>

        <select id="countAppraiseReferee" resultType="java.lang.Integer">
                select count(1) from (
                                             select
                                                     distinct referee_id
                                             from appraise_score
                                             where
                                                     provider_id = #{providerId}
                                               and appraise_id = #{appraiseId}
                                               and is_del = 0
                                     ) temp
        </select>

</mapper>
