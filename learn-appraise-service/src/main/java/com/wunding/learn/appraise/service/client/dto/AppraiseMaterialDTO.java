package com.wunding.learn.appraise.service.client.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @title: AppraiseMaterial
 * @projectName devlop-learn
 * @description: 被评人上传材料
 * @date 2021/12/316:31
 */
@Data
@Schema(name = "AppraiseMaterialDTO", description = "被评人上传材料")
public class AppraiseMaterialDTO implements Serializable {

    private static final long serialVersionUID = 3286842360394607547L;

    @Schema(description = "被评人上传的材料id, 添加时为null，编辑时修改需要置null,编辑时没修改需要带入值")
    private String providerFileId;

    @Schema(description = "材料类型id", required = true)
    private String fileTypeId;

    @Schema(description = "文件路径，新上传文件不为空", required = true)
    private String path;

    @Schema(description = "文件名", required = true)
    private String fileName;

    @Schema(description = "文件类型", required = true)
    private String mime;

    @Schema(description = "文件类型", required = true)
    private String fileType;

}
