package com.wunding.learn.appraise.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.appraise.service.admin.dto.AppraiseDetailDTO;
import com.wunding.learn.appraise.service.admin.dto.AppraiseHistoryDetailDTO;
import com.wunding.learn.appraise.service.admin.dto.AppraiseShowDetailDTO;
import com.wunding.learn.appraise.service.admin.dto.MeetingDetailDTO;
import com.wunding.learn.appraise.service.admin.dto.MeetingShowDetailDTO;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import com.wunding.learn.appraise.service.model.MeetingProviderSingleScore;
import java.util.List;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;

/**
 * <p>  Mapper 接口
 *
 * <AUTHOR> href="mailto:"></a>
    * @since 2024-01-22
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")})
public interface MeetingProviderSingleScoreMapper extends BaseMapper<MeetingProviderSingleScore> {


    /**
     * 查看打分详情
     *
     * @param appraiseDetailVo
     * @return
     */
    List<MeetingDetailDTO> queryPage(@Param("params") MeetingDetailDTO appraiseDetailVo);

    /**
     * 查看明细
     *
     * @param appraiseShowDetailVo
     * @return
     */
    List<MeetingShowDetailDTO> queryShowPage(@Param("params") MeetingShowDetailDTO appraiseShowDetailVo);

    /**
     * 历史打分明细查询记录
     *
     * @param appraiseHistoryDetailVo
     * @return
     */
    List<AppraiseHistoryDetailDTO> queryHistoryPage(@Param("params") AppraiseHistoryDetailDTO appraiseHistoryDetailVo);
}
