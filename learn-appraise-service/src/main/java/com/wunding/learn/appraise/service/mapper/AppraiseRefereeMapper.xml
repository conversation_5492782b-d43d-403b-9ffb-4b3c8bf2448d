<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.appraise.service.mapper.AppraiseRefereeMapper">
    <!-- 开启二级缓存 -->
    <!--
<cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
-->

    <!-- 使用缓存 -->
    <cache-ref namespace="com.wunding.learn.appraise.service.mapper.AppraiseRefereeMapper"/>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wunding.learn.appraise.service.model.AppraiseReferee">
        <!--@Table appraise_referee-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="appraise_id" jdbcType="VARCHAR"
          property="appraiseId"/>
        <result column="user_id" jdbcType="VARCHAR"
          property="userId"/>
        <result column="user_name" jdbcType="VARCHAR"
          property="userName"/>
        <result column="login_name" jdbcType="VARCHAR"
          property="loginName"/>
        <result column="org_id" jdbcType="VARCHAR"
          property="orgId"/>
        <result column="level_path" jdbcType="VARCHAR"
          property="levelPath"/>
        <result column="level_path_name" jdbcType="VARCHAR"
          property="levelPathName"/>
        <result column="weight" jdbcType="INTEGER"
          property="weight"/>
        <result column="is_anonymous" jdbcType="INTEGER"
          property="isAnonymous"/>
        <result column="is_del" jdbcType="INTEGER"
          property="isDel"/>
        <result column="create_by" jdbcType="VARCHAR"
          property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP"
          property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR"
          property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP"
          property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , appraise_id, user_id, weight, is_anonymous, is_del, create_by, create_time, update_by, update_time
    </sql>

    <!--嵌套子查询-待优化-->
    <select id="queryPage" resultType="com.wunding.learn.appraise.service.admin.dto.AppraiseRefereeDTO" useCache="false">
        select * from (
        select
        tar.id,
        tar.appraise_id,
        tar.user_id,
        g.id as org_id,
        tar.weight,
        tar.is_anonymous
        ,tar.login_name loginName
        ,tar.user_name fullName
        ,g.level_path_name orgName
        ,count(trps.id) appraisedUserCount
        ,((select count(id) from appraise_provider tap where tap.appraise_id =#{params.appraiseId} and tap.is_del=0) - count(trps.id))
        notAppraisedUserCount
        from
        appraise_referee tar
        left join referee_provider_score trps on trps.referee_id = tar.id and trps.appraise_id = #{params.appraiseId}
        left join sys_org g on g.id =tar.org_id
        <where>
            and tar.appraise_id = #{params.appraiseId}
            and tar.is_del = 0
            <if test="params.userIdList != null and params.userIdList.size() > 0">
                and tar.user_id in
                <foreach item="item" collection="params.userIdList" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="params.orgId != null and '' != params.orgId">
                and g.id = #{params.orgId}
            </if>
        </where>
        group by tar.id
        ) a
        <where>
            <if test="params.isFinish != null ">
                <if test="params.isFinish == 1">
                    and a.notAppraisedUserCount = 0
                </if>
                <if test="params.isFinish == 0">
                    and a.notAppraisedUserCount > 0
                </if>
            </if>
        </where>
    </select>

    <select id="selectAppraiseId" resultType="java.lang.String">
        select
        distinct appraise_id
        from
        appraise_referee tar
        <where>
            and (
            instr(tar.user_name,#{keyWord}) > 0 or instr(tar.login_name,#{keyWord}) > 0
            )


        </where>
    </select>
</mapper>
