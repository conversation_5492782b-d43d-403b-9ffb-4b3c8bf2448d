package com.wunding.learn.appraise.service.client.rest;


import com.github.pagehelper.PageInfo;
import com.wunding.learn.appraise.service.client.dto.AppraiseDTO;
import com.wunding.learn.appraise.service.client.dto.AppraiseDetailDTO;
import com.wunding.learn.appraise.service.client.dto.AppraiseQuery;
import com.wunding.learn.appraise.service.client.dto.MaterialsPreviewDTO;
import com.wunding.learn.appraise.service.service.IAppraiseService;
import com.wunding.learn.common.bean.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @title: AppraiseController
 * @projectName devlop-learn
 * @description: 评价数据接口
 * @date 2021/11/30 11:15
 */
@Tag(description = "评价模块》评价数据接口", name = "clientAppraiseRest")
@RestController("clientAppraiseRest")
@RequestMapping(value = "${module.appraise.contentPath:/}")
public class AppraiseRest {

    @Resource
    private IAppraiseService appraiseService;

    /**
     * 获取评价列表
     *
     * @return
     */
    @Operation(summary = "获取评价列表", description = "此接口用于评价列表查询，包含进行中和已结束")
    @GetMapping("/getAppraiseList")
    public Result<PageInfo<AppraiseDTO>> getAppraiseList(AppraiseQuery appraiseAo) {
        return appraiseService.getAppraiseList(appraiseAo.getPageNo(), appraiseAo.getPageSize(), appraiseAo);
    }

    /**
     * 获取评价详情
     *
     * @return
     */
    @Operation(summary = "获取评价详情", description = "此接口用于获取评价详情")
    @GetMapping("/getAppraiseDetail")
    public Result<AppraiseDetailDTO> getAppraiseDetail(
        @Parameter(description = "评价id", required = true) String appraiseId) {
        return appraiseService.getAppraiseDetail(appraiseId);
    }

    /**
     * 查询预览详情
     *
     * @return
     */
    @Operation(summary = "查询预览详情", description = "查询预览详情")
    @GetMapping("/getMaterialsPreview")
    public Result<MaterialsPreviewDTO> getMaterialsPreview(
        @Parameter(description = "被评人上传的材料id", required = true) String providerFileId) {
        return appraiseService.getMaterialsPreviewVO(providerFileId);
    }

}
