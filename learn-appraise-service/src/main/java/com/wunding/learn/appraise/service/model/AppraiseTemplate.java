package com.wunding.learn.appraise.service.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p> 评价内容模板
 *
 * <AUTHOR> href="mailto:<EMAIL>">xiao8</a>
 * @since 2022-08-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("appraise_template")
@Schema(name = "AppraiseTemplate", description = "评价内容模板")
public class AppraiseTemplate implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    @Schema(description = "主键")
    @TableId(value = "id", type = IdType.INPUT)
    private String id;


    /**
     * 评价id
     */
    @Schema(description = "评价id")
    @TableField("appraise_id")
    private String appraiseId;


    /**
     * 序号
     */
    @Schema(description = "序号")
    @TableField("num")
    private Integer num;


    /**
     * 分类
     */
    @Schema(description = "分类")
    @TableField("category")
    private String category;


    /**
     * 标题
     */
    @Schema(description = "标题")
    @TableField("title")
    private String title;


    /**
     * 内容描述
     */
    @Schema(description = "内容描述")
    @TableField("content")
    private String content;


    /**
     * 题型(1:打分,2:问答)
     */
    @Schema(description = "题型(1:打分,2:问答)")
    @TableField("type")
    private Integer type;


    /**
     * 打分说明
     */
    @Schema(description = "打分说明")
    @TableField("description")
    private String description;


    /**
     * 是否删除(1:已删除,0未删除)
     */
    @Schema(description = "是否删除(1:已删除,0未删除)")
    @TableField("is_del")
    @TableLogic(value = "0", delval = "1")
    private Integer isDel;


    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;


    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 修改人
     */
    @Schema(description = "修改人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;


    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    /**
     * 打分上限
     */
    @Schema(description = "打分上限")
    @TableField("upper_limit")
    private Integer upperLimit;


    /**
     * 打分下限
     */
    @Schema(description = "打分下限")
    @TableField("lower_limit")
    private Integer lowerLimit;


}
