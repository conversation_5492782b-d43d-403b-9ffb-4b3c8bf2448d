package com.wunding.learn.appraise.service.client.dto;

import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @title: AppraiseDetailVO
 * @projectName devlop-learn
 * @description: 评估详情
 * @date 2021/11/30 11:27
 */
@Data
@Schema(name = "MeetingClientDetailDTO", description = "评价详细")
public class MeetingClientDetailDTO {

    @Schema(description = "评价id")
    private String id;

    @Schema(description = "评价标题")
    private String title;

    @Schema(description = "评价地点")
    private String address;

    @Schema(description = "举办组织")
    private String sponsorOrg;

    @Schema(description = "评价封面")
    private String coverImage;

    @Schema(description = "评委人员数")
    private int refereeUserCount;

    @Schema(description = "被评人总数")
    private int providerUserCount;

    @Schema(description = "评价活动说明")
    private String activityDescription;

    @Schema(description = "当前人的角色 1-被评人，2-评委")
    private Integer currentRole;

    @Schema(description = "评价类型(1:讲师认证,2:课程认证,3:其它,4:任职资格)")
    private Integer type;

    @Schema(description = "是否缺少必填材料 0-否 1-是")
    private Integer isDeficiencyMaterials;

    @Schema(description = "评委评价开始时间")
    private Date judgeBeginTime;

    @Schema(description = "评委评价结束时间")
    private Date judgeEndTime;

    @Schema(description = "学员提交材料开始时间")
    private Date materialBeginTime;

    @Schema(description = "学员提交材料结束时间")
    private Date materialEndTime;

    @Schema(description = "是否线上会议")
    private Integer isOnline;

    @Schema(description = "评价通过名单")
    private List<MeetingPass> meetingPassList = Lists.newArrayList();

    public MeetingPass createAppraisePass() {
        return new MeetingPass();
    }

    /**
     * 评价通过名单
     */
    @Schema
    @Data
    public class MeetingPass {

        @Schema(description = "id")
        private String id;

        @Schema(description = "姓名")
        private String loginName;

        @Schema(description = "得分")
        private String score;

        @Schema(description = "排名")
        private Integer rank;
    }
}
