package com.wunding.learn.appraise.service.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p> 材料表
 *
 * <AUTHOR> href="mailto:<EMAIL>">xiao8</a>
 * @since 2022-08-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("appraise_file_source")
@Schema(name = "AppraiseFileSource", description = "材料表")
public class AppraiseFileSource implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    @Schema(description = "主键")
    @TableId(value = "id", type = IdType.INPUT)
    private String id;


    /**
     * 评价材料类型id
     */
    @Schema(description = "评价材料类型id")
    @TableField("file_type_id")
    private String fileTypeId;


    /**
     * 材料文件mime
     */
    @Schema(description = "材料文件mime")
    @TableField("mime")
    private String mime;


    /**
     * 材料文件类型
     */
    @Schema(description = "材料文件类型")
    @TableField("file_type")
    private String fileType;


    /**
     * 材料标题
     */
    @Schema(description = "材料标题")
    @TableField("title")
    private String title;


    /**
     * 文件名
     */
    @Schema(description = "文件名")
    @TableField("file_name")
    private String fileName;


    /**
     * 上传时间
     */
    @Schema(description = "上传时间")
    @TableField("upload_time")
    private Date uploadTime;


    /**
     * 是否删除(1:已删除,0未删除)
     */
    @Schema(description = "是否删除(1:已删除,0未删除)")
    @TableField("is_del")
    @TableLogic(value = "0", delval = "1")
    private Integer isDel;


    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;


    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 修改人
     */
    @Schema(description = "修改人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;


    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


}
