package com.wunding.learn.appraise.service.service.impl;

import com.alibaba.excel.util.ListUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Lists;
import com.wunding.learn.appraise.api.dto.AppraiseRefereeDTO;
import com.wunding.learn.appraise.api.dto.AppraiseTemplateDTO;
import com.wunding.learn.appraise.api.dto.CommentCategory;
import com.wunding.learn.appraise.api.dto.MeetingInfoDTO;
import com.wunding.learn.appraise.service.admin.dto.MeetingListDTO;
import com.wunding.learn.appraise.service.admin.dto.MeetingListQuery;
import com.wunding.learn.appraise.service.admin.dto.PublishAppraiseDTO;
import com.wunding.learn.appraise.service.admin.dto.SaveMeetingDTO;
import com.wunding.learn.appraise.service.client.dto.AppraiseDTO;
import com.wunding.learn.appraise.service.client.dto.AppraiseQuery;
import com.wunding.learn.appraise.service.client.dto.AppraiseRefereeDetailDTO;
import com.wunding.learn.appraise.service.client.dto.AppraiseRefereeDetailDTO.ScoreDetail;
import com.wunding.learn.appraise.service.client.dto.AppraiseScoreDTO;
import com.wunding.learn.appraise.service.client.dto.MaterialsPreviewDTO;
import com.wunding.learn.appraise.service.client.dto.MeetingClientDetailDTO;
import com.wunding.learn.appraise.service.client.dto.MeetingClientDetailDTO.MeetingPass;
import com.wunding.learn.appraise.service.client.dto.MeetingScoreListDTO;
import com.wunding.learn.appraise.service.client.dto.ParticipatorDTO;
import com.wunding.learn.appraise.service.component.MeetingViewLimitComponent;
import com.wunding.learn.appraise.service.constant.AppraiseConstants;
import com.wunding.learn.appraise.service.dto.AppraiseVirtualPo;
import com.wunding.learn.appraise.service.enums.AppraiseRedisKeyEnum;
import com.wunding.learn.appraise.service.mapper.MeetingMapper;
import com.wunding.learn.appraise.service.mapper.MeetingRefereeProviderScoreMapper;
import com.wunding.learn.appraise.service.mapper.MeetingScoreMapper;
import com.wunding.learn.appraise.service.mapper.MeetingTemplateMapper;
import com.wunding.learn.appraise.service.model.Meeting;
import com.wunding.learn.appraise.service.model.MeetingProvider;
import com.wunding.learn.appraise.service.model.MeetingReferee;
import com.wunding.learn.appraise.service.model.MeetingRefereeProviderScore;
import com.wunding.learn.appraise.service.model.MeetingScore;
import com.wunding.learn.appraise.service.model.MeetingTemplate;
import com.wunding.learn.appraise.service.service.IMeetingProviderService;
import com.wunding.learn.appraise.service.service.IMeetingProviderSingleScoreService;
import com.wunding.learn.appraise.service.service.IMeetingRefereeProviderScoreService;
import com.wunding.learn.appraise.service.service.IMeetingRefereeService;
import com.wunding.learn.appraise.service.service.IMeetingScoreService;
import com.wunding.learn.appraise.service.service.IMeetingService;
import com.wunding.learn.appraise.service.service.IMeetingTemplateService;
import com.wunding.learn.common.bean.Result;
import com.wunding.learn.common.constant.appraise.AppraiseErrorNoEnum;
import com.wunding.learn.common.constant.dynamicDataSource.DynamicDataSourceConstant;
import com.wunding.learn.common.constant.other.BaseErrorNoEnum;
import com.wunding.learn.common.constant.other.CommonConstants;
import com.wunding.learn.common.constant.other.ErrorNoEnum;
import com.wunding.learn.common.constant.other.PublishEnum;
import com.wunding.learn.common.constant.tenant.TenantRedisKeyConstant;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.dto.ResourceRecordSyncDTO;
import com.wunding.learn.common.dto.ResourceSyncDTO;
import com.wunding.learn.common.dto.SaveViewLimitDTO;
import com.wunding.learn.common.dto.ViewLimitBeanDTO;
import com.wunding.learn.common.enums.ResourceTypeEnum;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.enums.other.OperationEnum;
import com.wunding.learn.common.enums.other.PublishStatusEnum;
import com.wunding.learn.common.enums.other.RouterVisitEnum;
import com.wunding.learn.common.enums.push.PushNoticeEventEnum;
import com.wunding.learn.common.enums.push.PushType;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.mq.event.HomeRouterVisitEvent;
import com.wunding.learn.common.mq.event.ResourceOperateEvent;
import com.wunding.learn.common.mq.event.ResourceRecordSyncEvent;
import com.wunding.learn.common.mq.event.ResourceSyncEvent;
import com.wunding.learn.common.mq.service.MqProducer;
import com.wunding.learn.common.mybatis.service.impl.BaseServiceImpl;
import com.wunding.learn.common.util.bean.BeanListUtils;
import com.wunding.learn.common.util.bean.SpringUtil;
import com.wunding.learn.common.util.date.DateUtil;
import com.wunding.learn.common.util.excel.ExcelCheckMessage;
import com.wunding.learn.common.util.http.WebUtil;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.common.util.math.NumberOperationUtils;
import com.wunding.learn.common.util.string.StringPool;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.file.api.component.ExportComponent;
import com.wunding.learn.file.api.constant.ExportBizType;
import com.wunding.learn.file.api.constant.ExportFileNameEnum;
import com.wunding.learn.file.api.constant.ImageBizType;
import com.wunding.learn.file.api.dto.IExportNoEntityDataDTO;
import com.wunding.learn.file.api.dto.ImportDataDTO;
import com.wunding.learn.file.api.dto.SaveFileDTO;
import com.wunding.learn.file.api.service.FileFeign;
import com.wunding.learn.file.api.service.ImportDataFeign;
import com.wunding.learn.push.api.dto.PushAttributeDTO;
import com.wunding.learn.push.api.dto.PushNoticeSetDTO;
import com.wunding.learn.push.api.dto.PushNoticeSetDTO.EventPushSet;
import com.wunding.learn.push.api.dto.PushResourceDTO;
import com.wunding.learn.push.api.dto.SendPushDTO;
import com.wunding.learn.push.api.service.PushFeign;
import com.wunding.learn.user.api.dto.OrgShowDTO;
import com.wunding.learn.user.api.dto.UserDTO;
import com.wunding.learn.user.api.dto.viewlimit.ViewLimitMainSaveDTO;
import com.wunding.learn.user.api.dto.viewlimit.ViewLimitProgrammeInfoDTO;
import com.wunding.learn.user.api.service.OrgFeign;
import com.wunding.learn.user.api.service.UserFeign;
import com.wunding.learn.user.api.service.ViewLimitFeign;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeSet;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * <p> 答辩会议表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">xiao8</a>
 * @since 2022-08-08
 */
@Slf4j
@Service("meetingService")
public class MeetingServiceImpl extends BaseServiceImpl<MeetingMapper, Meeting> implements IMeetingService,
    CommandLineRunner {

    @Resource
    private MeetingScoreMapper meetingScoreMapper;
    @Resource
    @Lazy
    private IMeetingRefereeService meetingRefereeService;
    @Resource
    @Lazy
    private IMeetingProviderService meetingProviderService;
    @Resource
    @Lazy
    private IMeetingTemplateService meetingTemplateService;
    @Resource
    @Lazy
    private IMeetingProviderSingleScoreService meetingProviderSingleScoreService;
    @Resource
    private MeetingRefereeProviderScoreMapper meetingRefereeProviderScoreMapper;
    @Resource
    private IMeetingRefereeProviderScoreService meetingRefereeProviderScoreService;
    @Resource
    private IMeetingScoreService meetingScoreService;
    @Resource
    private MeetingTemplateMapper meetingTemplateMapper;
    @Resource
    private UserFeign userFeign;
    @Resource
    private FileFeign fileFeign;
    @Resource
    private ImportDataFeign importDataFeign;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private ExportComponent exportComponent;
    @Resource
    private OrgFeign orgFeign;
    @Resource
    private PushFeign pushFeign;
    @Resource
    private MqProducer mqProducer;
    @Resource
    private ViewLimitFeign viewLimitFeign;
    @Resource
    private MeetingViewLimitComponent meetingViewLimitComponent;

    @Override
    public void run(String... args) throws Exception {
        for (Object tenantId : redisTemplate.opsForHash().keys(TenantRedisKeyConstant.DB_KEY)) {
            String tid = ((String) tenantId).replace(DynamicDataSourceConstant.DB_LOOK_UP_KEY_PREFIX, "");
            UserThreadContext.setTenantId(tid);
            initRedisMeetingCodeNo();
        }
        UserThreadContext.remove();
    }

    //@PostConstruct
    public void initRedisMeetingCodeNo() {
        QueryWrapper<Meeting> query = new QueryWrapper<>();
        query.select("max(code) code");
        Meeting appraise = baseMapper.selectOne(query);
        long currNum = 100000000L;
        if (appraise == null || StringUtils.isBlank(appraise.getCode())) {
            redisTemplate.opsForValue().set(AppraiseRedisKeyEnum.MEETING_CODE_NUM.getKey(), currNum);
        } else {
            String oldNo = appraise.getCode();
            Long redisNum = null;
            try {
                currNum = Long.parseLong(oldNo.substring(2));
                Object v = redisTemplate.opsForValue().get(AppraiseRedisKeyEnum.MEETING_CODE_NUM.getKey());
                if (v instanceof Integer) {
                    redisNum = ((Integer) v).longValue();
                } else if (v instanceof Long) {
                    redisNum = (Long) v;
                }
            } catch (NumberFormatException e) {
                log.error("initRedisMeetingCodeNo error", e);
                currNum = 100000000L;
            }
            if (redisNum == null || redisNum < currNum) {
                redisTemplate.opsForValue().set(AppraiseRedisKeyEnum.MEETING_CODE_NUM.getKey(), currNum);
            }
        }
    }

    public String generateCode() {
        Object v = redisTemplate.opsForValue().get(AppraiseRedisKeyEnum.MEETING_CODE_NUM.getKey());
        if (v == null) {
            initRedisMeetingCodeNo();
        }
        return "DB" + redisTemplate.opsForValue().increment(AppraiseRedisKeyEnum.MEETING_CODE_NUM.getKey(), 1);
    }

    @Override
    public PageInfo<MeetingListDTO> queryPage(MeetingListQuery query) {
        Set<String> managerAreaOrgIds = orgFeign.findUserManageAreaLevelPath(UserThreadContext.getUserId());
        query.setManagerAreaOrgIds(managerAreaOrgIds);
        query.setCurrentOrgId(UserThreadContext.getOrgId());
        query.setCurrentUserId(UserThreadContext.getUserId());
        PageInfo<MeetingListDTO> pageInfo = PageMethod.startPage(query.getPageNo(), query.getPageSize())
            .doSelectPageInfo(() -> baseMapper.queryPage(query));
        if (!CollectionUtils.isEmpty(pageInfo.getList())) {
            Set<String> orgIdSet = pageInfo.getList().stream().map(MeetingListDTO::getOrgId)
                .collect(Collectors.toSet());
            Set<String> userIds = pageInfo.getList().stream().map(MeetingListDTO::getCreateBy)
                .collect(Collectors.toSet());

            Map<String, OrgShowDTO> orgShowDTOMap = orgFeign.getOrgShowDTO(orgIdSet);
            Map<String, UserDTO> fullNameAndOrgNameByUserId = userFeign.getFullNameAndOrgNameByUserId(userIds);
            Map<String, UserDTO> publishMap = userFeign.getFullNameAndOrgNameByUserId(userIds);
            pageInfo.getList().stream().forEach(dto -> {
                Optional.ofNullable(orgShowDTOMap.get(dto.getOrgId())).ifPresent(orgShowDTO -> {
                    dto.setOrgName(orgShowDTO.getOrgShortName());
                    dto.setOrgPath(orgShowDTO.getLevelPathName());
                });
                Optional.ofNullable(fullNameAndOrgNameByUserId.get(dto.getCreateBy())).ifPresent(fullName -> {
                    dto.setCreateBy(fullName.getFullName());
                });
                if (StringUtils.isNotBlank(dto.getPublishBy())) {
                    Optional.ofNullable(publishMap.get(dto.getPublishBy())).ifPresent(fullName -> {
                        dto.setPublishBy(fullName.getFullName());
                    });
                }
            });

        }

        //路由访问埋点
        mqProducer.sendMsg(
            new HomeRouterVisitEvent(RouterVisitEnum.AssessManagement.getRouterId(), UserThreadContext.getUserId(),
                RouterVisitEnum.AssessManagement.getName()));

        return pageInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveMeeting(SaveMeetingDTO saveMeetingDTO) {
        String jobQualificationId = saveMeetingDTO.getJobQualificationId();
        LambdaQueryWrapper<Meeting> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Meeting::getJobQualificationId, jobQualificationId);
        Meeting one = getOne(queryWrapper);
        if (one != null) {
            throw new BusinessException(AppraiseErrorNoEnum.JOB_QUALIFICATION_EXIT);
        }
        checkBreakTime(saveMeetingDTO);
        Meeting meeting = new Meeting();
        BeanUtils.copyProperties(saveMeetingDTO, meeting);
        meeting.setCode(generateCode());
        meeting.setType(AppraiseConstants.APPRAISE_TYPE_MEETING);

        ImportDataDTO importData = importDataFeign.getImportData(saveMeetingDTO.getExcelFilePath());

        ExcelCheckMessage check = new MeetingExcelTemplate().check(importData.getExcel());
        List<String> errorMessage = check.getMessage();
        if (!CollectionUtils.isEmpty(errorMessage)) {
            throw new BusinessException(BaseErrorNoEnum.ERR_IMPORT_DATA_CHECK, null, JsonUtil.objToJson(errorMessage));
        }

        // 保存评估模板
        List<?> objects = check.getObjects();
        String appraiseId = StringUtil.newId();
        if (!CollectionUtils.isEmpty(objects)) {
            List<MeetingTemplate> templateList = (List<MeetingTemplate>) objects;
            // 给集合中的添加答辩会议id
            List<MeetingTemplate> collect = templateList.stream().map(p -> p.setAppraiseId(appraiseId))
                .collect(Collectors.toList());
            meetingTemplateService.saveBatch(collect, CommonConstants.MAX_LIMIT);
        }

        // 图片迁移至正式目录
        if (StringUtils.isNotEmpty(saveMeetingDTO.getCoverImage().getPath())) {
            SaveFileDTO saveFileDTO = fileFeign.saveImage(appraiseId, ImageBizType.MEETING_COVER_IMG.toString(),
                saveMeetingDTO.getCoverImage().getName(), saveMeetingDTO.getCoverImage().getPath());
            meeting.setCoverImage(saveFileDTO.getPath());
        }

        meeting.setId(appraiseId);
        meeting.setActivityDescription(saveMeetingDTO.getActivityDescription());
        meeting.setOrgId(UserThreadContext.getOrgId());
        save(meeting);

        PublishAppraiseDTO publishAppraiseDTO = new PublishAppraiseDTO();
        publishAppraiseDTO.setIds(Collections.singletonList(meeting.getId()));
        publishAppraiseDTO.setIsPublish(saveMeetingDTO.getIsPublish());
        this.publishOrUnPublish(publishAppraiseDTO);
        // 启用才给资源配置推送
        if (GeneralJudgeEnum.CONFIRM.getValue().equals(saveMeetingDTO.getIsPublish()) && Optional.ofNullable(
            saveMeetingDTO.getPushNoticeSetDTO()).isPresent()) {
            sendPushFeign(saveMeetingDTO, 0);
        }

        // 发送资源同步事件消息
        if (Objects.equals(0, meeting.getReferencedType())) {
            mqProducer.sendMsg(new ResourceSyncEvent(
                new ResourceSyncDTO(OperationEnum.CREATE, ResourceTypeEnum.MEETING.name(), meeting.getId(),
                    meeting.getTitle(), meeting.getStartTime(), meeting.getEndTime(), 1, meeting.getIsPublish(),
                    meeting.getIsDel(), meeting.getCreateBy(), meeting.getCreateTime(), meeting.getUpdateBy(),
                    meeting.getUpdateTime())));
        }

        return appraiseId;
    }

    private void checkBreakTime(SaveMeetingDTO saveMeetingDTO) {
        if (ObjectUtils.allNotNull(saveMeetingDTO.getBreakStartTime(), saveMeetingDTO.getBreakEndTime())
            && (saveMeetingDTO.getStartTime().before(saveMeetingDTO.getStartTime()) || saveMeetingDTO.getBreakEndTime()
            .after(saveMeetingDTO.getEndTime()))) {
            throw new BusinessException(AppraiseErrorNoEnum.ERR_BREAK_TIME_IS_BEFORE_MEETING_TIME);

        }
    }

    private void sendPushFeign(SaveMeetingDTO saveMeetingDTO, Integer operateState) {
        SendPushDTO sendPushDTO = new SendPushDTO();
        sendPushDTO.setLang(LocaleContextHolder.getLocale().getLanguage());
        sendPushDTO.setServerName(Objects.requireNonNull(WebUtil.getRequest()).getServerName());
        PushNoticeSetDTO pushNoticeSetDTO = saveMeetingDTO.getPushNoticeSetDTO();
        sendPushDTO.setPushNoticeSetDTO(pushNoticeSetDTO);

        // 手动推送时采用内容封面图片处理
        if (Objects.equals(pushNoticeSetDTO.getPushMethod(), 1)) {
            pushNoticeSetDTO.getManualPushSet().getCustomPushContents().forEach(customPushContent -> {
                if (Objects.equals(customPushContent.getPushImage(), 1)) {
                    customPushContent.setImagePath(
                        fileFeign.getImageFileNamePath(saveMeetingDTO.getId(), ImageBizType.MEETING_COVER_IMG.name()));
                }
            });
        }

        // 为空默认是0
        Integer isTrain = Optional.ofNullable(saveMeetingDTO.getIsTrain()).orElse(0);

        if (Optional.ofNullable(saveMeetingDTO.getProgrammeId()).isEmpty()) {
            // 推送的时候获取被评人和评委
            List<ViewLimitBeanDTO> viewLimitDTOS = meetingProviderService.getProviderAndRefereeByAppraiseId(
                saveMeetingDTO.getId());
            TreeSet<ViewLimitMainSaveDTO> viewLimitMainSaveDTOList = new TreeSet<>();
            for (ViewLimitBeanDTO dto : viewLimitDTOS) {
                ViewLimitMainSaveDTO viewLimitMainSaveDTO = new ViewLimitMainSaveDTO();
                viewLimitMainSaveDTO.setViewId(dto.getCategoryId()).setViewType(2).setLimitType(0);
                viewLimitMainSaveDTOList.add(viewLimitMainSaveDTO);
            }
            ViewLimitProgrammeInfoDTO viewLimitProgrammeInfoDTO = viewLimitFeign.saveViewLimit(
                viewLimitMainSaveDTOList);
            saveMeetingDTO.setProgrammeId(viewLimitProgrammeInfoDTO.getId());
        }

        PushResourceDTO pushResourceDTO = new PushResourceDTO().setIsTrain(isTrain).setOperateState(operateState)
            .setProgrammeId(saveMeetingDTO.getProgrammeId());

        sendPushDTO.setPushResourceDTO(pushResourceDTO);

        PushAttributeDTO pushAttributeDTO = new PushAttributeDTO();

        boolean condition = isTrain != 0;
        if (condition) {
            // 非 0 证明是属于二级内容过来
            String[] name = pushNoticeSetDTO.getResourceName().split(StringPool.UNDERSCORE);
            String[] resourceId = pushNoticeSetDTO.getResourceId().split(StringPool.UNDERSCORE);
            pushAttributeDTO.setName(name[0]);
            pushAttributeDTO.setSecondaryName(name[1]);

            // 此时中 pushNoticeSetDTO 前端只会传 projectId_ 所以拼接好id
            pushResourceDTO.setResourceId(resourceId[0] + StringPool.UNDERSCORE + saveMeetingDTO.getId());
        } else {
            pushAttributeDTO.setName(pushNoticeSetDTO.getResourceName());
            pushResourceDTO.setResourceId(saveMeetingDTO.getId());
        }

        pushResourceDTO.setResourceName(pushNoticeSetDTO.getResourceName());
        pushResourceDTO.setResourceType(pushNoticeSetDTO.getResourceType());
        pushAttributeDTO.setExistSecondary(condition);
        pushAttributeDTO.setSubmitStartTime(saveMeetingDTO.getStartTime());
        pushAttributeDTO.setSubmitEndTime(saveMeetingDTO.getEndTime());
        pushAttributeDTO.setIntro(saveMeetingDTO.getActivityDescription());

        @SuppressWarnings("unchecked") String systemName = (String) redisTemplate.opsForHash()
            .get(TenantRedisKeyConstant.SYSTEM_NAME_KEY, UserThreadContext.getTenantId());
        pushAttributeDTO.setSystemName(systemName);

        sendPushDTO.setPushAttributeDTO(pushAttributeDTO);

        pushFeign.sendPush(sendPushDTO);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMeeting(SaveMeetingDTO saveMeetingDTO) {

        Meeting meeting = new Meeting();
        BeanUtils.copyProperties(saveMeetingDTO, meeting);
        meeting.setActivityDescription(saveMeetingDTO.getActivityDescription());

        Meeting oldMeeting = getById(saveMeetingDTO.getId());
        if (null == oldMeeting) {
            throw new BusinessException(AppraiseErrorNoEnum.RESOURCE_NOT_EXIST);
        }

        String jobQualificationId = saveMeetingDTO.getJobQualificationId();
        LambdaQueryWrapper<Meeting> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Meeting::getJobQualificationId, jobQualificationId);
        queryWrapper.ne(Meeting::getId, saveMeetingDTO.getId());
        Meeting one = getOne(queryWrapper);
        if (one != null) {
            throw new BusinessException(AppraiseErrorNoEnum.JOB_QUALIFICATION_EXIT);
        }

        // 编辑之后的图片地址不同了，说明是重新上传过
        if (!oldMeeting.getCoverImage().equals(saveMeetingDTO.getCoverImage().getPath())) {
            fileFeign.deleteImageByBizIdAndBizType(saveMeetingDTO.getId(), ImageBizType.MEETING_COVER_IMG.toString());
            SaveFileDTO saveFileDTO = fileFeign.saveImage(saveMeetingDTO.getId(),
                ImageBizType.MEETING_COVER_IMG.toString(), saveMeetingDTO.getCoverImage().getName(),
                saveMeetingDTO.getCoverImage().getPath());
            meeting.setCoverImage(saveFileDTO.getPath());
        }
        updateById(meeting);

        PublishAppraiseDTO publishAppraiseDTO = new PublishAppraiseDTO();
        publishAppraiseDTO.setIds(Collections.singletonList(meeting.getId()));
        publishAppraiseDTO.setIsPublish(saveMeetingDTO.getIsPublish());
        this.publishOrUnPublish(publishAppraiseDTO);

        // 与添加逻辑类似
        if (GeneralJudgeEnum.CONFIRM.getValue().equals(saveMeetingDTO.getIsPublish()) && Optional.ofNullable(
            saveMeetingDTO.getPushNoticeSetDTO()).isPresent()) {
            sendPushFeign(saveMeetingDTO, 1);
        }

        // 发送资源同步事件消息
        if (Objects.equals(0, oldMeeting.getReferencedType())) {
            mqProducer.sendMsg(new ResourceSyncEvent(
                new ResourceSyncDTO(OperationEnum.UPDATE, ResourceTypeEnum.MEETING.name(), meeting.getId(),
                    meeting.getTitle(), meeting.getStartTime(), meeting.getEndTime(), 1, meeting.getIsPublish(),
                    meeting.getIsDel(), meeting.getCreateBy(), meeting.getCreateTime(), meeting.getUpdateBy(),
                    meeting.getUpdateTime())));
        }
    }

    @Override
    public void publishOrUnPublish(PublishAppraiseDTO publishAppraiseDTO) {
        if (CollectionUtils.isEmpty(publishAppraiseDTO.getIds())) {
            return;
        }

        Meeting meeting;
        List<Meeting> updateMeeting = Lists.newArrayList();
        for (String id : publishAppraiseDTO.getIds()) {
            // 答辩会议必须有一个必填材料、被评人、评委至少都存在一个才能发布
            if (publishAppraiseDTO.getIsPublish() == PublishStatusEnum.IS_PUBLISH.getValue()) {

                LambdaQueryWrapper<MeetingReferee> refereeLambdaQueryWrapper = new LambdaQueryWrapper<>();
                refereeLambdaQueryWrapper.eq(MeetingReferee::getAppraiseId, id);
                long refereeCount = meetingRefereeService.count(refereeLambdaQueryWrapper);
                if (refereeCount == 0) {
                    throw new BusinessException(AppraiseErrorNoEnum.ERR_PUBLISH_MUST_ADD_APPRAISE_REFEREE);
                }

                LambdaQueryWrapper<MeetingProvider> providerLambdaQueryWrapper = new LambdaQueryWrapper<>();
                providerLambdaQueryWrapper.eq(MeetingProvider::getAppraiseId, id);
                long providerCount = meetingProviderService.count(providerLambdaQueryWrapper);
                if (providerCount == 0) {
                    throw new BusinessException(AppraiseErrorNoEnum.ERR_PUBLISH_MUST_ADD_MEETING_PROVIDER);
                }
            }
            meeting = new Meeting();
            meeting.setId(id);
            meeting.setIsPublish(publishAppraiseDTO.getIsPublish());
            meeting.setUpdateBy(UserThreadContext.getUserId());
            meeting.setUpdateTime(new Date());
            meeting.setPublishBy(UserThreadContext.getUserId());
            meeting.setPublishTime(new Date());
            updateMeeting.add(meeting);
        }
        updateBatchById2(updateMeeting);

        // 发送发布资源操作事件消息
        publishAppraiseDTO.getIds().forEach(id -> {
            mqProducer.sendMsg(new ResourceOperateEvent(
                Objects.equals(publishAppraiseDTO.getIsPublish(), GeneralJudgeEnum.NEGATIVE.getValue())
                    ? OperationEnum.PUBLISH_CANCEL : OperationEnum.PUBLISH, PushType.MEETING.getKey(), id));

            mqProducer.sendMsg(new ResourceSyncEvent(
                new ResourceSyncDTO(OperationEnum.UPDATE, ResourceTypeEnum.MEETING.name(), id, null, null, null, null,
                    publishAppraiseDTO.getIsPublish(), null, null, null, UserThreadContext.getUserId(), new Date())));
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteMeeting(String ids) {
        if (StringUtils.isEmpty(ids)) {
            return;
        }

        String[] split = ids.split(CommonConstants.A_COMMA_IN_ENGLISH);
        for (String id : split) {
            // 已发布不能删除
            Meeting byId = getById(id);
            if (null == byId) {
                throw new BusinessException(AppraiseErrorNoEnum.RESOURCE_NOT_EXIST);
            }

            if (byId.getIsPublish() == PublishStatusEnum.IS_PUBLISH.getValue()) {
                throw new BusinessException(AppraiseErrorNoEnum.ERR_PUBLISH_NOT_DELETE);
            }

            removeById(id);
            mqProducer.sendMsg(new ResourceOperateEvent(OperationEnum.DELETE, PushType.MEETING.getKey(), id));

            mqProducer.sendMsg(new ResourceSyncEvent(
                new ResourceSyncDTO(OperationEnum.UPDATE, ResourceTypeEnum.MEETING.name(), id, null, null, null, null,
                    null, 1, null, null, UserThreadContext.getUserId(), new Date())));
        }
    }

    @Override
    public Integer canOperate(String id) {
        return GeneralJudgeEnum.CONFIRM.getValue();
    }

    @Override
    public Result<PageInfo<AppraiseDTO>> getAppraiseList(int pageNo, int pageSize, AppraiseQuery appraiseAo) {

        if (null == appraiseAo.getQueryType()) {
            throw new BusinessException(ErrorNoEnum.ERR_PARAMS);
        }

        AppraiseVirtualPo appraiseVirtualPo = new AppraiseVirtualPo();
        BeanUtils.copyProperties(appraiseAo, appraiseVirtualPo);
        appraiseVirtualPo.setCurrentUserId(UserThreadContext.getUserId());
        PageMethod.startPage(pageNo, pageSize).setCount(false);
        List<com.wunding.learn.appraise.service.admin.dto.AppraiseDTO> appraiseListVo = baseMapper.getAppraiseListVo(
            appraiseVirtualPo);

        List<AppraiseDTO> appraiseVOS = Lists.newArrayList();

        for (com.wunding.learn.appraise.service.admin.dto.AppraiseDTO appraiseVo : appraiseListVo) {
            AppraiseDTO appraiseVO = new AppraiseDTO();
            BeanUtils.copyProperties(appraiseVo, appraiseVO);

            appraiseVO.setBeginTime(appraiseVo.getJudgeBeginTime());
            appraiseVO.setEndTime(appraiseVo.getJudgeEndTime());
            appraiseVO.setCoverImage(
                StringUtils.isNotEmpty(appraiseVO.getCoverImage()) ? fileFeign.getFileUrl(appraiseVO.getCoverImage())
                    : StringUtils.EMPTY);

            LambdaQueryWrapper<MeetingProvider> appraiseProviderLambdaQueryWrapper = new LambdaQueryWrapper<>();
            appraiseProviderLambdaQueryWrapper.eq(MeetingProvider::getAppraiseId, appraiseVo.getId());
            appraiseProviderLambdaQueryWrapper.eq(MeetingProvider::getUserId, UserThreadContext.getUserId());
            MeetingProvider providerServiceOne = meetingProviderService.getOne(appraiseProviderLambdaQueryWrapper);

            if (null != providerServiceOne
                && (appraiseAo.getQueryType() == 2)) {

                LambdaQueryWrapper<MeetingRefereeProviderScore> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                lambdaQueryWrapper.eq(MeetingRefereeProviderScore::getProviderId, providerServiceOne.getId());
                lambdaQueryWrapper.eq(MeetingRefereeProviderScore::getAppraiseId, appraiseVo.getId());
                long count = meetingRefereeProviderScoreService.count(lambdaQueryWrapper);
                if (0 != count) {
                    //有人答辩会议时才显示得分
                    appraiseVO.setMyScore(NumberOperationUtils.divide(providerServiceOne.getScore(),
                        NumberOperationUtils.ONE_HUNDRED_NUM, AppraiseConstants.APPRAISE_SCORE_PRECISION,
                        BigDecimal.ROUND_HALF_DOWN).toString());
                }
            }

            appraiseVOS.add(appraiseVO);
        }

        PageInfo<AppraiseDTO> pageModel = new PageInfo<>(appraiseVOS);
        pageModel.setPageNum(pageNo);
        pageModel.setPageSize(pageSize);
        return Result.success(pageModel);
    }

    /**
     * 当前人的角色
     *
     * @param appraiseId 答辩会议id
     * @return 1-学员，2-评委
     */
    private Integer chargeCurrentUserRole(String appraiseId) {
        String userId = UserThreadContext.getUserId();

        LambdaQueryWrapper<MeetingReferee> appraiseRefereeLambdaQueryWrapper = new LambdaQueryWrapper<>();
        appraiseRefereeLambdaQueryWrapper.eq(MeetingReferee::getUserId, userId);
        appraiseRefereeLambdaQueryWrapper.eq(MeetingReferee::getAppraiseId, appraiseId);
        long countReferee = meetingRefereeService.count(appraiseRefereeLambdaQueryWrapper);
        if (countReferee != 0) {
            return 2;
        }

        LambdaQueryWrapper<MeetingProvider> appraiseProviderLambdaQueryWrapper = new LambdaQueryWrapper<>();
        appraiseProviderLambdaQueryWrapper.eq(MeetingProvider::getUserId, userId);
        appraiseProviderLambdaQueryWrapper.eq(MeetingProvider::getAppraiseId, appraiseId);
        long countProvider = meetingProviderService.count(appraiseProviderLambdaQueryWrapper);
        if (countProvider != 0) {
            return 1;
        }
        return 0;
    }

    @Override
    public SaveMeetingDTO getSaveAppraiseDetail(String meetingId) {
        Meeting byId = getById(meetingId);
        if (null == byId) {
            throw new BusinessException(AppraiseErrorNoEnum.RESOURCE_NOT_EXIST);
        }
        SaveMeetingDTO appraiseDTO = new SaveMeetingDTO();
        BeanUtils.copyProperties(byId, appraiseDTO);
        // 返回归属部门名称
        appraiseDTO.setOrgName(orgFeign.getById(appraiseDTO.getOrgId()).getOrgName());
        appraiseDTO.setCoverImage(
            fileFeign.getImageFileNamePath(appraiseDTO.getId(), ImageBizType.MEETING_COVER_IMG.name()));
        List<ViewLimitBeanDTO> providerAndRefereeByAppraiseId = meetingProviderService.getProviderAndRefereeByAppraiseId(
            meetingId);
        if (!CollectionUtils.isEmpty(providerAndRefereeByAppraiseId)) {
            List<String> userIds = providerAndRefereeByAppraiseId.stream().map(SaveViewLimitDTO::getCategoryId)
                .collect(Collectors.toList());
            Map<String, UserDTO> userNameMapByIds = userFeign.getUserNameMapByIds(userIds);
            for (ViewLimitBeanDTO dto : providerAndRefereeByAppraiseId) {
                UserDTO userDTO = userNameMapByIds.get(dto.getCategoryId());
                if (userDTO != null) {
                    dto.setCategoryName(userDTO.getFullName());
                }
            }
            Optional.ofNullable(meetingViewLimitComponent.getViewLimitBaseInfo(meetingId)).ifPresent(limitInfo -> {
                appraiseDTO.setLimit(limitInfo);
                appraiseDTO.setProgrammeId(limitInfo.getProgrammeId());
            });
        }
        appraiseDTO.setIsTrain(0);
        Integer isStatusByMeetingId = meetingProviderService.getIsStatusByMeetingId(meetingId);
        appraiseDTO.setPeopleCount(isStatusByMeetingId);
        return appraiseDTO;
    }

    @Override
    public Result<MeetingClientDetailDTO> getClientAppraiseDetail(String meetingId) {
        Meeting byId = getById(meetingId);
        if (null == byId) {
            throw new BusinessException(AppraiseErrorNoEnum.RESOURCE_NOT_EXIST);
        }

        if (byId.getIsPublish().equals(PublishEnum.NOT_PUBLISH.getValue())) {
            throw new BusinessException(ErrorNoEnum.ERR_IS_NO_PUBLISH);
        }

        Integer userCurrentRole = this.chargeCurrentUserRole(meetingId);
        if (0 == userCurrentRole) {
            //无权访问
            throw new BusinessException(ErrorNoEnum.ERR_NO_POWER);
        }

        MeetingClientDetailDTO appraiseDetailVO = new MeetingClientDetailDTO();
        BeanUtils.copyProperties(byId, appraiseDetailVO);
        appraiseDetailVO.setJudgeBeginTime(byId.getStartTime());
        appraiseDetailVO.setJudgeEndTime(byId.getEndTime());
        appraiseDetailVO.setCurrentRole(userCurrentRole);
        if (1 == userCurrentRole) {
            // 当前用户是被评人时需要提示是否缺少上传材料
            LambdaQueryWrapper<MeetingProvider> appraiseProviderLambdaQueryWrapper = new LambdaQueryWrapper<>();
            appraiseProviderLambdaQueryWrapper.eq(MeetingProvider::getUserId, UserThreadContext.getUserId());
            appraiseProviderLambdaQueryWrapper.eq(MeetingProvider::getAppraiseId, meetingId);
        }

        //被评人总数数
        LambdaQueryWrapper<MeetingProvider> appraiseProviderLambdaQueryWrapper = new LambdaQueryWrapper<>();
        appraiseProviderLambdaQueryWrapper.eq(MeetingProvider::getAppraiseId, meetingId);
        long countProvider = meetingProviderService.count(appraiseProviderLambdaQueryWrapper);
        appraiseDetailVO.setProviderUserCount((int) countProvider);

        //评委人数
        LambdaQueryWrapper<MeetingReferee> appraiseRefereeLambdaQueryWrapper = new LambdaQueryWrapper<>();
        appraiseRefereeLambdaQueryWrapper.eq(MeetingReferee::getAppraiseId, meetingId);
        long countReferee = meetingRefereeService.count(appraiseRefereeLambdaQueryWrapper);
        appraiseDetailVO.setRefereeUserCount((int) countReferee);

        appraiseDetailVO.setCoverImage(StringUtils.isNotEmpty(appraiseDetailVO.getCoverImage()) ? fileFeign.getFileUrl(
            appraiseDetailVO.getCoverImage()) : StringUtils.EMPTY);
        //评价活动说明
        appraiseDetailVO.setActivityDescription(byId.getActivityDescription());

        //已结束的评价返回通过清单
        if (byId.getEndTime().getTime() < System.currentTimeMillis()) {

            List<MeetingClientDetailDTO.MeetingPass> appraisePassList = Lists.newArrayList();

            //仅显示大于8分且进入后台排名名次的人
            LambdaQueryWrapper<MeetingProvider> providerLambdaQueryWrapper = new LambdaQueryWrapper<>();
            providerLambdaQueryWrapper.eq(MeetingProvider::getAppraiseId, meetingId);
            providerLambdaQueryWrapper.ge(MeetingProvider::getScore,
                AppraiseConstants.APPRAISE_SCORE_EIGHT * NumberOperationUtils.ONE_HUNDRED_NUM);
            providerLambdaQueryWrapper.orderByDesc(MeetingProvider::getScore);
            providerLambdaQueryWrapper.orderByDesc(MeetingProvider::getCreateTime);
            providerLambdaQueryWrapper.orderByAsc(MeetingProvider::getId);
            PageMethod.startPage(1, 10000).setCount(false);
            List<MeetingProvider> appraiseProviders = meetingProviderService.list(providerLambdaQueryWrapper);

            Set<String> userIds = appraiseProviders.stream().map(MeetingProvider::getUserId)
                .collect(Collectors.toSet());
            Map<String, String> userIdNameMap = userFeign.getUseListByIds(userIds).stream()
                .collect(Collectors.toMap(UserDTO::getId, UserDTO::getFullName, (key1, key2) -> key1));

            MeetingPass meetingPass;
            int rank = 1;
            for (MeetingProvider appraiseProvider : appraiseProviders) {
                meetingPass = new MeetingClientDetailDTO().createAppraisePass();
                meetingPass.setId(appraiseProvider.getId());
                meetingPass.setRank(rank);
                meetingPass.setScore(
                    NumberOperationUtils.divide(appraiseProvider.getScore(), 100,
                        1, BigDecimal.ROUND_HALF_DOWN).toString());
                meetingPass.setLoginName(
                    StringUtils.defaultString(userIdNameMap.get(appraiseProvider.getUserId()), StringUtils.EMPTY));
                rank++;
            }
            appraiseDetailVO.setMeetingPassList(appraisePassList);
        }

        return Result.success(appraiseDetailVO);
    }

    @Override
    public Result<PageInfo<ParticipatorDTO>> getProviders(int pageNo, int pageSize, String appraiseId) {

        Meeting byId = getById(appraiseId);
        if (null == byId) {
            throw new BusinessException(AppraiseErrorNoEnum.RESOURCE_NOT_EXIST);
        }

        LambdaQueryWrapper<MeetingReferee> appraiseRefereeWrapper = new LambdaQueryWrapper<>();
        appraiseRefereeWrapper.eq(MeetingReferee::getUserId, UserThreadContext.getUserId());
        appraiseRefereeWrapper.eq(MeetingReferee::getAppraiseId, appraiseId);
        MeetingReferee appraiseRefereeServiceOne = meetingRefereeService.getOne(appraiseRefereeWrapper);
        if (null == appraiseRefereeServiceOne) {
            //只有评委能访问
            throw new BusinessException(ErrorNoEnum.ERR_NO_POWER);
        }

        PageMethod.startPage(pageNo, pageSize).setCount(false);
        LambdaQueryWrapper<MeetingProvider> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(MeetingProvider::getAppraiseId, appraiseId);
        List<MeetingProvider> appraiseProviders = meetingProviderService.list(lambdaQueryWrapper);
        Set<String> userIds = appraiseProviders.stream().map(MeetingProvider::getUserId).collect(Collectors.toSet());
        Map<String, String> userIdNameMap = userFeign.getUseListByIds(userIds).stream()
            .collect(Collectors.toMap(UserDTO::getId, UserDTO::getFullName, (key1, key2) -> key1));
        List<ParticipatorDTO> appraisePassList = Lists.newArrayList();
        appraiseProviders.forEach(appraiseProvider -> {

            LambdaQueryWrapper<MeetingRefereeProviderScore> appraiseScoreLambdaQueryWrapper = new LambdaQueryWrapper<>();
            appraiseScoreLambdaQueryWrapper.eq(MeetingRefereeProviderScore::getAppraiseId, appraiseId);
            appraiseScoreLambdaQueryWrapper.eq(MeetingRefereeProviderScore::getProviderId, appraiseProvider.getId());
            appraiseScoreLambdaQueryWrapper.eq(MeetingRefereeProviderScore::getRefereeId,
                appraiseRefereeServiceOne.getId());
            MeetingRefereeProviderScore refereeProviderScoreServiceOne = meetingRefereeProviderScoreService.getOne(
                appraiseScoreLambdaQueryWrapper);

            ParticipatorDTO participator = new ParticipatorDTO();
            participator.setId(appraiseProvider.getId());
            participator.setParticipatorName(
                StringUtils.defaultString(userIdNameMap.get(appraiseProvider.getUserId()), StringUtils.EMPTY));

            if (null != refereeProviderScoreServiceOne) {
                participator.setStatus(1);
                participator.setParticipatorTime(refereeProviderScoreServiceOne.getCreateTime());
                participator.setScore(NumberOperationUtils.divide(refereeProviderScoreServiceOne.getScore(),
                    appraiseRefereeServiceOne.getWeight(), AppraiseConstants.APPRAISE_SCORE_PRECISION,
                    BigDecimal.ROUND_HALF_DOWN).toString());
            } else {
                participator.setStatus(0);
            }
            appraisePassList.add(participator);
        });

        PageInfo<ParticipatorDTO> pageModel = new PageInfo<>(appraisePassList);
        pageModel.setPageNum(pageNo);
        pageModel.setPageSize(pageSize);
        pageModel.setIsLastPage(pageSize != pageModel.getList().size());
        return Result.success(pageModel);
    }

    @Override
    public AppraiseRefereeDTO getMeetingReferees(String meetingId, String providerId) {
        Meeting meeting = getById(meetingId);
        // 答辩会议不存在
        if (null == meeting) {
            throw new BusinessException(AppraiseErrorNoEnum.RESOURCE_NOT_EXIST);
        }

        LambdaQueryWrapper<MeetingReferee> appraiseRefereeLambdaQueryWrapper = new LambdaQueryWrapper<>();
        appraiseRefereeLambdaQueryWrapper.eq(MeetingReferee::getUserId, UserThreadContext.getUserId());
        appraiseRefereeLambdaQueryWrapper.eq(MeetingReferee::getAppraiseId, meetingId);
        MeetingReferee appraiseRefereeServiceOne = meetingRefereeService.getOne(appraiseRefereeLambdaQueryWrapper);
        // 当前用户不是评委，无权操作
        if (null == appraiseRefereeServiceOne) {
            throw new BusinessException(ErrorNoEnum.ERR_NO_POWER);
        }

        MeetingProvider appraiseProviderServiceById = meetingProviderService.getById(providerId);
        // 被评人不存在
        if (null == appraiseProviderServiceById) {
            throw new BusinessException(AppraiseErrorNoEnum.RESOURCE_NOT_EXIST);
        }

        LambdaQueryWrapper<MeetingScore> appraiseScoreWrapper = new LambdaQueryWrapper<>();
        appraiseScoreWrapper.eq(MeetingScore::getRefereeId, appraiseRefereeServiceOne.getId());
        appraiseScoreWrapper.eq(MeetingScore::getProviderId, providerId);
        appraiseScoreWrapper.eq(MeetingScore::getAppraiseId, meetingId);
        long count = meetingScoreService.count(appraiseScoreWrapper);
        // 不能重复提交
        if (0 != count) {
            throw new BusinessException(AppraiseErrorNoEnum.ERR_ALREADY_AUDIT);
        }
        AppraiseRefereeDTO appraiseRefereeVO = new AppraiseRefereeDTO();
        // 当前被评人信息
        Optional.ofNullable(userFeign.getUserById(appraiseProviderServiceById.getUserId())).ifPresent(userDTO -> {
            appraiseRefereeVO.setCurrentUserId(userDTO.getId());
            appraiseRefereeVO.setCurrentUserName(userDTO.getFullName());
        });
        // 任职资格id
        appraiseRefereeVO.setJobQualificationId(meeting.getJobQualificationId());
        // 答辩会议评分模板
        appraiseRefereeVO.setAppraiseTemplates(this.buildAppraiseTemplates(meetingId));
        // 答辩会议评论模板
        appraiseRefereeVO.setCommentCategories(
            this.buildCommentCategories(meetingId, providerId, appraiseRefereeServiceOne.getId()));
        // 被评人
        Map<String, String> needProviderIds = this.getNeedProviderIds(meetingId, providerId,
            appraiseRefereeServiceOne.getId());
        appraiseRefereeVO.setBeforeProviderId(needProviderIds.get("beforeId"));
        appraiseRefereeVO.setNextProviderId(needProviderIds.get("nextId"));
        return appraiseRefereeVO;
    }

    private Map<String, String> getNeedProviderIds(String appraiseId, String providerId, String refereeId) {

        //已经答辩会议的记录
        LambdaQueryWrapper<MeetingRefereeProviderScore> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.select(MeetingRefereeProviderScore::getProviderId);
        lambdaQueryWrapper.eq(MeetingRefereeProviderScore::getAppraiseId, appraiseId);
        lambdaQueryWrapper.eq(MeetingRefereeProviderScore::getRefereeId, refereeId);
        List<MeetingRefereeProviderScore> providerScores = meetingRefereeProviderScoreService.list(lambdaQueryWrapper);

        LambdaQueryWrapper<MeetingProvider> appraiseProviderLambdaQueryWrapper = new LambdaQueryWrapper<>();
        appraiseProviderLambdaQueryWrapper.select(MeetingProvider::getId);
        appraiseProviderLambdaQueryWrapper.eq(MeetingProvider::getAppraiseId, appraiseId);

        if (!CollectionUtils.isEmpty(providerScores)) {
            List<String> appraiseIds = providerScores.stream().map(MeetingRefereeProviderScore::getProviderId)
                .collect(Collectors.toList());
            appraiseProviderLambdaQueryWrapper.notIn(MeetingProvider::getId, appraiseIds);
        }

        appraiseProviderLambdaQueryWrapper.orderByDesc(MeetingProvider::getCreateTime);
        appraiseProviderLambdaQueryWrapper.orderByAsc(MeetingProvider::getId);

        List<MeetingProvider> appraiseProviders = meetingProviderService.list(appraiseProviderLambdaQueryWrapper);
        List<String> allProviderIds = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(appraiseProviders)) {
            allProviderIds = appraiseProviders.stream().map(MeetingProvider::getId).collect(Collectors.toList());
        }

        String beforeId = null;
        String nextId = null;
        int currentProviderIdIndex = allProviderIds.indexOf(providerId);

        if (0 == currentProviderIdIndex) {
            // 第一个
            if (allProviderIds.size() - 1 > currentProviderIdIndex) {
                //如果有下一个
                nextId = allProviderIds.get(currentProviderIdIndex + 1);
            }
        } else if (allProviderIds.size() - 1 == currentProviderIdIndex) {
            //最后一个
            beforeId = allProviderIds.get(currentProviderIdIndex - 1);
        } else {
            //位于中间
            beforeId = allProviderIds.get(currentProviderIdIndex - 1);
            nextId = allProviderIds.get(currentProviderIdIndex + 1);
        }

        Map<String, String> result = new HashMap<>(4);
        result.put("beforeId", beforeId);
        result.put("nextId", nextId);
        return result;
    }

    @Override
    public Result<AppraiseRefereeDetailDTO> getAppraiseRefereeDetail(String appraiseId) {
        Meeting appraise = getById(appraiseId);
        if (null == appraise) {
            //答辩会议不存在
            throw new BusinessException(AppraiseErrorNoEnum.RESOURCE_NOT_EXIST);
        }

        LambdaQueryWrapper<MeetingProvider> appraiseRefereeLambdaQueryWrapper = new LambdaQueryWrapper<>();
        appraiseRefereeLambdaQueryWrapper.eq(MeetingProvider::getUserId, UserThreadContext.getUserId());
        appraiseRefereeLambdaQueryWrapper.eq(MeetingProvider::getAppraiseId, appraiseId);
        MeetingProvider appraiseProviderServiceOne = meetingProviderService.getOne(appraiseRefereeLambdaQueryWrapper);
        if (null == appraiseProviderServiceOne) {
            //当前用户不是被评人，无权操作
            throw new BusinessException(ErrorNoEnum.ERR_NO_POWER);
        }

        AppraiseRefereeDetailDTO appraiseRefereeDetailVO = new AppraiseRefereeDetailDTO();
        appraiseRefereeDetailVO.setAppraiseTemplates(this.buildAppraiseTemplates(appraiseId));
        appraiseRefereeDetailVO.setRefereeScoreListVOS(
            this.buildScoreCategoryDetails(appraiseId, appraiseProviderServiceOne.getId(), 1));
        appraiseRefereeDetailVO.setCommentCategories(
            this.buildCommentCategories(appraiseId, appraiseProviderServiceOne.getId(), null));

        LambdaQueryWrapper<MeetingRefereeProviderScore> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(MeetingRefereeProviderScore::getProviderId, appraiseProviderServiceOne.getId());
        lambdaQueryWrapper.eq(MeetingRefereeProviderScore::getAppraiseId, appraiseId);
        long count = meetingRefereeProviderScoreService.count(lambdaQueryWrapper);
        if (0 != count) {
            //有人答辩会议时才显示得分
            appraiseRefereeDetailVO.setScore(
                NumberOperationUtils.divide(appraiseProviderServiceOne.getScore(), NumberOperationUtils.ONE_HUNDRED_NUM,
                    AppraiseConstants.APPRAISE_SCORE_PRECISION, BigDecimal.ROUND_HALF_DOWN).toString());
        }

        Result<AppraiseRefereeDetailDTO> result = new Result<>();
        result.setData(appraiseRefereeDetailVO);
        return Result.success(appraiseRefereeDetailVO);
    }

    @Override
    public Result<AppraiseRefereeDetailDTO> getBeAppraiseRefereeDetail(String meetingId, String providerId) {

        Meeting meeting = getById(meetingId);
        if (null == meeting) {
            //答辩会议不存在
            throw new BusinessException(AppraiseErrorNoEnum.RESOURCE_NOT_EXIST);
        }

        LambdaQueryWrapper<MeetingReferee> appraiseRefereeLambdaQueryWrapper = new LambdaQueryWrapper<>();
        appraiseRefereeLambdaQueryWrapper.eq(MeetingReferee::getUserId, UserThreadContext.getUserId());
        appraiseRefereeLambdaQueryWrapper.eq(MeetingReferee::getAppraiseId, meetingId);
        MeetingReferee appraiseReferee = meetingRefereeService.getOne(appraiseRefereeLambdaQueryWrapper);
        if (null == appraiseReferee) {
            //当前用户不是评委，无权操作
            throw new BusinessException(ErrorNoEnum.ERR_NO_POWER);
        }

        LambdaQueryWrapper<MeetingRefereeProviderScore> appraiseScoreLambdaQueryWrapper = new LambdaQueryWrapper<>();
        appraiseScoreLambdaQueryWrapper.eq(MeetingRefereeProviderScore::getAppraiseId, meetingId);
        appraiseScoreLambdaQueryWrapper.eq(MeetingRefereeProviderScore::getProviderId, providerId);
        appraiseScoreLambdaQueryWrapper.eq(MeetingRefereeProviderScore::getRefereeId, appraiseReferee.getId());
        MeetingRefereeProviderScore refereeProviderScoreServiceOne = meetingRefereeProviderScoreService.getOne(
            appraiseScoreLambdaQueryWrapper);
        if (null == refereeProviderScoreServiceOne && meeting.getEndTime().after(new Date())) {
            //能看到详情的条件是：当前评委对被评人答辩会议过后，或者，整个答辩会议结束之后
            throw new BusinessException(ErrorNoEnum.ERR_NO_POWER);
        }

        AppraiseRefereeDetailDTO appraiseRefereeDetailVO = new AppraiseRefereeDetailDTO();

        appraiseRefereeDetailVO.setAppraiseTemplates(this.buildAppraiseTemplates(meetingId));
        appraiseRefereeDetailVO.setRefereeScoreListVOS(this.buildScoreCategoryDetails(meetingId, providerId, 1));
        appraiseRefereeDetailVO.setCommentCategories(
            this.buildCommentCategories(meetingId, providerId, appraiseReferee.getId()));
        //评委打的分值
        LambdaQueryWrapper<MeetingRefereeProviderScore> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(MeetingRefereeProviderScore::getAppraiseId, meetingId);
        lambdaQueryWrapper.eq(MeetingRefereeProviderScore::getRefereeId, appraiseReferee.getId());
        lambdaQueryWrapper.eq(MeetingRefereeProviderScore::getProviderId, providerId);
        MeetingRefereeProviderScore providerScoreServiceOne = meetingRefereeProviderScoreService.getOne(
            lambdaQueryWrapper);
        //原始分
        appraiseRefereeDetailVO.setScore(
            null != providerScoreServiceOne ? NumberOperationUtils.divide(providerScoreServiceOne.getScore(),
                    appraiseReferee.getWeight(), AppraiseConstants.APPRAISE_SCORE_PRECISION, BigDecimal.ROUND_HALF_DOWN)
                .toString() : StringUtils.EMPTY);
        return Result.success(appraiseRefereeDetailVO);
    }

    /**
     * @param meetingId 答辩会议id
     * @return 评委打分模板
     */
    private List<AppraiseTemplateDTO> buildAppraiseTemplates(String meetingId) {

        List<AppraiseTemplateDTO> result = Lists.newArrayList();
        AppraiseTemplateDTO appraiseTemplateVO;
        List<AppraiseTemplateDTO.AppraiseItem> appraiseItems;
        AppraiseTemplateDTO.AppraiseItem appraiseItem;
        List<String> categoryList = meetingTemplateMapper.selectCategoryList(meetingId,
            AppraiseConstants.TEMPLATE_TYPE_SCORE);
        for (String category : categoryList) {

            appraiseTemplateVO = new AppraiseTemplateDTO();
            appraiseTemplateVO.setCategory(category);

            LambdaQueryWrapper<MeetingTemplate> appraiseTemplateLambdaQueryWrapper = new LambdaQueryWrapper<>();
            appraiseTemplateLambdaQueryWrapper.eq(MeetingTemplate::getAppraiseId, meetingId);
            appraiseTemplateLambdaQueryWrapper.eq(MeetingTemplate::getCategory, category);
            appraiseTemplateLambdaQueryWrapper.eq(MeetingTemplate::getType, AppraiseConstants.TEMPLATE_TYPE_SCORE);
            appraiseTemplateLambdaQueryWrapper.orderByAsc(MeetingTemplate::getNum);
            List<MeetingTemplate> appraiseTemplates = meetingTemplateService.list(appraiseTemplateLambdaQueryWrapper);

            appraiseItems = Lists.newArrayList();
            for (MeetingTemplate appraiseTemplate : appraiseTemplates) {
                appraiseItem = new AppraiseTemplateDTO().createAppraiseItem();
                appraiseItem.setTemplateId(appraiseTemplate.getId());
                appraiseItem.setTitle(appraiseTemplate.getTitle());
                appraiseItem.setContent(appraiseTemplate.getContent());
                appraiseItem.setDescription(appraiseTemplate.getDescription());
                appraiseItems.add(appraiseItem);
            }
            appraiseTemplateVO.setAppraiseItems(appraiseItems);

            result.add(appraiseTemplateVO);
        }
        return result;
    }

    /**
     * 构造评委打分明细
     *
     * @param appraiseId 答辩会议id
     * @param providerId 被评人id
     * @param viewType   1:结束后允许,2:不允许,3:匿名允许
     * @return 评委评分
     */
    private List<AppraiseRefereeDetailDTO.RefereeScoreListVO> buildScoreCategoryDetails(String appraiseId,
        String providerId, Integer viewType) {
        List<AppraiseRefereeDetailDTO.RefereeScoreListVO> result = Lists.newArrayList();

        LambdaQueryWrapper<MeetingRefereeProviderScore> refereeProviderScoreLambdaQueryWrapper = new LambdaQueryWrapper<>();
        refereeProviderScoreLambdaQueryWrapper.eq(MeetingRefereeProviderScore::getAppraiseId, appraiseId);
        refereeProviderScoreLambdaQueryWrapper.eq(MeetingRefereeProviderScore::getProviderId, providerId);
        List<MeetingRefereeProviderScore> refereeProviderScores = meetingRefereeProviderScoreService.list(
            refereeProviderScoreLambdaQueryWrapper);

        AppraiseRefereeDetailDTO.RefereeScoreListVO refereeScoreListVO;
        int anonymityNum = 1;
        for (MeetingRefereeProviderScore refereeProviderScore : refereeProviderScores) {
            refereeScoreListVO = new AppraiseRefereeDetailDTO().createRefereeScoreListVO();
            refereeScoreListVO.setRefereeId(refereeProviderScore.getRefereeId());

            MeetingReferee appraiseRefereeServiceById = meetingRefereeService.getById(
                refereeProviderScore.getRefereeId());
            Integer isAnonymous = appraiseRefereeServiceById.getIsAnonymous();

            if (3 == viewType) {
                //允许且匿名评
                refereeScoreListVO.setRefereeName("评委" + anonymityNum);
                anonymityNum++;
            } else {
                if (1 == isAnonymous) {
                    refereeScoreListVO.setRefereeName("评委" + anonymityNum);
                    anonymityNum++;
                } else {
                    UserDTO user = userFeign.getUserById(appraiseRefereeServiceById.getUserId());
                    refereeScoreListVO.setRefereeName(user != null ? user.getFullName() : StringUtils.EMPTY);
                }
            }

            LambdaQueryWrapper<MeetingTemplate> appraiseTemplateLambdaQueryWrapper = new LambdaQueryWrapper<>();
            appraiseTemplateLambdaQueryWrapper.eq(MeetingTemplate::getAppraiseId, appraiseId);
            appraiseTemplateLambdaQueryWrapper.eq(MeetingTemplate::getType, AppraiseConstants.TEMPLATE_TYPE_SCORE);
            List<MeetingTemplate> appraiseTemplates = meetingTemplateService.list(appraiseTemplateLambdaQueryWrapper);

            List<AppraiseRefereeDetailDTO.ScoreDetail> scoreDetails = Lists.newArrayList();
            handleScoreDetail(appraiseId, providerId, refereeProviderScore, appraiseTemplates, scoreDetails);

            refereeScoreListVO.setScoreDetails(scoreDetails);
            result.add(refereeScoreListVO);
        }

        return result;
    }

    private void handleScoreDetail(String appraiseId, String providerId,
        MeetingRefereeProviderScore refereeProviderScore, List<MeetingTemplate> appraiseTemplates,
        List<ScoreDetail> scoreDetails) {
        for (MeetingTemplate appraiseTemplate : appraiseTemplates) {

            ScoreDetail scoreDetail = new AppraiseRefereeDetailDTO().createScoreDetail();
            scoreDetail.setTemplateId(appraiseTemplate.getId());

            LambdaQueryWrapper<MeetingScore> appraiseScoreLambdaQueryWrapper = new LambdaQueryWrapper<>();
            appraiseScoreLambdaQueryWrapper.eq(MeetingScore::getAppraiseId, appraiseId);
            appraiseScoreLambdaQueryWrapper.eq(MeetingScore::getProviderId, providerId);
            appraiseScoreLambdaQueryWrapper.eq(MeetingScore::getTemplateId, appraiseTemplate.getId());
            appraiseScoreLambdaQueryWrapper.eq(MeetingScore::getRefereeId, refereeProviderScore.getRefereeId());
            MeetingScore appraiseScoreServiceOne = meetingScoreService.getOne(appraiseScoreLambdaQueryWrapper);
            if (appraiseScoreServiceOne != null) {
                scoreDetail.setScore(NumberOperationUtils.divide(appraiseScoreServiceOne.getScore(),
                    NumberOperationUtils.ONE_HUNDRED_NUM, AppraiseConstants.APPRAISE_SCORE_PRECISION,
                    BigDecimal.ROUND_HALF_DOWN).toString());
            }
            scoreDetails.add(scoreDetail);
        }
    }

    /**
     * 构造答辩会议内容
     *
     * @param appraiseId 答辩会议id
     * @param providerId 被评人id
     * @param refereeId  评委id
     * @return 评论内容
     */
    private List<CommentCategory> buildCommentCategories(String appraiseId, String providerId, String refereeId) {
        List<CommentCategory> result = Lists.newArrayList();
        List<String> categoryList = meetingTemplateMapper.selectCategoryList(appraiseId,
            AppraiseConstants.TEMPLATE_TYPE_QUESTIONS);

        List<CommentCategory.Comment> comments;
        CommentCategory commentCategory;
        CommentCategory.Comment comment;
        for (String category : categoryList) {

            commentCategory = new CommentCategory();
            commentCategory.setCategory(category);

            LambdaQueryWrapper<MeetingTemplate> appraiseTemplateLambdaQueryWrapper = new LambdaQueryWrapper<>();
            appraiseTemplateLambdaQueryWrapper.eq(MeetingTemplate::getAppraiseId, appraiseId);
            appraiseTemplateLambdaQueryWrapper.eq(MeetingTemplate::getCategory, category);
            appraiseTemplateLambdaQueryWrapper.eq(MeetingTemplate::getType, AppraiseConstants.TEMPLATE_TYPE_QUESTIONS);
            List<MeetingTemplate> appraiseTemplates = meetingTemplateService.list(appraiseTemplateLambdaQueryWrapper);

            comments = Lists.newArrayList();
            for (MeetingTemplate appraiseTemplate : appraiseTemplates) {
                comment = new CommentCategory().createComment();
                comment.setTitle(appraiseTemplate.getTitle());
                comment.setTemplateId(appraiseTemplate.getId());

                LambdaQueryWrapper<MeetingScore> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                lambdaQueryWrapper.eq(MeetingScore::getTemplateId, appraiseTemplate.getId());
                lambdaQueryWrapper.eq(MeetingScore::getAppraiseId, appraiseId);
                lambdaQueryWrapper.eq(MeetingScore::getProviderId, providerId);
                //refereeId 评委id可能为空
                lambdaQueryWrapper.eq(null != refereeId, MeetingScore::getRefereeId, refereeId);
                List<MeetingScore> appraiseScores = meetingScoreService.list(lambdaQueryWrapper);
                List<String> commentContents = Lists.newArrayList();
                for (MeetingScore appraiseScore : appraiseScores) {
                    if (StringUtils.isNotEmpty(appraiseScore.getRemark())) {
                        commentContents.add(appraiseScore.getRemark());
                    }
                }
                comment.setCommentContents(commentContents);
                comments.add(comment);
            }

            commentCategory.setComments(comments);
            result.add(commentCategory);
        }

        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<Void> submitAppraiseScore(MeetingScoreListDTO appraiseScoreListAO) {

        Meeting appraise = getById(appraiseScoreListAO.getAppraiseId());
        if (null == appraise) {
            //答辩会议不存在
            throw new BusinessException(AppraiseErrorNoEnum.RESOURCE_NOT_EXIST);
        }

        LambdaQueryWrapper<MeetingReferee> appraiseRefereeLambdaQueryWrapper = new LambdaQueryWrapper<>();
        appraiseRefereeLambdaQueryWrapper.eq(MeetingReferee::getUserId, UserThreadContext.getUserId());
        appraiseRefereeLambdaQueryWrapper.eq(MeetingReferee::getAppraiseId, appraiseScoreListAO.getAppraiseId());
        MeetingReferee appraiseRefereeServiceOne = meetingRefereeService.getOne(appraiseRefereeLambdaQueryWrapper);
        if (null == appraiseRefereeServiceOne) {
            //当前用户不是评委，无权操作
            throw new BusinessException(ErrorNoEnum.ERR_NO_POWER);
        }

        MeetingProvider providerServiceById = meetingProviderService.getById(appraiseScoreListAO.getProviderId());
        if (null == providerServiceById) {
            //用户不存在
            throw new BusinessException(AppraiseErrorNoEnum.RESOURCE_NOT_EXIST);
        }

        LambdaQueryWrapper<MeetingScore> appraiseScoreWrapper = new LambdaQueryWrapper<>();
        appraiseScoreWrapper.eq(MeetingScore::getRefereeId, appraiseRefereeServiceOne.getId());
        appraiseScoreWrapper.eq(MeetingScore::getProviderId, appraiseScoreListAO.getProviderId());
        appraiseScoreWrapper.eq(MeetingScore::getAppraiseId, appraiseScoreListAO.getAppraiseId());
        long count = meetingScoreService.count(appraiseScoreWrapper);
        if (0 != count) {
            //不能重复提交
            throw new BusinessException(AppraiseErrorNoEnum.ERR_ALREADY_AUDIT);
        }

        MeetingScore insertAppraiseScore;
        List<MeetingScore> appraiseScores = Lists.newArrayList();
        for (AppraiseScoreDTO appraiseScoreAO : appraiseScoreListAO.getAppraiseScoreAOS()) {

            MeetingTemplate appraiseTemplateServiceById = meetingTemplateService.getById(
                appraiseScoreAO.getTemplateId());
            if (null == appraiseTemplateServiceById) {
                //模板不存在
                throw new BusinessException(AppraiseErrorNoEnum.RESOURCE_NOT_EXIST);
            }

            insertAppraiseScore = new MeetingScore();
            insertAppraiseScore.setId(StringUtil.newId());
            insertAppraiseScore.setAppraiseId(appraiseScoreListAO.getAppraiseId());
            insertAppraiseScore.setProviderId(appraiseScoreListAO.getProviderId());
            insertAppraiseScore.setRefereeId(appraiseRefereeServiceOne.getId());
            if (appraiseTemplateServiceById.getType() == AppraiseConstants.TEMPLATE_TYPE_SCORE) {
                //weight 也是百分制的所以不需要在进行转换了，这里放入的值就是 原始值的100倍
                insertAppraiseScore.setWeightScore(appraiseScoreAO.getScore() * appraiseRefereeServiceOne.getWeight());
                insertAppraiseScore.setScore(appraiseScoreAO.getScore() * NumberOperationUtils.ONE_HUNDRED_NUM);
            } else if (appraiseTemplateServiceById.getType() == AppraiseConstants.TEMPLATE_TYPE_QUESTIONS) {
                insertAppraiseScore.setRemark(appraiseScoreAO.getComment());
            }

            insertAppraiseScore.setTemplateId(appraiseScoreAO.getTemplateId());
            appraiseScores.add(insertAppraiseScore);
        }
        //批量保存
        meetingScoreService.saveOrUpdateBatch(appraiseScores);

        //汇总单项汇总表
        meetingProviderSingleScoreService.sumProviderSingleScore(appraiseScoreListAO.getAppraiseId(),
            appraiseScoreListAO.getProviderId());

        //计算答辩会议人与被评人分数
        this.calculateRefereeProviderScore(appraiseScoreListAO.getAppraiseId(), appraiseScoreListAO.getProviderId(),
            appraiseRefereeServiceOne.getId(), appraiseScoreListAO.getIsPass());

        MeetingProvider byId = meetingProviderService.getById(appraiseScoreListAO.getProviderId());

        sendPushFeign(byId.getUserId(), appraise);

        // 发送资源记录同步事件 评委完成
        mqProducer.sendMsg(new ResourceRecordSyncEvent(
            new ResourceRecordSyncDTO(OperationEnum.CREATE, ResourceTypeEnum.APPRAISE.name(),
                appraiseScores.size() > 0 ? appraiseScores.get(0).getId() : StringUtil.newId(),
                appraiseScoreListAO.getAppraiseId(), appraiseRefereeServiceOne.getUserId(), 1,
                UserThreadContext.getUserId(), new Date(), UserThreadContext.getUserId(), new Date())));

        return Result.success();
    }

    private void sendPushFeign(String userId, Meeting appraise) {
        log.info("userId:[{}]", userId);
        // 保存推送范围
        TreeSet<ViewLimitMainSaveDTO> viewLimitMainSaveDTOList = new TreeSet<>();
        ViewLimitMainSaveDTO viewLimitMainSaveDTO = new ViewLimitMainSaveDTO();
        viewLimitMainSaveDTO.setViewId(userId).setViewType(2).setLimitType(0);
        viewLimitMainSaveDTOList.add(viewLimitMainSaveDTO);
        ViewLimitProgrammeInfoDTO viewLimitProgrammeInfoDTO = viewLimitFeign.saveViewLimit(viewLimitMainSaveDTOList);

        SendPushDTO sendPushDTO = new SendPushDTO();
        sendPushDTO.setLang(LocaleContextHolder.getLocale().getLanguage());
        sendPushDTO.setServerName(Objects.requireNonNull(WebUtil.getRequest()).getServerName());

        PushResourceDTO pushResourceDTO = new PushResourceDTO();
        pushResourceDTO.setResourceId(appraise.getId());
        pushResourceDTO.setResourceType(PushType.MEETING.getKey());
        pushResourceDTO.setResourceName(appraise.getTitle());
        pushResourceDTO.setIsTrain(0);
        pushResourceDTO.setOperateState(1);
        pushResourceDTO.setProgrammeId(viewLimitProgrammeInfoDTO.getId());
        sendPushDTO.setPushResourceDTO(pushResourceDTO);

        PushNoticeSetDTO pushNoticeSetDTO = new PushNoticeSetDTO();
        pushNoticeSetDTO.setPushMethod(2);
        EventPushSet eventPushSet = new EventPushSet();
        eventPushSet.setEventId(PushNoticeEventEnum.MEETING_58.getId());
        pushNoticeSetDTO.setEventPushSet(eventPushSet);
        sendPushDTO.setPushNoticeSetDTO(pushNoticeSetDTO);

        PushAttributeDTO pushAttributeDTO = new PushAttributeDTO();
        pushAttributeDTO.setName(appraise.getTitle());
        pushAttributeDTO.setStartTime(appraise.getStartTime());
        pushAttributeDTO.setEndTime(appraise.getEndTime());
        pushAttributeDTO.setIntro(appraise.getActivityDescription());
        String systemName = (String) redisTemplate.opsForHash()
            .get(TenantRedisKeyConstant.SYSTEM_NAME_KEY, UserThreadContext.getTenantId());
        pushAttributeDTO.setSystemName(systemName);
        sendPushDTO.setPushAttributeDTO(pushAttributeDTO);

        pushFeign.sendPush(sendPushDTO);
    }

    /**
     * 计算答辩会议人与被评人分数
     *
     * @param meetingId  答辩会议id
     * @param providerId 被评人id
     * @param refereeId  评委id
     */
    private void calculateRefereeProviderScore(String meetingId, String providerId, String refereeId, Integer isPass) {
        //答辩会议人与被评人分数平均分
        int sumRefereeProviderScore = meetingScoreMapper.sumRefereeProviderScore(providerId, meetingId, refereeId);

        MeetingRefereeProviderScore saveOrUpdateRefereeProviderScore = new MeetingRefereeProviderScore();
        saveOrUpdateRefereeProviderScore.setAppraiseId(meetingId);
        saveOrUpdateRefereeProviderScore.setProviderId(providerId);
        saveOrUpdateRefereeProviderScore.setRefereeId(refereeId);
        saveOrUpdateRefereeProviderScore.setScore(sumRefereeProviderScore);
        saveOrUpdateRefereeProviderScore.setIsPass(isPass);

        LambdaQueryWrapper<MeetingRefereeProviderScore> refereeProviderScoreLambdaQueryWrapper = new LambdaQueryWrapper<>();
        refereeProviderScoreLambdaQueryWrapper.eq(MeetingRefereeProviderScore::getAppraiseId, meetingId);
        refereeProviderScoreLambdaQueryWrapper.eq(MeetingRefereeProviderScore::getProviderId, providerId);
        refereeProviderScoreLambdaQueryWrapper.eq(MeetingRefereeProviderScore::getRefereeId, refereeId);
        MeetingRefereeProviderScore providerScoreServiceOne = meetingRefereeProviderScoreService.getOne(
            refereeProviderScoreLambdaQueryWrapper);
        if (null == providerScoreServiceOne) {
            saveOrUpdateRefereeProviderScore.setId(StringUtil.newId());
            meetingRefereeProviderScoreService.save(saveOrUpdateRefereeProviderScore);
        } else {
            saveOrUpdateRefereeProviderScore.setId(providerScoreServiceOne.getId());
            meetingRefereeProviderScoreService.updateById(saveOrUpdateRefereeProviderScore);
        }

        //计算总分
        MeetingProvider appraiseProvider = meetingProviderService.getById(providerId);
        appraiseProvider.setScore(meetingRefereeProviderScoreMapper.sumRefereeProviderScore(providerId, meetingId));
        meetingProviderService.updateById(appraiseProvider);
    }

    @Override
    public void export(MeetingListQuery meetingListQuery) {
        IExportNoEntityDataDTO exportDataDTO = new IExportNoEntityDataDTO() {
            @Override
            public List<List<Object>> getData(Integer pageNo, Integer pageSize) {
                List<List<Object>> data = ListUtils.newArrayList();
                IMeetingService appraiseService = SpringUtil.getBean("meetingService", IMeetingService.class);
                meetingListQuery.setExport(true);
                meetingListQuery.setPageNo(pageNo);
                meetingListQuery.setPageSize(pageSize);
                assert appraiseService != null;
                PageInfo<MeetingListDTO> pageInfo = appraiseService.queryPage(meetingListQuery);
                if (!CollectionUtils.isEmpty(pageInfo.getList())) {
                    pageInfo.getList().forEach(dto -> {
                        List<Object> dataList = ListUtils.newArrayList();
                        dataList.add(dto.getTitle());
                        dataList.add(dto.getCode());
                        dataList.add(
                            DateUtil.formatDate(dto.getStartTime(), DateUtil.YYMMDD_HHMM) + " ~ " + DateUtil.formatDate(
                                dto.getEndTime(), DateUtil.YYMMDD_HHMM));
                        dataList.add(
                            GeneralJudgeEnum.CONFIRM.getValue().equals(dto.getIsPublish()) ? "已发布" : "未发布");
                        dataList.add(
                            StringUtils.isNotBlank(dto.getPublishBy()) ? dto.getPublishBy() : StringUtils.EMPTY);
                        dataList.add(Optional.ofNullable(dto.getPublishTime()).isPresent() ? DateUtil.formatDate(
                            dto.getPublishTime(), DateUtil.YYMMDD_HHMM) : StringUtils.EMPTY);
                        dataList.add(dto.getOrgName());
                        dataList.add(DateUtil.formatDate(dto.getCreateTime(), DateUtil.YYMMDD_HHMM));
                        data.add(dataList);
                    });
                }
                return data;
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.Meeting;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.Meeting.getType();
            }

        };

        List<List<String>> head = getExportHeader();
        exportComponent.exportNoEntityRecord(exportDataDTO, head);
    }

    private List<List<String>> getExportHeader() {
        List<List<String>> head = new ArrayList<>();
        head.add(List.of("会议名称"));
        head.add(List.of("编号"));
        head.add(List.of("起止时间"));
        head.add(List.of("发布状态"));
        head.add(List.of("发布人"));
        head.add(List.of("发布时间"));
        head.add(List.of("归属部门"));
        head.add(List.of("创建时间"));
        return head;
    }

    @Override
    public Result<MaterialsPreviewDTO> getMaterialsPreviewVO(String providerFileId) {
        return null;
    }

    @Override
    public List<MeetingInfoDTO> getMeetingInfoList(Collection<String> qualificationIdList) {
        if (CollectionUtils.isEmpty(qualificationIdList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<Meeting> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(Meeting::getJobQualificationId, qualificationIdList).eq(Meeting::getIsDel, 0);
        List<Meeting> list = baseMapper.selectList(queryWrapper);
        return BeanListUtils.copyList(list, MeetingInfoDTO.class);
    }

    @Override
    public MeetingInfoDTO getMeetingInfoByQualificationId(String qualificationId) {
        MeetingInfoDTO dto = new MeetingInfoDTO();
        Meeting meeting = getOne(new LambdaQueryWrapper<Meeting>().eq(Meeting::getJobQualificationId, qualificationId));
        if (Optional.ofNullable(meeting).isPresent()) {
            BeanUtils.copyProperties(meeting, dto);
        }
        return dto;
    }

}
