package com.wunding.learn.appraise.service.client.dto;

import com.wunding.learn.common.bean.BasePageQuery;
import io.swagger.v3.oas.annotations.Parameter;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.format.annotation.DateTimeFormat.ISO;

/**
 * <AUTHOR>
 * @date 2021/11/3016:28
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AppraiseQuery extends BasePageQuery {

    private static final long serialVersionUID = 609097403678438219L;

    @Parameter(description = "查询类型 1-进行中 2-已结束", required = true)
    Integer queryType;

    @Parameter(description = "标题")
    String title;

    @Parameter(description = "开始时间")
    @DateTimeFormat(iso = ISO.DATE_TIME)
    Date startTime;

    @Parameter(description = "结束时间")
    @DateTimeFormat(iso = ISO.DATE_TIME)
    Date endTime;

    @Parameter(description = "是否缺少必填材料 0-否 1-是")
    Integer isLackMaterials;
}
