package com.wunding.learn.appraise.service.constant;

/**
 * <AUTHOR>
 * @title: AppraiseConstants
 * @projectName devlop-learn
 * @description: 评价相关常量
 * @date 2021/12/109:53
 */
public class AppraiseConstants {

    private AppraiseConstants() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 仅显示大于8分且进入后台排名名次的人
     */
    public final static int APPRAISE_SCORE_EIGHT = 8;

    /**
     * 评价类型-讲师
     */
    public final static int APPRAISE_TYPE_LECTURER = 1;

    /**
     * 评价类型-课程
     */
    public final static int APPRAISE_TYPE_COURSE = 2;

    /**
     * 评价类型-其它
     */
    public final static int APPRAISE_TYPE_OTHER = 3;

    /**
     * 评价类型-其它
     */
    public final static int APPRAISE_TYPE_MEETING = 4;

    /**
     * 被评单项类型 题型-打分
     */
    public final static int TEMPLATE_TYPE_SCORE = 1;

    /**
     * 被评单项类型 题型-问答
     */
    public final static int TEMPLATE_TYPE_QUESTIONS = 2;

    /**
     * 是否评价完成 ---0否 1-是
     */
    public final static int APPRAISE_STATUS_FINISH = 1;

    /**
     * 是否评价完成 ---0否 1-是
     */
    public final static int APPRAISE_STATUS_NO_FINISH = 0;

    /**
     * 预览材料必填 -是
     */
    public final static int APPRAISE_MATERIALS_REQUIRED_YES = 1;

    /**
     * 预览材料必填 -否
     */
    public final static int APPRAISE_MATERIALS_REQUIRED_NO = 0;

    /**
     * 是否待认证课件 -是
     */
    public final static int APPRAISE_ISSAVELIB_YES = 1;

    /**
     * 是否待认证课件 -否
     */
    public final static int APPRAISE_ISSAVELIB_NO = 0;

    /**
     * 评价分数计算小数位数
     */
    public final static int APPRAISE_SCORE_PRECISION = 1;

    /**
     * 评委默认权重
     */
    public final static int APPRAISE_DEFAULT_WEIGHT = 100;

    /**
     * APP端查看明细 1-结束后允许
     */
    public final static int APPRAISE_VIEWTYPE_WEIGHT = 1;

    /**
     * APP端查看明细 1-结束后允许
     */
    public final static int APPRAISE_VIEWTYPE_REFULSE_AFTER_TIME = 1;

    /**
     * APP端查看明细  2-不允许
     */
    public final static int APPRAISE_VIEWTYPE_NOT_REFULSE = 2;

    /**
     * APP端查看明细  3-匿名允许
     */
    public final static int APPRAISE_VIEWTYPE_ALLOW = 3;

    /**
     * 被评人评价状态-未完成
     */
    public final static int APPRAISE_PROVIDER_NO_FINISH = 0;

    /**
     * 被评人评价状态-已完成
     */
    public final static int APPRAISE_PROVIDER_FINISH = 1;

    /**
     * 被评人评价状态-进行中
     */
    public final static int APPRAISE_PROVIDER_UNDERWAY = 2;

    /**
     * 是否匿名 -是
     */
    public final static int APPRAISE_ISANONYMOUS_YES = 1;

    /**
     * 是否匿名  -否
     */
    public final static int APPRAISE_ISANONYMOUS_NO = 0;

    public static final String ERROR_EXISTS_PROVIDER_REFEREE = "添加失败，该人员不能同时作为被评人和评委！";

    public static final  String ERROR_EXISTS_REFEREE = "添加失败，该人员已在评委列表！";

    public static final  String ERROR_EXISTS_PROVIDER = "添加失败，该人员已在被评人列表！";

}
