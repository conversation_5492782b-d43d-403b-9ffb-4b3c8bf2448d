package com.wunding.learn.appraise.service.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p>
 *
 * <AUTHOR> href="mailto:"></a>
 * @since 2024-01-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("meeting_referee_provider_score")
@Schema(name = "MeetingRefereeProviderScore对象", description = "评委与答辩人员分数汇总表")
public class MeetingRefereeProviderScore implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    @Schema(description = "主键")
    @TableId(value ="id",type = IdType.INPUT)
    private String id;


    /**
     * 被评人id
     */
    @Schema(description = "被评人id")
    @TableField("provider_id")
    private String providerId;


    /**
     * 评价人id
     */
    @Schema(description = "评价人id")
    @TableField("referee_id")
    private String refereeId;


    /**
     * 评价表id
     */
    @Schema(description = "评价表id")
    @TableField("meeting_id")
    private String appraiseId;


    /**
     * 评分 分 实际分数*100
     */
    @Schema(description = "评分 分 实际分数*100")
    @TableField("score")
    private Integer score;

    /**
     * 评分 分 实际分数*100
     */
    @Schema(description = "意见，是否通过 0-未评 1-通过 2-未通过")
    @TableField("is_pass")
    private Integer isPass;

    /**
     * 评分 分 实际分数*100
     */
    @Schema(description = "说明")
    @TableField("description")
    private String description;


    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;


    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 修改人
     */
    @Schema(description = "修改人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;


    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


}
