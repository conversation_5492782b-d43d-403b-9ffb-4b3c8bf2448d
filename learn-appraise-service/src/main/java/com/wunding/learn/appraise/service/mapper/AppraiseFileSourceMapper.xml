<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.appraise.service.mapper.AppraiseFileSourceMapper">

        <!-- 开启二级缓存 -->
        <!--
    <cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
    -->

        <!-- 使用缓存 -->
        <cache-ref namespace="com.wunding.learn.appraise.service.mapper.AppraiseFileSourceMapper"/>

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.wunding.learn.appraise.service.model.AppraiseFileSource">
            <!--@Table appraise_file_source-->
                    <id column="id" jdbcType="VARCHAR" property="id"/>
                    <result column="file_type_id" jdbcType="VARCHAR"
                            property="fileTypeId"/>
                    <result column="mime" jdbcType="VARCHAR"
                            property="mime"/>
                    <result column="file_type" jdbcType="VARCHAR"
                            property="fileType"/>
                    <result column="title" jdbcType="VARCHAR"
                            property="title"/>
                    <result column="file_name" jdbcType="VARCHAR"
                            property="fileName"/>
                    <result column="upload_time" jdbcType="TIMESTAMP"
                            property="uploadTime"/>
                    <result column="is_del" jdbcType="INTEGER"
                            property="isDel"/>
                    <result column="create_by" jdbcType="VARCHAR"
                            property="createBy"/>
                    <result column="create_time" jdbcType="TIMESTAMP"
                            property="createTime"/>
                    <result column="update_by" jdbcType="VARCHAR"
                            property="updateBy"/>
                    <result column="update_time" jdbcType="TIMESTAMP"
                            property="updateTime"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, file_type_id, mime, file_type, title, file_name, upload_time, is_del, create_by, create_time, update_by, update_time
        </sql>

</mapper>
