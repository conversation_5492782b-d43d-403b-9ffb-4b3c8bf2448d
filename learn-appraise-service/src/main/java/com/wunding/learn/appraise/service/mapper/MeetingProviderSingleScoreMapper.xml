<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.appraise.service.mapper.MeetingProviderSingleScoreMapper">
    <!-- 开启二级缓存 -->
    <!--
<cache type="com.wunding.learn.common.mybatis.cache.MyBatisPlusRedisCache"/>
-->

    <!-- 使用缓存 -->
    <cache-ref namespace="com.wunding.learn.appraise.service.mapper.MeetingProviderSingleScoreMapper"/>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wunding.learn.appraise.service.model.MeetingProviderSingleScore">
        <!--@Table meeting_provider_single_score-->
        <result column="id" jdbcType="VARCHAR"
          property="id"/>
        <result column="provider_id" jdbcType="VARCHAR"
          property="providerId"/>
        <result column="template_id" jdbcType="VARCHAR"
          property="templateId"/>
        <result column="meeting_id" jdbcType="VARCHAR"
          property="appraiseId"/>
        <result column="score" jdbcType="INTEGER"
          property="score"/>
        <result column="referee_num" jdbcType="INTEGER"
          property="refereeNum"/>
        <result column="create_by" jdbcType="VARCHAR"
          property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP"
          property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR"
          property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP"
          property="updateTime"/>
    </resultMap>

    <resultMap id="DetailResultMap" type="com.wunding.learn.appraise.service.admin.dto.MeetingDetailDTO">
        <result column="providerId" property="providerId" jdbcType="VARCHAR"/>
        <result column="userId" property="userId" jdbcType="VARCHAR"/>
        <result column="loginName" property="loginName" jdbcType="VARCHAR"/>
        <result column="levelPathName" property="levelPathName" jdbcType="VARCHAR"/>
        <result column="providerName" property="providerName" jdbcType="DECIMAL"/>
        <result column="totalScore" property="totalScore" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="isPass" property="isPass" jdbcType="VARCHAR"/>
        <result column="noPassCount" property="noPassCount" jdbcType="VARCHAR"/>
        <result column="avgscore" property="avgScore" jdbcType="VARCHAR"/>
        <result column="sortNo" property="sortNo" jdbcType="VARCHAR"/>
        <result column="passCount" property="passCount" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , provider_id, template_id, meeting_id, score, referee_num, create_by, create_time, update_by, update_time
    </sql>

    <!--嵌套子查询-待优化-->
    <select id="queryPage" resultMap="DetailResultMap" useCache="false">
        select * from (
        select tap.id                                                       as providerId
             , tap.user_id                                                  as userId
             , tap.user_name                                                as providerName
             , tap.login_name                                               as loginName
             , tap.level_path_name                                          as levelPathName
             , tap.score                                                    as totalScore
             , tap.status                                                   as status
             , if(tap.create_time = tap.update_time, null, tap.update_time) as updateTime
             , (select count(1)
                from meeting_referee_provider_score trps
                where trps.provider_id = tap.id
                  and trps.meeting_id = tap.meeting_id)                     as curRefereeNum
             , (select count(1)
                from meeting_referee_provider_score trps
                where trps.provider_id = tap.id
                  and trps.meeting_id = tap.meeting_id
                  and trps.is_pass = 1)                                     as passCount
             , (select count(1)
                from meeting_referee_provider_score trps
                where trps.provider_id = tap.id
                  and trps.meeting_id = tap.meeting_id
                  and trps.is_pass = 2)                                     as noPassCount
             , (select round(avg(score) / 100,1)
                from meeting_referee_provider_score trps
                where trps.provider_id = tap.id
                  and trps.meeting_id = tap.meeting_id)                        avgscore
             , tap.meeting_id                                               as appraiseId
             , tap.is_pass                                                  as isPass
             , tap.sort_no                                                  as sortNo
        from meeting_provider tap
        where tap.meeting_id = #{params.appraiseId}
          and tap.is_del = 0
        <if test="params.providerName != null and params.providerName != ''">
            and instr(tap.user_name, #{params.providerName}) > 0
        </if>
        <if test="params.totalScore != null">
            and tap.score &gt;= #{params.totalScore} * 100
        </if>
        <if test="params.status != null">
            and tap.status = #{params.status}
        </if>
        order by tap.sort_no, tap.create_time ) temp
    </select>

    <resultMap id="ShowDetailResultMap" type="com.wunding.learn.appraise.service.admin.dto.MeetingShowDetailDTO">
        <result column="referee_id" property="refereeId" jdbcType="VARCHAR"/>
        <result column="referee_name" property="refereeName" jdbcType="DECIMAL"/>
        <result column="update_time" property="updateTime" jdbcType="VARCHAR"/>
        <result column="total_score" property="totalScore" jdbcType="VARCHAR"/>
        <result column="provider_id" property="providerId" jdbcType="VARCHAR"/>
        <result column="appraise_id" property="appraiseId" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="queryTemplated"
      type="com.wunding.learn.appraise.service.admin.dto.AppraiseDetailDTO$TemplateCollect">
        <result column="template_id" property="templateId" jdbcType="VARCHAR"/>
        <result column="score" property="score" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="queryTemplate" parameterType="map" resultMap="queryTemplated" useCache="false">
        select tas.template_id, tas.score, tas.remark, tat.type
        from meeting_score tas
                 inner join meeting_template tat on tat.id = tas.template_id
        where tas.is_del = 0
          and tas.referee_id = #{refereeId}
          and tas.meeting_id = #{appraiseId}
          and tas.provider_id = #{providerId}
    </select>

    <select id="queryShowPage" resultMap="ShowDetailResultMap" useCache="false">
        select distinct tas.referee_id   as referee_id,
                        tar.user_name    as referee_name,
                        trps.create_time as update_time,
                        trps.score       as total_score,
                        tar.create_time,
                        tap.meeting_id   as appraise_id,
                        tap.id           as provider_id
        from meeting_provider tap
                 left join meeting_score tas on tas.provider_id = tap.id
                 left join meeting_provider_single_score tpss on tpss.provider_id = tap.id
                 left join meeting_referee tar on tar.id = tas.referee_id
                 left join meeting_referee_provider_score trps on trps.provider_id = tap.id and trps.referee_id = tar.id
                 left join meeting_template tat on tat.id = tas.template_id
        where tap.id = #{params.providerId}
          and tap.meeting_id = #{params.appraiseId}
          and tap.is_del = 0
          and tar.is_del = 0
          and tat.is_del = 0
          and trps.id != ''
        order by tar.create_time
    </select>

    <select id="queryHistoryPage" resultType="com.wunding.learn.appraise.service.admin.dto.AppraiseHistoryDetailDTO"
      useCache="false">
        select
        trps.id as id,
        tar.user_name as referee_name,
        tar.login_name as login_Name,
        tar.weight,
        g.level_path_name as level_path_name,
        trps.score as weighted_score,
        trps.create_time
        from
        meeting_referee_provider_score trps
        left join meeting_referee tar on tar.id = trps.referee_id and tar.is_del =0
        left join sys_org g on g.id=tar.org_id
        where trps.provider_id = #{params.providerId} and trps.meeting_id=#{params.appraiseId} and tar.is_del = 0
        <if test="params.refereeIdsVo != null and params.refereeIdsVo.size() > 0">
            <foreach collection="params.refereeIdsVo" item="item" open="and tar.user_id in (" close=")"
              separator=",">
                #{item}
            </foreach>
        </if>
        order by trps.create_time desc
    </select>
</mapper>
