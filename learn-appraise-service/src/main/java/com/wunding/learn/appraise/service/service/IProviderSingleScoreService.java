package com.wunding.learn.appraise.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wunding.learn.appraise.service.admin.dto.AppraiseDetailDTO;
import com.wunding.learn.appraise.service.admin.dto.AppraiseHistoryDetailDTO;
import com.wunding.learn.appraise.service.admin.dto.AppraiseShowDetailDTO;
import com.wunding.learn.appraise.service.model.ProviderSingleScore;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p> 被评价人单项汇总表 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">xiao8</a>
 * @since 2022-08-09
 */
public interface IProviderSingleScoreService extends IService<ProviderSingleScore> {

    /**
     * 被评人单项汇总分页
     *
     * @param appraiseDetailVo 搜索条件
     * @return
     */
    List<AppraiseDetailDTO> queryPage(@Param("params") AppraiseDetailDTO appraiseDetailVo);

    /**
     * 查看打分详情
     *
     * @param appraiseShowDetailVo 搜索条件
     * @return
     */
    List<AppraiseShowDetailDTO> queryShowPage(AppraiseShowDetailDTO appraiseShowDetailVo);

    /**
     * 历史打分明细查询记录
     *
     * @param appraiseHistoryDetailVo
     * @return
     */
    List<AppraiseHistoryDetailDTO> queryHistoryPage(AppraiseHistoryDetailDTO appraiseHistoryDetailVo);

    /**
     * 被评人单项总分求和
     *
     * @param appraiseId 评价id
     * @param providerId 被评人id
     */
    void sumProviderSingleScore(String appraiseId, String providerId);

}
