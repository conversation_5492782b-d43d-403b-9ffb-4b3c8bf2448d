package com.wunding.learn.appraise.service.client.dto;

import com.wunding.learn.appraise.api.dto.AppraiseTemplateDTO;
import com.wunding.learn.appraise.api.dto.CommentCategory;
import com.wunding.learn.appraise.api.dto.RefereeFilesDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @title: AppraiseRefereeVO
 * @projectName devlop-learn
 * @description: 评委评价明细
 * @date 2021/12/4 11:25
 */
@Data
@Schema
public class AppraiseRefereeDetailDTO {

    @Schema(description = "评委查看时，这个是评委的评分；被评人查看时，这是被评人获得总分")
    private String score;

    @Schema(description = "用户上传文件")
    private List<RefereeFilesDTO> refereeFiles;

    @Schema(description = "分值分类条目项")
    private List<AppraiseTemplateDTO> appraiseTemplates;

    @Schema(description = "打分内容,分值明细")
    private List<RefereeScoreListVO> refereeScoreListVOS;

    @Schema(description = "评价")
    private List<CommentCategory> commentCategories;

    public ScoreDetail createScoreDetail() {
        return new ScoreDetail();
    }

    public RefereeScoreListVO createRefereeScoreListVO() {
        return new RefereeScoreListVO();
    }

    @Data
    @Schema
    public class ScoreDetail {

        @Schema(description = "模板id")
        private String templateId;

        @Schema(description = "分值")
        private String score;

    }

    @Data
    @Schema
    public class RefereeScoreListVO {

        @Schema(description = "评委id")
        private String refereeId;

        /**
         * 当老师选择匿名时，此处显示评委+数字
         */
        @Schema(description = "评委名字")
        private String refereeName;

        @Schema(description = "评委打分内容")
        private List<ScoreDetail> scoreDetails;
    }

}
