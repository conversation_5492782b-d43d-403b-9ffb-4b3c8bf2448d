package com.wunding.learn.appraise.service.client.rest;


import com.wunding.learn.appraise.service.client.dto.AppraiseMaterialsDTO;
import com.wunding.learn.appraise.service.client.dto.AppraiseRefereeDetailDTO;
import com.wunding.learn.appraise.service.client.dto.MyMaterialsDTO;
import com.wunding.learn.appraise.service.service.IAppraiseService;
import com.wunding.learn.common.bean.Result;
import com.wunding.learn.common.util.json.JsonUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @title: AppraiseProviderController
 * @projectName devlop-learn
 * @description: 被评人相关操作入口
 * @date 2021/12/515:15
 */
@Tag(description = "评价模块》被评人相关操作接口", name = "clientAppraiseProviderRest")
@RequestMapping("${module.appraise.contentPath:/}provider")
@RestController("clientAppraiseProviderRest")
@Slf4j
public class AppraiseProviderRest {

    @Resource
    private IAppraiseService appraiseService;

    /**
     * 被评人查看本人材料
     *
     * @return
     */
    @Operation(summary = "被评人查看本人材料", description = "被评人查看本人材料", security = @SecurityRequirement(name = "apiKey"))
    @GetMapping("/getMyMaterials")
    public Result<MyMaterialsDTO> getMyMaterials(
        @Parameter(description = "评价id", required = true) String appraiseId) {
        return appraiseService.getMyMaterials(appraiseId);
    }

    /**
     * 被评人查询评价明细
     *
     * @return
     */
    @Operation(summary = "被评人查询评价明细", description = "被评人查询评价明细", security = @SecurityRequirement(name = "apiKey"))
    @GetMapping("/getAppraiseRefereeDetail")
    public Result<AppraiseRefereeDetailDTO> getAppraiseRefereeDetail(
        @Parameter(description = "评价id", required = true) String appraiseId) {
        return appraiseService.getAppraiseRefereeDetail(appraiseId);
    }

    /**
     * 被评人上传材料
     *
     * @return
     */
    @Operation(summary = "被评人上传材料", description = "被评人上传材料", security = @SecurityRequirement(name = "apiKey"))
    @PostMapping("/submitMaterials")
    public Result<Void> submitMaterials(
        @Parameter(name = "appraiseMaterialsAO", description = "请求参数", required = true) @RequestBody
            AppraiseMaterialsDTO appraiseMaterials) {
        log.info("submitMaterials appraiseMaterialsAO:{}", JsonUtil.objToJson(appraiseMaterials));
        return appraiseService.submitMaterials(appraiseMaterials);
    }

}
