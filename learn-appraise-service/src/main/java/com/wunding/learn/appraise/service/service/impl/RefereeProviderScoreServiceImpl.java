package com.wunding.learn.appraise.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.appraise.service.constant.AppraiseConstants;
import com.wunding.learn.appraise.service.dao.RefereeProviderScoreDao;
import com.wunding.learn.appraise.service.mapper.AppraiseMapper;
import com.wunding.learn.appraise.service.mapper.AppraiseProviderMapper;
import com.wunding.learn.appraise.service.mapper.AppraiseRefereeMapper;
import com.wunding.learn.appraise.service.mapper.AppraiseScoreMapper;
import com.wunding.learn.appraise.service.mapper.RefereeProviderScoreMapper;
import com.wunding.learn.appraise.service.model.Appraise;
import com.wunding.learn.appraise.service.model.AppraiseProvider;
import com.wunding.learn.appraise.service.model.AppraiseReferee;
import com.wunding.learn.appraise.service.model.AppraiseScore;
import com.wunding.learn.appraise.service.model.RefereeProviderScore;
import com.wunding.learn.appraise.service.service.IProviderSingleScoreService;
import com.wunding.learn.appraise.service.service.IRefereeProviderScoreService;
import java.util.Optional;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p> 评价人与被评人分数汇总表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">xiao8</a>
 * @since 2022-08-09
 */
@Slf4j
@Service("refereeProviderScoreService")
public class RefereeProviderScoreServiceImpl extends
    ServiceImpl<RefereeProviderScoreMapper, RefereeProviderScore> implements
    IRefereeProviderScoreService {

    @Resource
    private AppraiseScoreMapper appraiseScoreMapper;

    @Resource
    private AppraiseProviderMapper appraiseProviderMapper;

    @Resource
    private AppraiseRefereeMapper appraiseRefereeMapper;

    @Resource
    private IProviderSingleScoreService providerSingleScoreService;

    @Resource
    private AppraiseMapper appraiseMapper;
    @Resource(name = "refereeProviderScoreDao")
    private RefereeProviderScoreDao refereeProviderScoreDao;
    @Override
    public int sumRefereeProviderScore(String providerId, String appraiseId) {
        return baseMapper.sumRefereeProviderScore(providerId, appraiseId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteRefereeProviderScore(String id) {

        RefereeProviderScore refereeProviderScore = getBaseMapper().selectById(id);
        if (null == refereeProviderScore) {
            return;
        }

        //删除 评分明细
        LambdaQueryWrapper<AppraiseScore> scoreLambdaQueryWrapper = new LambdaQueryWrapper<>();
        scoreLambdaQueryWrapper.eq(AppraiseScore::getProviderId,
            refereeProviderScore.getProviderId());
        scoreLambdaQueryWrapper.eq(AppraiseScore::getAppraiseId,
            refereeProviderScore.getAppraiseId());
        scoreLambdaQueryWrapper.eq(AppraiseScore::getRefereeId,
            refereeProviderScore.getRefereeId());
        appraiseScoreMapper.delete(scoreLambdaQueryWrapper);

        // 删除 评价人与被评人分数汇总表
        Appraise byId = appraiseMapper.selectById(refereeProviderScore.getAppraiseId());
        String title = Optional.ofNullable(byId).isPresent() ? byId.getTitle() : "";
        String msg = String.format("【删除评分记录】%s", title);
        refereeProviderScoreDao.deleteRefereeProviderScore(refereeProviderScore, msg);

        //重新计算单项汇总表
        providerSingleScoreService
            .sumProviderSingleScore(refereeProviderScore.getAppraiseId(),
                refereeProviderScore.getProviderId());

        //已评价记录数
        int countAppraiseReferee = appraiseScoreMapper
            .countAppraiseReferee(refereeProviderScore.getProviderId(),
                refereeProviderScore.getAppraiseId());
        LambdaQueryWrapper<AppraiseReferee> appraiseRefereeLambdaQueryWrapper = new LambdaQueryWrapper<>();
        appraiseRefereeLambdaQueryWrapper.eq(AppraiseReferee::getAppraiseId,
            refereeProviderScore.getAppraiseId());
        //评委人数
        long countReferee = appraiseRefereeMapper.selectCount(appraiseRefereeLambdaQueryWrapper);

        //更新被评人状态
        int status;
        if (countAppraiseReferee == 0) {
            //已评人数为0
            status = AppraiseConstants.APPRAISE_PROVIDER_NO_FINISH;
        } else if (countAppraiseReferee >= countReferee) {
            //全部评委评价完成
            status = AppraiseConstants.APPRAISE_PROVIDER_FINISH;
        } else {
            //进行中
            status = AppraiseConstants.APPRAISE_PROVIDER_UNDERWAY;
        }

        //重新计算学员总分
        AppraiseProvider appraiseProvider = new AppraiseProvider();
        appraiseProvider.setId(refereeProviderScore.getProviderId());
        appraiseProvider.setStatus(status);
        appraiseProvider.setScore(
            this.sumRefereeProviderScore(refereeProviderScore.getProviderId(),
                refereeProviderScore.getAppraiseId()));
        appraiseProviderMapper.updateById(appraiseProvider);
    }
}
