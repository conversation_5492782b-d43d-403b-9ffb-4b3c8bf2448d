package com.wunding.learn.appraise.service.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p> 评价人（评委）
 *
 * <AUTHOR> href="mailto:<EMAIL>">xiao8</a>
 * @since 2022-08-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("appraise_referee")
@Schema(name = "AppraiseReferee", description = "评价人（评委）")
public class AppraiseReferee implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    @Schema(description = "主键")
    @TableId(value = "id", type = IdType.INPUT)
    private String id;


    /**
     * 评价id
     */
    @Schema(description = "评价id")
    @TableField("appraise_id")
    private String appraiseId;


    /**
     * 用户id
     */
    @Schema(description = "用户id")
    @TableField("user_id")
    private String userId;

    /**
     * 用户姓名
     */
    @Schema(description = "用户姓名")
    @TableField("user_name")
    private String userName;

    /**
     * 用户账号
     */
    @Schema(description = "用户账号")
    @TableField("login_name")
    private String loginName;

    /**
     * 用户组织id
     */
    @Schema(description = "用户组织id")
    @TableField("org_id")
    private String orgId;

    /**
     * 组织层级
     */
    @Schema(description = "组织层级")
    @TableField("level_path")
    private String levelPath;

    /**
     * 组织层级名
     */
    @Schema(description = "组织层级名")
    @TableField("level_path_name")
    private String levelPathName;


    /**
     * 权重 百分制
     */
    @Schema(description = "权重 百分制")
    @TableField("weight")
    private Integer weight;


    /**
     * 是否匿名(0:否,1:是)
     */
    @Schema(description = "是否匿名(0:否,1:是)")
    @TableField("is_anonymous")
    private Integer isAnonymous;


    /**
     * 是否删除(1:已删除,0未删除)
     */
    @Schema(description = "是否删除(1:已删除,0未删除)")
    @TableField("is_del")
    @TableLogic(value = "0", delval = "1")
    private Integer isDel;


    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;


    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 修改人
     */
    @Schema(description = "修改人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;


    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


}
