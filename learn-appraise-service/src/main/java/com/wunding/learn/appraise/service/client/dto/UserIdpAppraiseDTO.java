package com.wunding.learn.appraise.service.client.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

/**
 * @Author: aixinrong
 * @Date: 2022/10/19 14:43
 */
@Data
@Schema(name = "UserIdpAppraiseDTO", description = "用户idp评价对象")
public class UserIdpAppraiseDTO {

    @Schema(description = "用户岗位名")
    private String postName;

    @Schema(description = "按月份分类评价")
    private List<MonthIdpAppraiseDTO> monthIdpList;
}
