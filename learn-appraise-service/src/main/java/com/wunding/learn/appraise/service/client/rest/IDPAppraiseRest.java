package com.wunding.learn.appraise.service.client.rest;

import com.wunding.learn.appraise.service.client.dto.UserIdpAppraiseDTO;
import com.wunding.learn.appraise.service.client.query.UserIdpAppraiseQuery;
import com.wunding.learn.appraise.service.service.IAppraiseService;
import com.wunding.learn.common.bean.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: GYong
 * @Date: 2022/9/29 20:38
 */
@RestController
@RequestMapping("${module.appraise.contentPath:/}idp")
@Tag(description = "评价-IDP", name = "IDPAppraiseRest")
@Validated
public class IDPAppraiseRest {

    @Resource
    private IAppraiseService appraiseService;

    @GetMapping("/mineAppraise")
    @Operation(operationId = "getUserIdpAppraiseByYear", summary = "获取用户年度idp评价列表", description = "获取用户年度idp评价列表")
    public Result<UserIdpAppraiseDTO> getUserIdpAppraiseByYear(
        @Valid @ParameterObject UserIdpAppraiseQuery userIdpAppraiseQuery) {
        return Result.success(appraiseService.getUserIdpAppraiseByYear(userIdpAppraiseQuery));
    }
}
