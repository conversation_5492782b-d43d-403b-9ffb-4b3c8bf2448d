package com.wunding.learn.appraise.service.client.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @title: AppraiseScoreAO
 * @projectName devlop-learn
 * @description: TODO
 * @date 2021/12/412:46
 */
@Data
public class MeetingRefereeProviderScoreDetailListDTO {

    private static final long serialVersionUID = -6319526235025120020L;


    /**
     * 主键
     */
    @Schema(description = "主键")
    private String id;


    /**
     * 评价人id
     */
    @Schema(description = "评价人id")
    private String refereeId;

    /**
     * 评价人id
     */
    @Schema(description = "评委")
    private String refereeName;

    /**
     * 评价人id
     */
    @Schema(description = "账户")
    private String loginName;

    /**
     * 评分 分 实际分数*100
     */
    @Schema(description = "评分 分 实际分数*100", hidden = true)
    private Integer primitiveScore;

    @Schema(description = "评分")
    private BigDecimal score;

    /**
     * 评分 分 实际分数*100
     */
    @Schema(description = "状态 意见，是否通过 0-未评 1-通过 2-未通过")
    private Integer isPass;

    /**
     * 评分 分 实际分数*100
     */
    @Schema(description = "描述")
    private String description;

    /**
     * 修改时间
     */
    @Schema(description = "修改人")
    private String updateBy;

    /**
     * 修改时间
     */
    @Schema(description = "修改人名称")
    private String updateByName;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    private Date updateTime;


}
