<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.appraise.service.mapper.AppraiseProviderFileMapper">
    <!-- 开启二级缓存 -->
    <!--
<cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
-->

    <!-- 使用缓存 -->
    <cache-ref namespace="com.wunding.learn.appraise.service.mapper.AppraiseProviderFileMapper"/>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wunding.learn.appraise.service.model.AppraiseProviderFile">
        <!--@Table appraise_provider_file-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="appraise_id" jdbcType="VARCHAR"
          property="appraiseId"/>
        <result column="file_type_id" jdbcType="VARCHAR"
          property="fileTypeId"/>
        <result column="provider_id" jdbcType="VARCHAR"
          property="providerId"/>
        <result column="play_time" jdbcType="INTEGER"
          property="playTime"/>
        <result column="mime" jdbcType="VARCHAR"
          property="mime"/>
        <result column="is_enter_storage" jdbcType="INTEGER"
          property="isEnterStorage"/>
        <result column="transform_status" jdbcType="INTEGER"
          property="transformStatus"/>
        <result column="cw_type" jdbcType="VARCHAR"
          property="cwType"/>
        <result column="sort" jdbcType="INTEGER"
          property="sort"/>
        <result column="create_by" jdbcType="VARCHAR"
          property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP"
          property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR"
          property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP"
          property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , appraise_id, file_type_id, provider_id, play_time, mime, is_enter_storage, transform_status, cw_type, sort, create_by, create_time, update_by, update_time
    </sql>

    <select id="queryPage" resultType="com.wunding.learn.appraise.service.admin.dto.AppraiseProviderFileDTO" useCache="false">
        select
        tap.id as provider_id,
        tap.user_name fullName,
        g.id as org_id,
        g.level_path_name levelPathName,
        taft.title,
        tapf.create_time adddate,
        tap.status,
        tap.score,
        taft.is_save_lib,
        tapf.id
        from appraise_provider_file tapf
        left join appraise ta on ta.id = tapf.appraise_id
        left join appraise_provider tap on tap.appraise_id = ta.id and tapf.provider_id = tap.id
        left join appraise_file_type taft on taft.id = tapf.file_type_id
        left join sys_org g on g.id = tap.org_id
        <where>
            and ta.id=#{params.appraiseId}
            and taft.is_del = 0
            and tap.is_del=0
            and ta.is_del=0
            <if test="params.userIds != null and params.userIds.size()>0">
                and tap.user_id in
                <foreach collection="params.userIds" item="userId" open="(" close=")" separator=",">
                    #{userId}
                </foreach>
            </if>
            <if test="params.status != null">
                and tap.status = #{params.status}
            </if>
            <if test="params.isSaveLib != null">
                and taft.is_save_lib = #{params.isSaveLib}
            </if>
            <if test="params.createBeginTime != null">
                and date_format(tapf.create_time, '%Y-%m-%d') >= date_format(#{params.createBeginTime},'%Y-%m-%d')
            </if>
            <if test="params.createEndTime != null">
                and date_format(#{params.createEndTime}, '%Y-%m-%d') >= date_format(tapf.create_time,'%Y-%m-%d')
            </if>
            <if test="params.title != null and  '' != params.title">
                and instr(taft.title,#{params.title}) > 0
            </if>
            <if test="params.score != null ">
                and tap.score >= #{params.score}*100
            </if>
        </where>
    </select>

    <select id="getAppraiseProviderFileById"
      resultType="com.wunding.learn.appraise.service.admin.dto.AppraiseProviderFileDTO">
        select tapf.id , tapf.cw_type cwType,
               tapf.mime
        from appraise_provider_file tapf
        where tapf.id = #{id}
    </select>

    <select id="countAppraiseProviderFile" resultType="java.lang.Integer">
        select count(1) from appraise_provider_file
        <where>
            <if test="params.appraiseId != null and params.appraiseId.trim() != ''">
                and appraise_id = #{params.appraiseId}
            </if>
            <if test="params.fileTypeId != null and params.fileTypeId.trim() != ''">
                and file_type_id = #{params.fileTypeId}
            </if>
        </where>
    </select>

    <update id="updateAppraiseProviderFileTransformStatus">
        update
            appraise_provider_file
        set transform_status = #{status},
            mime             = 'text/html'
        where id = #{id}
    </update>

    <select id="getEndTime" resultType="java.util.Date">
        select material_end_time
        from appraise
        where id = #{id}
    </select>

    <select id = "getAppraiseId" resultType="java.lang.String">
        select appraise_id
        from appraise_provider_file
        where id = #{id}
    </select>
</mapper>
