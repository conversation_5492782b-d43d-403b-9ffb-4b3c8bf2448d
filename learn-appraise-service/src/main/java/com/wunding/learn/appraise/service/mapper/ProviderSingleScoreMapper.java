package com.wunding.learn.appraise.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.appraise.service.admin.dto.AppraiseDetailDTO;
import com.wunding.learn.appraise.service.admin.dto.AppraiseHistoryDetailDTO;
import com.wunding.learn.appraise.service.admin.dto.AppraiseShowDetailDTO;
import com.wunding.learn.appraise.service.model.ProviderSingleScore;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import java.util.List;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;

/**
 * <p> 被评价人单项汇总表 Mapper 接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">xiao8</a>
 * @since 2022-08-09
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")
})
public interface ProviderSingleScoreMapper extends BaseMapper<ProviderSingleScore> {

    /**
     * 查看打分详情
     *
     * @param appraiseDetailVo
     * @return
     */
    List<AppraiseDetailDTO> queryPage(@Param("params") AppraiseDetailDTO appraiseDetailVo);

    /**
     * 查看明细
     *
     * @param appraiseShowDetailVo
     * @return
     */
    List<AppraiseShowDetailDTO> queryShowPage(@Param("params") AppraiseShowDetailDTO appraiseShowDetailVo);

    /**
     * 历史打分明细查询记录
     *
     * @param appraiseHistoryDetailVo
     * @return
     */
    List<AppraiseHistoryDetailDTO> queryHistoryPage(@Param("params") AppraiseHistoryDetailDTO appraiseHistoryDetailVo);
}
