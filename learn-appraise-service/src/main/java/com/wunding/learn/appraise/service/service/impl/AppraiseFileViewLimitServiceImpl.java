package com.wunding.learn.appraise.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.appraise.service.mapper.AppraiseFileViewLimitMapper;
import com.wunding.learn.appraise.service.model.AppraiseFileViewLimit;
import com.wunding.learn.appraise.service.service.IAppraiseFileViewLimitService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p> 下发范围表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">xiao8</a>
 * @since 2022-08-08
 */
@Slf4j
@Service("appraiseFileViewLimitService")
public class AppraiseFileViewLimitServiceImpl extends
    ServiceImpl<AppraiseFileViewLimitMapper, AppraiseFileViewLimit> implements IAppraiseFileViewLimitService {

}
