package com.wunding.learn.appraise.service.client.rest;


import com.github.pagehelper.PageInfo;
import com.wunding.learn.appraise.service.client.dto.AppraiseRefereeDetailDTO;
import com.wunding.learn.appraise.service.client.dto.MeetingScoreListDTO;
import com.wunding.learn.appraise.service.client.dto.ParticipatorDTO;
import com.wunding.learn.appraise.service.service.IMeetingService;
import com.wunding.learn.common.bean.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @title: AppraiseRefereeController
 * @projectName devlop-learn
 * @description: 评委相关操作入口
 * @date 2021/12/515:15
 */
@Tag(description = "答辩会议模块》评委相关操作接口", name = "clientMeetingRefereeRest")
@RequestMapping("${module.appraise.contentPath:/}meeting/referee")
@RestController("clientMeetingRefereeRest")
public class MeetingRefereeRest {

    @Resource
    private IMeetingService meetingService;

    /**
     * 分页查询答辩人员列表
     *
     * @return
     */
    @Operation(summary = "评委分页查询答辩人员列表", description = "评委分页查询答辩人员列表", security = @SecurityRequirement(name = "apiKey"))
    @GetMapping("/getMeetingProviders")
    public Result<PageInfo<ParticipatorDTO>> getMeetingProviders(@Parameter(required = true) int pageNo,
        @Parameter(required = true) int pageSize,
        @Parameter(description = "答辩会议id", required = true) String appraiseId) {
        return meetingService.getProviders(pageNo, pageSize, appraiseId);
    }

    /**
     * 评委查询答辩会议明细
     */
    @Operation(summary = "评委查询答辩会议明细", description = "评委查询答辩会议明细", security = @SecurityRequirement(name = "apiKey"))
    @GetMapping("/getBeMeetingRefereeDetail")
    public Result<AppraiseRefereeDetailDTO> getBeMeetingRefereeDetail(
        @Parameter(description = "答辩会议id", required = true) String appraiseId,
        @Parameter(description = "被评人id", required = true) String providerId) {
        return meetingService.getBeAppraiseRefereeDetail(appraiseId, providerId);
    }

    /**
     * 评委提交答辩会议
     */
    @Operation(summary = "评委提交答辩会议", description = "评委提交答辩会议", security = @SecurityRequirement(name = "apiKey"))
    @PostMapping("/submitMeetingScore")
    public Result<Void> submitMeetingScore(
        @Parameter(name = "appraiseScoreListAO", description = "请求参数", required = true) @RequestBody MeetingScoreListDTO appraiseScoreListAO) {
        return meetingService.submitAppraiseScore(appraiseScoreListAO);
    }
}
