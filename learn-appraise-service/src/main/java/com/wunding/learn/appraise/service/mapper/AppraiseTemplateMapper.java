package com.wunding.learn.appraise.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.appraise.service.model.AppraiseTemplate;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import java.util.List;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;

/**
 * <p> 评价内容模板 Mapper 接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">xiao8</a>
 * @since 2022-08-08
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")
})
public interface AppraiseTemplateMapper extends BaseMapper<AppraiseTemplate> {

    /**
     * 获取模板所有的分类项
     *
     * @param appraiseId 评价id
     * @param type       模板题目类型
     * @return
     */
    List<String> selectCategoryList(@Param("appraiseId") String appraiseId, @Param("type") Integer type);
}
