package com.wunding.learn.appraise.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.appraise.api.dto.MeetingProviderInfoDTO;
import com.wunding.learn.appraise.service.admin.dto.MeetingProviderDTO;
import com.wunding.learn.appraise.service.model.MeetingProvider;
import com.wunding.learn.common.dto.ViewLimitBeanDTO;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import java.util.List;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;

/**
 * <p>  Mapper 接口
 *
 * <AUTHOR> href="mailto:"></a>
 * @since 2024-01-22
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")})
public interface MeetingProviderMapper extends BaseMapper<MeetingProvider> {

    /**
     * 被评人分页查询
     *
     * @param appraiseProviderVo
     * @return
     */
    List<MeetingProviderDTO> queryPage(@Param("params") MeetingProviderDTO appraiseProviderVo);

    /**
     * 根据关键字查询 评价id
     *
     * @param keyWord
     * @return 评价id 集合
     */
    List<String> selectAppraiseId(String keyWord);

    /**
     * 根据被评人id查询被评人名字
     *
     * @param providerId
     * @return 被评人名字
     */
    String getUserNameById(String providerId);

    /**
     * 根据评价id 查询被评人列表
     *
     * @param appraiseId
     * @return 被评人列表
     */
    List<MeetingProvider> getAppraiseProvider(String appraiseId);

    /**
     * @param id
     * @return
     */
    List<ViewLimitBeanDTO> getProviderAndRefereeByAppraiseId(String id);

    /**
     * @param meetingId
     * @return
     */
    Integer getIsStatusByMeetingId(String meetingId);

    /**
     * 获取
     *
     * @param userId
     * @param qualificationId
     * @return
     */
    List<MeetingProviderInfoDTO> getMeetingProviderInfo(@Param("userId") String userId,
        @Param("qualificationId") String qualificationId);

    /**
     * 获取最大排序
     *
     * @param meetIngId
     * @return
     */
    Integer getMaxSortNoByMeetingId(@Param("meetIngId") String meetIngId);
}
