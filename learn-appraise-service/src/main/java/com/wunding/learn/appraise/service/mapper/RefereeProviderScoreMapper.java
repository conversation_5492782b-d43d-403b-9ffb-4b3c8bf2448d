package com.wunding.learn.appraise.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.appraise.service.model.RefereeProviderScore;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;

/**
 * <p> 评价人与被评人分数汇总表 Mapper 接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">xiao8</a>
 * @since 2022-08-09
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")
})
public interface RefereeProviderScoreMapper extends BaseMapper<RefereeProviderScore> {

    int sumRefereeProviderScore(@Param("providerId") String providerId, @Param("appraiseId") String appraiseId);
}
