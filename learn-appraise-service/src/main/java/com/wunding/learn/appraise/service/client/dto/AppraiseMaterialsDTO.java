package com.wunding.learn.appraise.service.client.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @title: AppraiseMaterialsAO
 * @projectName devlop-learn
 * @description: 被评人上传材料
 * @date 2021/12/215:20
 */
@Data
public class AppraiseMaterialsDTO {

    private static final long serialVersionUID = 5644921237438366604L;

    @Schema(description = "评价id", required = true)
    String appraiseId;

    @Schema(name = "appraiseMaterials", description = "被评人上传材料")
    private List<AppraiseMaterialDTO> appraiseMaterials;

}
