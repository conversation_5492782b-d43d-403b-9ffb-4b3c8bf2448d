package com.wunding.learn.appraise.service.client.query;

import io.swagger.v3.oas.annotations.Parameter;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @Author: GYong
 * @Date: 2022/9/29 15:56
 */
@Data

public class UserIdpAppraiseQuery {

    @Parameter(description = "用户id", hidden = true)
    private String userId;

    @Parameter(description = "年份")
    @NotBlank(message = "年份信息不可为空")
    private String year;

}
