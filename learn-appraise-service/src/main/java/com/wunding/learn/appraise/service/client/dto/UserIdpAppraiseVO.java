package com.wunding.learn.appraise.service.client.dto;

import com.wunding.learn.appraise.service.model.Appraise;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Author: GYong
 * @Date: 2022/9/29 21:05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class UserIdpAppraiseVO extends Appraise {

    @Schema(description = "是否完成")
    private int isFinish;

    @Schema(description = "是否可以直接进入任务: 0不可以, 1可以")
    private int isOperation;
}
