package com.wunding.learn.appraise.service.client.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @title: AppraiseScoreAO
 * @projectName devlop-learn
 * @description: TODO
 * @date 2021/12/412:47
 */
@Data
@Schema(name = "AppraiseScoreDTO", description = "评委评价内容")
public class AppraiseScoreDTO implements Serializable {

    private static final long serialVersionUID = -4038915088270920227L;

    @Schema(description = "模板id", required = true)
    String templateId;

    @Schema(description = "分数 0-10")
    Integer score;

    @Schema(description = "评语")
    String comment;

}
