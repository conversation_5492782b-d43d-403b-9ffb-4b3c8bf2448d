package com.wunding.learn.appraise.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Lists;
import com.wunding.learn.appraise.service.admin.dto.MeetingRefereeDTO;
import com.wunding.learn.appraise.service.admin.dto.MeetingRefereeListQuery;
import com.wunding.learn.appraise.service.admin.dto.SaveMeetingRefereeUserDTO;
import com.wunding.learn.appraise.service.constant.AppraiseConstants;
import com.wunding.learn.appraise.service.mapper.MeetingRefereeMapper;
import com.wunding.learn.appraise.service.model.Meeting;
import com.wunding.learn.appraise.service.model.MeetingProvider;
import com.wunding.learn.appraise.service.model.MeetingReferee;
import com.wunding.learn.appraise.service.service.IMeetingProviderService;
import com.wunding.learn.appraise.service.service.IMeetingRefereeService;
import com.wunding.learn.appraise.service.service.IMeetingService;
import com.wunding.learn.appraise.service.service.IMeetingViewLimitService;
import com.wunding.learn.common.constant.appraise.AppraiseErrorNoEnum;
import com.wunding.learn.common.constant.excel.ExcelTitleBaseCheckUtil;
import com.wunding.learn.common.constant.other.AvailableEnum;
import com.wunding.learn.common.constant.other.CommonConstants;
import com.wunding.learn.common.constant.other.ErrorNoEnum;
import com.wunding.learn.common.constant.tenant.TenantRedisKeyConstant;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.dto.ImportResultDTO;
import com.wunding.learn.common.enums.push.PushNoticeEventEnum;
import com.wunding.learn.common.enums.push.PushType;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.mybatis.service.impl.BaseServiceImpl;
import com.wunding.learn.common.util.bean.SpringUtil;
import com.wunding.learn.common.util.http.WebUtil;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.file.api.component.ExportComponent;
import com.wunding.learn.file.api.constant.ExportBizType;
import com.wunding.learn.file.api.constant.ExportFileNameEnum;
import com.wunding.learn.file.api.dto.AbstractExportDataDTO;
import com.wunding.learn.file.api.dto.IExportDataDTO;
import com.wunding.learn.file.api.dto.ImportDataDTO;
import com.wunding.learn.file.api.service.ImportDataFeign;
import com.wunding.learn.push.api.dto.PushAttributeDTO;
import com.wunding.learn.push.api.dto.PushNoticeSetDTO;
import com.wunding.learn.push.api.dto.PushNoticeSetDTO.EventPushSet;
import com.wunding.learn.push.api.dto.PushNoticeSetDTO.ManualPushSet;
import com.wunding.learn.push.api.dto.PushResourceDTO;
import com.wunding.learn.push.api.dto.SendPushDTO;
import com.wunding.learn.push.api.service.PushFeign;
import com.wunding.learn.user.api.dto.ExpertUserListDTO;
import com.wunding.learn.user.api.dto.OrgShowDTO;
import com.wunding.learn.user.api.dto.UserDTO;
import com.wunding.learn.user.api.dto.viewlimit.ViewLimitMainSaveDTO;
import com.wunding.learn.user.api.dto.viewlimit.ViewLimitProgrammeInfoDTO;
import com.wunding.learn.user.api.service.OrgFeign;
import com.wunding.learn.user.api.service.UserFeign;
import com.wunding.learn.user.api.service.ViewLimitFeign;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeSet;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * <p> 评价人（评委） 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">xiao8</a>
 * @since 2022-08-08
 */
@Slf4j
@Service("meetingRefereeService")
public class MeetingRefereeServiceImpl extends BaseServiceImpl<MeetingRefereeMapper, MeetingReferee> implements
    IMeetingRefereeService {

    @Resource
    @Lazy
    private IMeetingProviderService meetingProviderService;

    @Resource
    @Lazy
    private IMeetingService meetingService;

    @Resource
    private UserFeign userFeign;

    @Resource
    private OrgFeign orgFeign;

    @Resource
    private ImportDataFeign importDataFeign;

    @Resource
    private ExportComponent exportComponent;

    @Resource
    @Lazy
    private IMeetingViewLimitService meetingViewLimitService;

    @Override
    public PageInfo<MeetingRefereeDTO> queryPage(MeetingRefereeListQuery query) {
        MeetingRefereeDTO appraiseRefereeVo = new MeetingRefereeDTO();
        BeanUtils.copyProperties(query, appraiseRefereeVo);
        appraiseRefereeVo.setAppraiseId(query.getMeetingId());
        PageInfo<MeetingRefereeDTO> pageInfo = PageMethod.startPage(query.getPageNo(), query.getPageSize())
            .doSelectPageInfo(() -> baseMapper.queryPage(appraiseRefereeVo));
        List<MeetingRefereeDTO> list = pageInfo.getList();
        if (!CollectionUtils.isEmpty(list)) {
            // 用户id
            Set<String> userIds = pageInfo.getList().stream().map(MeetingRefereeDTO::getUserId).collect(Collectors.toSet());
            //查出专家path
            Map<String, String> expertPathMap = userFeign.getExpertPath(userIds).stream()
                .collect(Collectors.toMap(ExpertUserListDTO::getId,
                    dto -> StringUtils.isEmpty(dto.getExpertPath()) ? "" : dto.getExpertPath()));
            Set<String> orgIdSet = list.stream().map(MeetingRefereeDTO::getOrgId).collect(Collectors.toSet());
            Map<String, OrgShowDTO> orgShowDTOMap = orgFeign.getOrgShowDTO(orgIdSet);
            list.forEach(appraiseRefereeDTO -> {
                Optional.ofNullable(orgShowDTOMap.get(appraiseRefereeDTO.getOrgId())).ifPresent(orgShowDTO -> {
                    appraiseRefereeDTO.setOrgName(orgShowDTO.getOrgShortName());
                    appraiseRefereeDTO.setOrgPath(orgShowDTO.getLevelPathName());
                });
                // 体系路径
                Optional.ofNullable(expertPathMap.get(appraiseRefereeDTO.getUserId()))
                    .ifPresent(expertPath -> {
                        appraiseRefereeDTO.setIsExpert(1);
                        appraiseRefereeDTO.setExpertPath(expertPath);
                    });
            });
        }
        return pageInfo;

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addUser(SaveMeetingRefereeUserDTO saveAppraiseRefereeUserDTO) {

        Map<String, UserDTO> userMapByIds = userFeign.getUserNameMapByIds(saveAppraiseRefereeUserDTO.getUserIds());

        List<MeetingReferee> appraiseReferees = Lists.newArrayList();

        for (String userId : saveAppraiseRefereeUserDTO.getUserIds()) {
            UserDTO user = userMapByIds.get(userId);
            this.filterAppraiseRefereeList(appraiseReferees, saveAppraiseRefereeUserDTO.getMeetingId(), user);
        }

        // 一个都没能添加时提示失败
        if (CollectionUtils.isEmpty(appraiseReferees)) {
            throw new BusinessException(AppraiseErrorNoEnum.ERR_USER_CANNOT_IS_REFEREE);
        }

        saveBatch2(appraiseReferees, CommonConstants.MAX_LIMIT);

        meetingViewLimitService.processViewLimit(saveAppraiseRefereeUserDTO.getMeetingId());
    }

    /**
     * 过滤已经是评委的用户和已经是被评人的用户
     *
     * @param appraiseReferees 规则过滤后的评委集合
     * @param appraiseId       评价id
     */
    private void filterAppraiseRefereeList(List<MeetingReferee> appraiseReferees, String appraiseId, UserDTO user) {
        String userId = user.getId();
        String userName = user.getFullName();
        String orgId = user.getOrgId();
        String levelPath = user.getLevelPath();
        String levelPathName = user.getLevelPathName();
        String loginName = user.getLoginName();

        LambdaQueryWrapper<MeetingReferee> appraiseRefereeLambdaQueryWrapper = new LambdaQueryWrapper<>();
        appraiseRefereeLambdaQueryWrapper.eq(MeetingReferee::getAppraiseId, appraiseId);
        appraiseRefereeLambdaQueryWrapper.eq(MeetingReferee::getUserId, userId);
        long count = count(appraiseRefereeLambdaQueryWrapper);
        if (0 != count) {
            // 已存在在评价人中
            // 招募及评价涉及到的人员保持现有报错不变，但是允许不重复的人员正常添加。
            return;
        }

        LambdaQueryWrapper<MeetingProvider> appraiseProviderLambdaQueryWrapper = new LambdaQueryWrapper<>();
        appraiseProviderLambdaQueryWrapper.eq(MeetingProvider::getAppraiseId, appraiseId);
        appraiseProviderLambdaQueryWrapper.eq(MeetingProvider::getUserId, userId);
        long appraiseProviderCount = meetingProviderService.count(appraiseProviderLambdaQueryWrapper);
        if (0 != appraiseProviderCount) {
            // 已存在在被评人中
            throw new BusinessException(AppraiseErrorNoEnum.ERROR_EXISTS_PROVIDER_REFEREE);
        }

        MeetingReferee appraiseReferee = new MeetingReferee();
        appraiseReferee.setId(StringUtil.newId());
        appraiseReferee.setAppraiseId(appraiseId);
        // 默认权重100
        appraiseReferee.setWeight(AppraiseConstants.APPRAISE_DEFAULT_WEIGHT);
        appraiseReferee.setUserId(userId);
        appraiseReferee.setUserName(userName);
        appraiseReferee.setOrgId(orgId);
        appraiseReferee.setLoginName(loginName);
        appraiseReferee.setLevelPath(levelPath);
        appraiseReferee.setLevelPathName(levelPathName);
        appraiseReferee.setIsAnonymous(AppraiseConstants.APPRAISE_ISANONYMOUS_NO);
        appraiseReferees.add(appraiseReferee);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ImportResultDTO importUser(String appraiseId, String excelFilePath) {
        ImportResultDTO importResultDTO = new ImportResultDTO();
        importResultDTO.setIsSuccess(true);
        ImportDataDTO importData = importDataFeign.getImportData(excelFilePath);
        // 1 获取表格 查看是否格式正确
        String[][] excel = importData.getExcel();
        String[] viewLimitImportExcel = ExcelTitleBaseCheckUtil.VIEW_LIMIT_IMPORT_EXCEL;
        ExcelTitleBaseCheckUtil.baseCheck(excel, viewLimitImportExcel);

        List<String> errorMsg = new ArrayList<>();
        List<MeetingReferee> appraiseReferees = Lists.newArrayList();
        Set<String> loginNameSet = new HashSet<>();
        if (!CollectionUtils.isEmpty(importData.getRowList())) {
            doCheck(appraiseId, importData, errorMsg, appraiseReferees, loginNameSet);
        }
        if (!CollectionUtils.isEmpty(errorMsg)) {
            importResultDTO.setIsSuccess(false);
            importResultDTO.setMsg(JsonUtil.objToJson(errorMsg));
            return importResultDTO;
        }

        // 一个都没能添加时提示失败
        if (CollectionUtils.isEmpty(appraiseReferees)) {
            throw new BusinessException(AppraiseErrorNoEnum.ERR_USER_CANNOT_IS_REFEREE);
        }

        saveBatch2(appraiseReferees, CommonConstants.MAX_LIMIT);

        meetingViewLimitService.processViewLimit(appraiseId);
        return importResultDTO;
    }

    private void doCheck(String appraiseId, ImportDataDTO importData, List<String> errorMsg,
        List<MeetingReferee> appraiseReferees, Set<String> loginNameSet) {
        int index = 0;
        for (String[] row : importData.getRowList()) {
            index++;
            if (StringUtils.isBlank(row[0])) {
                errorMsg.add("第" + index + "行姓名不能为空!");
                continue;
            }
            if (StringUtils.isBlank(row[1])) {
                errorMsg.add("第" + index + "行用户账号不能为空!");
                continue;
            }
            if (loginNameSet.contains(row[1])) {
                errorMsg.add("第" + index + "行,账号不允许添加重复数据!");
                continue;
            } else {
                loginNameSet.add(row[1]);
            }
            UserDTO user = userFeign.getUserByLoginName(row[1]);
            if (user == null) {
                errorMsg.add("第" + index + "行,导入的用户不存在 ");
                continue;
            }
            if (Objects.equals(user.getIsAvailable(), AvailableEnum.NOT_AVAILABLE.getValue())) {
                errorMsg.add("第" + index + "行,导入的用户被禁用 ");
                continue;
            }
            if (!Objects.equals(user.getFullName(), row[0])) {
                errorMsg.add("第" + index + "行,账号与姓名不匹配!");
                continue;
            }
            filterAppraiseRefereeList(appraiseReferees, appraiseId, user);
        }
    }


    @Override
    public void delete(String ids) {
        if (StringUtils.isEmpty(ids)) {
            return;
        }

        String[] split = ids.split(CommonConstants.A_COMMA_IN_ENGLISH);
        // 获取评价
        MeetingReferee appraiseReferee = getById(split[0]);
        for (String id : split) {
            removeById(id);
        }
        // 更新评价下发范围
        if (appraiseReferee != null) {
            meetingViewLimitService.processViewLimit(appraiseReferee.getAppraiseId());
        }
    }

    @Override
    public int countByAppraiseId(String appraiseId) {
        return (int) count(Wrappers.<MeetingReferee>lambdaQuery().eq(MeetingReferee::getAppraiseId, appraiseId));
    }

    @Override
    public void changeIsAnonymous(String refereeId, Integer isAnonymous) {
        MeetingReferee appraiseReferee = new MeetingReferee();
        appraiseReferee.setId(refereeId);
        appraiseReferee.setIsAnonymous(isAnonymous);
        updateById(appraiseReferee);
    }

    @Override
    public void changeWeight(String refereeId, Integer weight) {

        MeetingReferee appraiseRefereeServiceById = getById(refereeId);
        if (null == appraiseRefereeServiceById) {
            throw new BusinessException(AppraiseErrorNoEnum.ERR_APPRAISE_REFEREE_NOT_EXIST);
        }

        Meeting byId = meetingService.getById(appraiseRefereeServiceById.getAppraiseId());
        if (null == byId) {
            throw new BusinessException(AppraiseErrorNoEnum.ERR_APPRAISE_NOT_EXIST);
        }

        if (byId.getStartTime().getTime() < System.currentTimeMillis()) {
            throw new BusinessException(AppraiseErrorNoEnum.ERR_NOT_ALLOW_CHANGE_WEIGHT);
        }

        if (null == weight || weight > 200 || weight <= 0) {
            throw new BusinessException(AppraiseErrorNoEnum.ERR_CHANGE_WEIGHT_ILLEGALITY);
        }

        MeetingReferee appraiseReferee = new MeetingReferee();
        appraiseReferee.setId(refereeId);
        appraiseReferee.setWeight(weight);
        updateById(appraiseReferee);
    }

    @Override
    public void exportData(MeetingRefereeListQuery query) {

        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<IMeetingRefereeService, MeetingRefereeDTO>(
            query) {
            @Override
            protected IMeetingRefereeService getBean() {
                return SpringUtil.getBean("meetingRefereeService", IMeetingRefereeService.class);
            }

            @Override
            protected PageInfo<MeetingRefereeDTO> getPageInfo() {
                return getBean().queryPage((MeetingRefereeListQuery) pageQueryDTO);

            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.MeetingReferee;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.MeetingReferee.getType();
            }

        };
        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    public Map<String, String> getUserIdByRefereeIds(List<String> refereeIds) {
        LambdaQueryWrapper<MeetingReferee> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(MeetingReferee::getId, refereeIds);
        List<MeetingReferee> list = list(queryWrapper);
        if (!CollectionUtils.isEmpty(list)) {
            return list.stream().collect(Collectors.toMap(MeetingReferee::getId, e -> e.getUserId()));
        }
        return new HashMap<>(0);
    }


    @Resource
    private ViewLimitFeign viewLimitFeign;
    @Resource
    private PushFeign pushFeign;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    public void pushAll(String appraiseId) {
        Meeting appraise = meetingService.getById(appraiseId);
        if (Objects.equals(0, appraise.getIsPublish())) {
            throw new BusinessException(ErrorNoEnum.ERR_IS_NO_PUBLISH);
        }

        MeetingRefereeDTO appraiseRefereeVo = new MeetingRefereeDTO();
        appraiseRefereeVo.setAppraiseId(appraiseId);
        // 未完成评委
        appraiseRefereeVo.setIsFinish(0);
        List<MeetingRefereeDTO> list = baseMapper.queryPage(appraiseRefereeVo);

        if (list.isEmpty()) {
            return;
        }
        //推送
        // 保存推送范围
        TreeSet<ViewLimitMainSaveDTO> viewLimitMainSaveDTOList = new TreeSet<>();
        list.forEach(r -> {
            ViewLimitMainSaveDTO viewLimitMainSaveDTO = new ViewLimitMainSaveDTO();
            viewLimitMainSaveDTO.setViewId(r.getUserId()).setViewType(2).setLimitType(0);
            viewLimitMainSaveDTOList.add(viewLimitMainSaveDTO);
        });

        ViewLimitProgrammeInfoDTO viewLimitProgrammeInfoDTO = viewLimitFeign.saveViewLimit(viewLimitMainSaveDTOList);

        SendPushDTO sendPushDTO = new SendPushDTO();
        sendPushDTO.setLang(LocaleContextHolder.getLocale().getLanguage());
        sendPushDTO.setServerName(Objects.requireNonNull(WebUtil.getRequest()).getServerName());

        PushResourceDTO pushResourceDTO = new PushResourceDTO();
        pushResourceDTO.setResourceId(appraiseId);
        pushResourceDTO.setResourceType(PushType.MEETING.getKey());
        pushResourceDTO.setResourceName(appraise.getTitle());
        pushResourceDTO.setIsTrain(0);
        pushResourceDTO.setOperateState(1);
        pushResourceDTO.setProgrammeId(viewLimitProgrammeInfoDTO.getId());
        sendPushDTO.setPushResourceDTO(pushResourceDTO);

        PushNoticeSetDTO pushNoticeSetDTO = new PushNoticeSetDTO();
        pushNoticeSetDTO.setPushMethod(1);
        EventPushSet eventPushSet = new EventPushSet();
        eventPushSet.setEventId(PushNoticeEventEnum.MEETING_77.getId());
        pushNoticeSetDTO.setEventPushSet(eventPushSet);

        //手动发送
        ManualPushSet manualPushSet = new ManualPushSet();
        manualPushSet.setEventId(PushNoticeEventEnum.MEETING_77.getId());
        manualPushSet.setProgrammeId(viewLimitProgrammeInfoDTO.getId());
        manualPushSet.setNoticeContent(0);
        pushNoticeSetDTO.setManualPushSet(manualPushSet);

        sendPushDTO.setPushNoticeSetDTO(pushNoticeSetDTO);

        PushAttributeDTO pushAttributeDTO = new PushAttributeDTO();
        pushAttributeDTO.setName(appraise.getTitle());
        pushAttributeDTO.setStartTime(appraise.getStartTime());
        pushAttributeDTO.setEndTime(appraise.getEndTime());
        pushAttributeDTO.setIntro(appraise.getActivityDescription());
        String systemName = (String) redisTemplate.opsForHash()
            .get(TenantRedisKeyConstant.SYSTEM_NAME_KEY, UserThreadContext.getTenantId());
        pushAttributeDTO.setSystemName(systemName);
        sendPushDTO.setPushAttributeDTO(pushAttributeDTO);

        pushFeign.sendPush(sendPushDTO);
    }
}
