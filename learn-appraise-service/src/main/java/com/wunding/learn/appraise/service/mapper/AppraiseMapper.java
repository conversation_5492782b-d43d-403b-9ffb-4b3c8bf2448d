package com.wunding.learn.appraise.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.appraise.service.admin.dto.AppraiseDTO;
import com.wunding.learn.appraise.service.admin.dto.AppraiseListQuery;
import com.wunding.learn.appraise.service.client.dto.UserIdpAppraiseVO;
import com.wunding.learn.appraise.service.client.query.UserIdpAppraiseQuery;
import com.wunding.learn.appraise.service.dto.AppraiseVirtualPo;
import com.wunding.learn.appraise.service.model.Appraise;
import com.wunding.learn.common.dto.CerDitchDTO;
import com.wunding.learn.common.dto.CertificationContentDTO;
import com.wunding.learn.common.dto.ResourceDeleteInfoDTO;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import java.util.Collection;
import java.util.List;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;

/**
 * <p> 评价表 Mapper 接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">xiao8</a>
 * @since 2022-08-08
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")
})
public interface AppraiseMapper extends BaseMapper<Appraise> {

    List<AppraiseDTO> queryPage(@Param("params") AppraiseListQuery query);

    List<AppraiseDTO> getAppraiseListVo(@Param("params") AppraiseVirtualPo appraiseVirtualPo);

    /**
     * 获取资源状态
     *
     * @param contentId 内容id
     * @return {@link CerDitchDTO}
     */
    CerDitchDTO getDitch(String contentId);

    List<CertificationContentDTO> getCertificationContentList(@Param("batchIds") Collection<String> batchIds);

    /**
     * 获取用户idp评价数据
     *
     * @param userIdpAppraiseQuery {@link UserIdpAppraiseQuery}
     * @return {@link UserIdpAppraiseVO}
     */
    List<UserIdpAppraiseVO> getAppraiseIdpTreeByYear(@Param("params") UserIdpAppraiseQuery userIdpAppraiseQuery);

    /**
     * 获取评价的删除信息
     * @param resourceId 评价id
     * @return ResourceDeleteInfoDTO
     */
    ResourceDeleteInfoDTO getAppraiseIsDelById(String resourceId);
}
