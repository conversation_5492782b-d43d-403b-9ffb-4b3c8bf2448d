<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.appraise.service.mapper.MeetingProviderMapper">
    <!-- 开启二级缓存 -->
    <!--
<cache type="com.wunding.learn.common.mybatis.cache.MyBatisPlusRedisCache"/>
-->

    <!-- 使用缓存 -->
    <cache-ref namespace="com.wunding.learn.appraise.service.mapper.MeetingProviderMapper"/>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wunding.learn.appraise.service.model.MeetingProvider">
        <!--@Table meeting_provider-->
        <result column="id" jdbcType="VARCHAR"
          property="id"/>
        <result column="user_id" jdbcType="VARCHAR"
          property="userId"/>
        <result column="user_name" jdbcType="VARCHAR"
          property="userName"/>
        <result column="login_name" jdbcType="VARCHAR"
          property="loginName"/>
        <result column="org_id" jdbcType="VARCHAR"
          property="orgId"/>
        <result column="level_path" jdbcType="VARCHAR"
          property="levelPath"/>
        <result column="level_path_name" jdbcType="VARCHAR"
          property="levelPathName"/>
        <result column="meeting_id" jdbcType="VARCHAR"
          property="appraiseId"/>
        <result column="source" jdbcType="INTEGER"
          property="source"/>
        <result column="status" jdbcType="INTEGER"
          property="status"/>
        <result column="score" jdbcType="INTEGER"
          property="score"/>
        <result column="is_del" jdbcType="INTEGER"
          property="isDel"/>
        <result column="sort_no" jdbcType="INTEGER"
          property="sortNo"/>
        <result column="is_pass" jdbcType="INTEGER"
          property="isPass"/>
        <result column="create_by" jdbcType="VARCHAR"
          property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP"
          property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR"
          property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP"
          property="updateTime"/>
        <result column="start_time" jdbcType="TIMESTAMP"
          property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP"
          property="endTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
             , sort_no
             , start_time
             , end_time
             , is_pass
             , user_id
             , user_name
             , login_name
             , org_id
             , level_path
             , level_path_name
             , meeting_id
             , source
             , status
             , score
             , is_del
             , create_by
             , create_time
             , update_by
             , update_time
    </sql>


    <select id="queryPage" resultType="com.wunding.learn.appraise.service.admin.dto.MeetingProviderDTO"
      useCache="false">
        select * from (
        select tap.id
             , tap.status
             , tap.meeting_id
             , tap.user_id
             , g.id as           org_id
             , tap.source
             , tap.login_name
             , tap.user_name     fullName
             , g.level_path_name orgName
             , tap.create_time
             , tap.sort_no
             , tap.start_time
             , tap.end_time
        from meeting_provider tap
                 left join sys_org g on g.id = tap.org_id
        <where>
            and tap.meeting_id = #{params.appraiseId}
                  and tap.is_del = 0
            <if test="params.userIdList != null and params.userIdList.size() > 0">
                and tap.user_id in
                <foreach item="item" collection="params.userIdList" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="params.isFinishAppraise != null">
                and tap.status = #{params.isFinishAppraise}
            </if>
            <if test="params.orgId != null and '' != params.orgId">
                and g.id = #{params.orgId}
            </if>
            <if test="params.source != null">
                and tap.source = #{params.source}
            </if>
        </where>
        ) temp
        order by temp.sort_no, temp.create_time
    </select>

    <select id="selectAppraiseId" resultType="java.lang.String">
        select distinct meeting_id
        from meeting_provider tar
        <where>
            and (instr(tar.user_name, #{keyWord}) > 0 or instr(tar.login_name, #{keyWord}) > 0)
        </where>
    </select>

    <select id="getUserNameById" resultType="java.lang.String">
        select tap.user_name fullname
        from meeting_provider tap
        where tap.id = #{providerId}
          and tap.is_del = 0
    </select>

    <!--嵌套子查询-待优化-->
    <select id="getAppraiseProvider" resultType="com.wunding.learn.appraise.service.model.MeetingProvider"
      useCache="false">
        select *
        from (select tar.*,
                     case

                         when (select count(1)
                               from appraise_file_type taft
                                        inner join appraise_provider_file tapf on taft.appraise_id = tar.appraise_id
                                   and tapf.file_type_id = taft.id
                                   and tapf.appraise_id = tar.appraise_id
                                   and taft.required = 1
                                   and tapf.provider_id = tar.id) = (select count(1)
                                                                     from appraise_file_type taft
                                                                     where taft.appraise_id = tar.appraise_id
                                                                       and taft.required = 1
                                                                       and taft.is_del = 0) then
                             0
                         else 1
                         end as isLackMaterials
              from appraise_provider tar
              where tar.appraise_id = #{appraiseId}
                and tar.is_del = 0) temp
        where temp.isLackMaterials = 0
        order by temp.create_time desc, temp.id
    </select>

    <select id="getProviderAndRefereeByAppraiseId" resultType="com.wunding.learn.common.dto.ViewLimitBeanDTO"
      useCache="false">
        select user_id     categoryId,
               'userlimit' categoryType
        from meeting_provider
        where meeting_id = #{id}
        union all
        select user_id     categoryId,
               'userlimit' categoryType
        from meeting_referee
        where meeting_id = #{id}
    </select>

    <select id="getIsStatusByMeetingId" resultType="java.lang.Integer">
        select count(distinct provider_id)
        from meeting_referee_provider_score
        where meeting_id = #{meetingId}
    </select>

    <!--嵌套子查询-待优化-->
    <select id="getMeetingProviderInfo" resultType="com.wunding.learn.appraise.api.dto.MeetingProviderInfoDTO"
      useCache="false">
        select m.id
             , m.title
             , m.end_time
             , p.start_time
             , p.end_time
             , p.is_pass
             , p.user_id
             , p.status
             , m.score
             , (select round(avg(score) / 100, 1)
                from meeting_referee_provider_score trps
                where trps.provider_id = p.id
                  and trps.meeting_id = p.meeting_id) intScore
        from meeting m
                 Left join meeting_provider p on m.id = p.meeting_id
        where m.job_qualification_id = #{qualificationId}
        <if test="userId != null and userId != ''">
            and p.user_id = #{userId}
        </if>
        and m.is_del = 0
        and p.is_del = 0
    </select>

    <select id="getMaxSortNoByMeetingId" resultType="java.lang.Integer" useCache="false">
        select coalesce(max(sort_no) + 1, 1) as next_sort_no
        from meeting_provider
        where is_del = 0
          and meeting_id = #{meetIngId}
    </select>
</mapper>
