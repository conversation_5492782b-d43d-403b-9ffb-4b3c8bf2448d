package com.wunding.learn.appraise.service.feign;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wunding.learn.appraise.api.dto.AppraiseRefereeDTO;
import com.wunding.learn.appraise.api.dto.MeetingInfoDTO;
import com.wunding.learn.appraise.api.dto.MeetingProviderInfoDTO;
import com.wunding.learn.appraise.api.service.MeetingFeign;
import com.wunding.learn.appraise.service.model.Meeting;
import com.wunding.learn.appraise.service.model.MeetingProvider;
import com.wunding.learn.appraise.service.service.IMeetingProviderService;
import com.wunding.learn.appraise.service.service.IMeetingService;
import com.wunding.learn.common.constant.appraise.AppraiseErrorNoEnum;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.exception.BusinessException;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>  答辩会议Feign服务端
 *
 * <AUTHOR> href="mailto:<EMAIL>">Herilo</a>
 * @since 2024-02-28
 */
@Slf4j
@RestController("meetingFeign")
@RequestMapping("${module.user.contentPath:/}")
public class MeetingFeignImpl implements MeetingFeign {

    @Resource
    private IMeetingService meetingService;

    @Resource
    private IMeetingProviderService meetingProviderService;

    @Override
    public AppraiseRefereeDTO getMeetingReferees(String meetingId, String providerId) {
        return meetingService.getMeetingReferees(meetingId, providerId);
    }

    @Override
    public MeetingInfoDTO getMeetingInfoByQualificationId(String qualificationId) {
        return meetingService.getMeetingInfoByQualificationId(qualificationId);
    }

    @Override
    public MeetingInfoDTO getMeetingById(String meetingId) {
        Meeting meeting = Optional.ofNullable(meetingService.getById(meetingId))
            .orElseThrow(() -> new BusinessException(
                AppraiseErrorNoEnum.ERR_MEETING_NULL));
        MeetingInfoDTO info = new MeetingInfoDTO();
        BeanUtils.copyProperties(meeting, info);
        return info;
    }

    @Override
    public List<String> getMeetingProvider(String meetingId) {
        LambdaQueryWrapper<MeetingProvider> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MeetingProvider::getAppraiseId, meetingId);
        return meetingProviderService.list(queryWrapper).stream().map(MeetingProvider::getUserId)
            .collect(Collectors.toList());
    }

    @Override
    public Integer getMeetingProviderStatus(String jobQualificationId, String applyUserId) {
        AtomicReference<Integer> meetingStatus = new AtomicReference<>(GeneralJudgeEnum.NEGATIVE.getValue());
        LambdaQueryWrapper<Meeting> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Meeting::getJobQualificationId, jobQualificationId);
        Optional.ofNullable(meetingService.getOne(queryWrapper)).ifPresent(meeting -> {
            LambdaQueryWrapper<MeetingProvider> meetingProviderQuery = new LambdaQueryWrapper<>();
            meetingProviderQuery.eq(MeetingProvider::getAppraiseId, meeting.getId());
            meetingProviderQuery.eq(MeetingProvider::getUserId, applyUserId);
            if (Optional.ofNullable(meetingProviderService.getOne(meetingProviderQuery)).isPresent()) {
                meetingStatus.set(GeneralJudgeEnum.CONFIRM.getValue());
            }
        });
        return meetingStatus.get();
    }

    @Override
    public MeetingInfoDTO getMeetingByJobQualificationId(String jobQualificationId) {
        LambdaQueryWrapper<Meeting> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Meeting::getJobQualificationId, jobQualificationId);
        Meeting meeting = Optional.ofNullable(meetingService.getOne(queryWrapper))
            .orElseThrow(() -> new BusinessException(AppraiseErrorNoEnum.ERR_MEETING_NULL));
        MeetingInfoDTO info = new MeetingInfoDTO();
        BeanUtils.copyProperties(meeting, info);
        return info;
    }

    @Override
    public MeetingProviderInfoDTO getAppointMeetingProviderInfo(String userId, String qualificationId) {
        LambdaQueryWrapper<Meeting> meetingQuery = new LambdaQueryWrapper<>();
        meetingQuery.eq(Meeting::getJobQualificationId, qualificationId);
        Meeting meeting = meetingService.getOne(meetingQuery);
        if (Optional.ofNullable(meeting).isPresent()) {
            LambdaQueryWrapper<MeetingProvider> meetingProviderQuery = new LambdaQueryWrapper<>();
            meetingProviderQuery.eq(MeetingProvider::getAppraiseId, meeting.getId());
            meetingProviderQuery.eq(MeetingProvider::getUserId, userId);
            MeetingProvider meetingProvider = meetingProviderService.getOne(meetingProviderQuery);
            if (Optional.ofNullable(meetingProvider).isPresent()) {
                MeetingProviderInfoDTO info = new MeetingProviderInfoDTO();
                BeanUtils.copyProperties(meetingProvider, info);
                return info;
            }
        }
        return null;
    }
}
