<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.appraise.service.mapper.AppraiseProviderMapper">
    <!-- 开启二级缓存 -->
    <!--
<cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
-->

    <!-- 使用缓存 -->
    <cache-ref namespace="com.wunding.learn.appraise.service.mapper.AppraiseProviderMapper"/>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wunding.learn.appraise.service.model.AppraiseProvider">
        <!--@Table appraise_provider-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="user_id" jdbcType="VARCHAR"
          property="userId"/>
        <result column="appraise_id" jdbcType="VARCHAR"
          property="appraiseId"/>
        <result column="source" jdbcType="INTEGER"
          property="source"/>
        <result column="status" jdbcType="INTEGER"
          property="status"/>
        <result column="score" jdbcType="INTEGER"
          property="score"/>
        <result column="is_del" jdbcType="INTEGER"
          property="isDel"/>
        <result column="create_by" jdbcType="VARCHAR"
          property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP"
          property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR"
          property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP"
          property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , user_id, appraise_id, source, status, score, is_del, create_by, create_time, update_by, update_time
    </sql>

    <!--嵌套子查询-待优化-->
    <select id="queryPage" resultType="com.wunding.learn.appraise.service.admin.dto.AppraiseProviderDTO"
      useCache="false">
        select * from (
        select
        tap.id
        ,tap.status
        ,tap.appraise_id
        ,tap.user_id
        ,g.id as org_id
        ,tap.source
        ,tap.login_name
        ,tap.user_name fullName
        ,g.level_path_name orgName
        , case
        when (
        select
        count( 1 )
        from
        appraise_file_type taft
        inner join appraise_provider_file tapf on taft.appraise_id = tap.appraise_id
        and tapf.file_type_id = taft.id
        and tapf.appraise_id = tap.appraise_id
        and taft.required = 1
        and tapf.provider_id = tap.id
        ) = (
        select
        count( 1 )
        from
        appraise_file_type taft
        where
        taft.appraise_id = tap.appraise_id
        and taft.required = 1
        and taft.is_del = 0
        ) then
        1 else 0
        end as appraiseFileCount
        ,tap.create_time
        from
        appraise_provider tap
        left join sys_org g on g.id=tap.org_id
        <where>
            and tap.appraise_id = #{params.appraiseId}
            and tap.is_del = 0
            <if test="params.userIdList != null and params.userIdList.size() > 0">
                and tap.user_id in
                <foreach item="item" collection="params.userIdList" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="params.isFinishAppraise != null ">
                and tap.status = #{params.isFinishAppraise}
            </if>
            <if test="params.orgId != null and '' != params.orgId">
                and g.id = #{params.orgId}
            </if>
            <if test="params.source != null ">
                and tap.source = #{params.source}
            </if>
        </where>
        ) temp
        <where>
            <if test="null != params.isSubmitAppraiseFile ">
                and temp.appraiseFileCount = #{params.isSubmitAppraiseFile}
            </if>
        </where>
        order by temp.create_time desc,temp.id
    </select>

    <select id="selectAppraiseId" resultType="java.lang.String">
        select
        distinct appraise_id
        from
        appraise_provider tar
        <where>
            and (instr(tar.user_name,#{keyWord}) > 0 or instr(tar.login_name,#{keyWord}) > 0)
        </where>
    </select>

    <select id="getUserNameById" resultType="java.lang.String">
        select tap.user_name fullname
        from appraise_provider tap
        where tap.id = #{providerId}
          and tap.is_del = 0
    </select>

    <!--嵌套子查询-待优化-->
    <select id="getAppraiseProvider" resultType="com.wunding.learn.appraise.service.model.AppraiseProvider"
      useCache="false">
        select *
        from (select tar.*
                     <if test="isMaterial != null and isMaterial == 1">
                         , if((select count(1)
                             from appraise_file_type taft
                                      inner join appraise_provider_file tapf on taft.appraise_id = tar.appraise_id
                                 and tapf.file_type_id = taft.id
                                 and tapf.appraise_id = tar.appraise_id
                                 and taft.required = 1
                                 and tapf.provider_id = tar.id) = (select count(1)
                                                                   from appraise_file_type taft
                                                                   where taft.appraise_id = tar.appraise_id
                                                                     and taft.required = 1
                                                                     and taft.is_del = 0), 0, 1) as isLackMaterials
                     </if>
              from appraise_provider tar
              where tar.appraise_id = #{appraiseId}
                and tar.is_del = 0
            ) temp
        <if test="isMaterial != null and isMaterial == 1">
            where temp.isLackMaterials = 0
        </if>
        order by temp.create_time desc, temp.id
    </select>

    <select id="getProviderAndRefereeByAppraiseId" resultType="com.wunding.learn.common.dto.ViewLimitBeanDTO" useCache="false">
        select user_id     categoryId,
               'userlimit' categoryType
        from appraise_provider
        where appraise_id = #{id}
        union all
        select user_id     categoryId,
               'userlimit' categoryType
        from appraise_referee
        where appraise_id = #{id}
    </select>
</mapper>
