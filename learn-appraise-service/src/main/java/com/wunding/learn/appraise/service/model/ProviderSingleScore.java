package com.wunding.learn.appraise.service.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p> 被评价人单项汇总表
 *
 * <AUTHOR> href="mailto:<EMAIL>">xiao8</a>
 * @since 2022-08-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("provider_single_score")
@Schema(name = "ProviderSingleScore", description = "被评价人单项汇总表")
public class ProviderSingleScore implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    @Schema(description = "主键")
    @TableId(value = "id", type = IdType.INPUT)
    private String id;


    /**
     * 被评人id
     */
    @Schema(description = "被评人id")
    @TableField("provider_id")
    private String providerId;


    /**
     * 评分项id
     */
    @Schema(description = "评分项id")
    @TableField("template_id")
    private String templateId;


    /**
     * 评价表id
     */
    @Schema(description = "评价表id")
    @TableField("appraise_id")
    private String appraiseId;


    /**
     * 评分  实际分数*100
     */
    @Schema(description = "评分  实际分数*100")
    @TableField("score")
    private Integer score;


    /**
     * 评委人数
     */
    @Schema(description = "评委人数")
    @TableField("referee_num")
    private Integer refereeNum;


    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;


    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 修改人
     */
    @Schema(description = "修改人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;


    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


}
