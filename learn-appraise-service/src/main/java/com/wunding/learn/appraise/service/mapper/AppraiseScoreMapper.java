package com.wunding.learn.appraise.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.appraise.service.model.AppraiseScore;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import java.math.BigDecimal;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;

/**
 * <p> 评价评分明细表 Mapper 接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">xiao8</a>
 * @since 2022-08-08
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")
})
public interface AppraiseScoreMapper extends BaseMapper<AppraiseScore> {

    /**
     * 查询已评价人数
     *
     * @param providerId 被评人id
     * @param appraiseId 评价id
     * @param templateId 模板内容id
     * @return 已评价人数
     */
    int countRefereeNum(@Param("providerId") String providerId, @Param("appraiseId") String appraiseId,
        @Param("templateId") String templateId);

    /**
     * @param providerId 被评人id
     * @param appraiseId 评价id
     * @param templateId 模板内容id
     * @return 单项汇总分
     */
    int sumSingleScore(@Param("providerId") String providerId, @Param("appraiseId") String appraiseId,
        @Param("templateId") String templateId);

    /**
     * @param providerId 被评人id
     * @param appraiseId 评价id
     * @param refereeId  评委id
     * @return 评委对被评人总分
     */
    int sumRefereeProviderScore(@Param("providerId") String providerId, @Param("appraiseId") String appraiseId,
        @Param("refereeId") String refereeId);

    /**
     * 询被评人平均分原始值
     *
     * @param providerId
     * @param appraiseId
     * @return
     */
    BigDecimal sumProviderScore(@Param("providerId") String providerId, @Param("appraiseId") String appraiseId);

    /**
     * 查询被评人加权平均分
     *
     * @param providerId 被评人id
     * @param appraiseId 评价id
     * @return
     */
    BigDecimal sumProviderWeightScore(@Param("providerId") String providerId, @Param("appraiseId") String appraiseId);

    /**
     * 查询已评价人数 考虑到评价里面可能不存在打分题的情况
     *
     * @param providerId 被评人id
     * @param appraiseId 评价id
     * @return
     */
    int countAppraiseReferee(@Param("providerId") String providerId, @Param("appraiseId") String appraiseId);
}
