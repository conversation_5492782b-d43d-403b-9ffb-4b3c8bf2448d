package com.wunding.learn.appraise.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Lists;
import com.wunding.learn.appraise.service.admin.dto.AppraiseProviderDTO;
import com.wunding.learn.appraise.service.admin.dto.AppraiseProviderListQuery;
import com.wunding.learn.appraise.service.admin.dto.SaveAppraiseProviderUserDTO;
import com.wunding.learn.appraise.service.constant.AppraiseConstants;
import com.wunding.learn.appraise.service.dao.AppraiseProviderDao;
import com.wunding.learn.appraise.service.mapper.AppraiseProviderMapper;
import com.wunding.learn.appraise.service.mapper.AppraiseScoreMapper;
import com.wunding.learn.appraise.service.model.Appraise;
import com.wunding.learn.appraise.service.model.AppraiseProvider;
import com.wunding.learn.appraise.service.model.AppraiseReferee;
import com.wunding.learn.appraise.service.service.IAppraiseProviderService;
import com.wunding.learn.appraise.service.service.IAppraiseRefereeService;
import com.wunding.learn.appraise.service.service.IAppraiseService;
import com.wunding.learn.appraise.service.service.IAppraiseViewLimitService;
import com.wunding.learn.common.constant.appraise.AppraiseErrorNoEnum;
import com.wunding.learn.common.constant.other.AvailableEnum;
import com.wunding.learn.common.constant.other.CommonConstants;
import com.wunding.learn.common.dto.ImportResultDTO;
import com.wunding.learn.common.dto.SaveViewLimitDTO;
import com.wunding.learn.common.dto.ViewLimitBeanDTO;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.mybatis.service.impl.BaseServiceImpl;
import com.wunding.learn.common.util.bean.SpringUtil;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.common.util.math.NumberOperationUtils;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.file.api.component.ExportComponent;
import com.wunding.learn.file.api.constant.ExportBizType;
import com.wunding.learn.file.api.constant.ExportFileNameEnum;
import com.wunding.learn.file.api.dto.AbstractExportDataDTO;
import com.wunding.learn.file.api.dto.IExportDataDTO;
import com.wunding.learn.file.api.dto.ImportDataDTO;
import com.wunding.learn.file.api.service.ImportDataFeign;
import com.wunding.learn.user.api.dto.OrgShowDTO;
import com.wunding.learn.user.api.dto.UserDTO;
import com.wunding.learn.user.api.service.OrgFeign;
import com.wunding.learn.user.api.service.UserFeign;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * <p> 被评人（学员） 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">xiao8</a>
 * @since 2022-08-08
 */
@Slf4j
@Service("appraiseProviderService")
public class AppraiseProviderServiceImpl extends
    BaseServiceImpl<AppraiseProviderMapper, AppraiseProvider> implements
    IAppraiseProviderService {

    private static final String SOURCE = "source";
    private static final String IS_SUBMIT_APPRAISE_FILE = "isSubmitAppraiseFile";
    private static final String IS_FINISH_APPRAISE = "isFinishAppraise";

    @Resource
    private IAppraiseRefereeService appraiseRefereeService;

    @Resource
    private UserFeign userFeign;

    @Resource
    private ImportDataFeign importDataFeign;

    @Resource
    private AppraiseScoreMapper appraiseScoreMapper;

    @Resource
    private ExportComponent exportComponent;

    @Resource
    private OrgFeign orgFeign;

    @Resource
    @Lazy
    private IAppraiseViewLimitService appraiseViewLimitService;

    @Resource
    @Lazy
    private IAppraiseService appraiseService;
    @Resource(name = "appraiseProviderDao")
    private AppraiseProviderDao appraiseProviderDao;

    @Override
    public PageInfo<AppraiseProviderDTO> queryPage(AppraiseProviderListQuery query) {
        AppraiseProviderDTO appraiseProviderVo = new AppraiseProviderDTO();
        BeanUtils.copyProperties(query, appraiseProviderVo);
        PageInfo<AppraiseProviderDTO> page = PageMethod.startPage(query.getPageNo(),
                query.getPageSize())
            .doSelectPageInfo(() -> baseMapper.queryPage(appraiseProviderVo));
        Set<String> orgIdSet = page.getList().stream().map(AppraiseProvider::getOrgId)
            .collect(Collectors.toSet());
        Map<String, OrgShowDTO> orgShowDTOMap = orgFeign.getOrgShowDTO(orgIdSet);
        for (AppraiseProviderDTO providerVo : page.getList()) {

            providerVo.setAppraisedCount(
                appraiseScoreMapper.countAppraiseReferee(providerVo.getId(),
                    appraiseProviderVo.getAppraiseId()));

            // 平均分
            BigDecimal sumProviderScore =
                appraiseScoreMapper.sumProviderScore(providerVo.getId(),
                    appraiseProviderVo.getAppraiseId());
            providerVo.setAvgScore(
                sumProviderScore.setScale(AppraiseConstants.APPRAISE_SCORE_PRECISION,
                    BigDecimal.ROUND_HALF_DOWN));

            // 加权平均分
            BigDecimal sumProviderWrightScore =
                appraiseScoreMapper.sumProviderWeightScore(providerVo.getId(),
                    appraiseProviderVo.getAppraiseId());
            providerVo.setAvgWeightScore(sumProviderWrightScore
                .divide(new BigDecimal(NumberOperationUtils.ONE_HUNDRED_NUM),
                    AppraiseConstants.APPRAISE_SCORE_PRECISION, BigDecimal.ROUND_HALF_DOWN));

            providerVo.setIsSubmitAppraiseFile(providerVo.getAppraiseFileCount());
            providerVo.setIsFinishAppraise(providerVo.getStatus());

            Optional.ofNullable(orgShowDTOMap.get(providerVo.getOrgId())).ifPresent(orgShowDTO -> {
                providerVo.setOrgName(orgShowDTO.getOrgShortName());
                providerVo.setOrgPath(orgShowDTO.getLevelPathName());
            });
        }
        return page;
    }

    @Override
    public List<String> selectAppraiseId(String keyWord) {
        return baseMapper.selectAppraiseId(keyWord);
    }

    @Override
    public String getUserNameById(String providerId) {
        return baseMapper.getUserNameById(providerId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addUser(SaveAppraiseProviderUserDTO saveAppraiseProviderUserDTO) {

        List<AppraiseProvider> appraiseProviders = Lists.newArrayList();
        for (String userId : saveAppraiseProviderUserDTO.getUserIds()) {
            UserDTO user = userFeign.getUserById(userId);
            this.filterAppraiseProviderList(appraiseProviders, saveAppraiseProviderUserDTO.getAppraiseId(), userId, 2,
                user);
        }

        // 一个都没能添加时提示失败
        if (CollectionUtils.isEmpty(appraiseProviders)) {
            throw new BusinessException(AppraiseErrorNoEnum.ERR_USER_CANNOT_IS_PROVIDER);
        }

        // 查询评价标题
        Appraise byId = appraiseService.getById(saveAppraiseProviderUserDTO.getAppraiseId());
        var title = Optional.ofNullable(byId).isPresent() ? byId.getTitle() : "";

        // 批量添加被评人
        appraiseProviderDao.saveBatchAppraiseProvider(saveAppraiseProviderUserDTO.getAppraiseId(),
            title, appraiseProviders);

        appraiseViewLimitService.processViewLimit(saveAppraiseProviderUserDTO.getAppraiseId());
    }

    /**
     * 过滤已经是评委的用户和已经是被评人的用户
     *
     * @param appraiseReferees
     * @param appraiseId       评价id
     * @param userId
     * @param source           1:名单导入,2:在线录入
     */
    private void filterAppraiseProviderList(List<AppraiseProvider> appraiseReferees,
        String appraiseId, String userId,
        int source, UserDTO user) {
        String userName = user.getFullName();
        String orgId = user.getOrgId();
        String levelPath = user.getLevelPath();
        String levelPathName = user.getLevelPathName();
        String loginName = user.getLoginName();

        LambdaQueryWrapper<AppraiseReferee> appraiseRefereeLambdaQueryWrapper = new LambdaQueryWrapper<>();
        appraiseRefereeLambdaQueryWrapper.eq(AppraiseReferee::getAppraiseId, appraiseId);
        appraiseRefereeLambdaQueryWrapper.eq(AppraiseReferee::getUserId, userId);
        long count = appraiseRefereeService.count(appraiseRefereeLambdaQueryWrapper);
        if (0 != count) {
            // 已存在评委中
            throw new BusinessException(AppraiseErrorNoEnum.ERROR_EXISTS_PROVIDER_REFEREE);
        }

        LambdaQueryWrapper<AppraiseProvider> appraiseProviderLambdaQueryWrapper = new LambdaQueryWrapper<>();
        appraiseProviderLambdaQueryWrapper.eq(AppraiseProvider::getAppraiseId, appraiseId);
        appraiseProviderLambdaQueryWrapper.eq(AppraiseProvider::getUserId, userId);
        long appraiseProviderCount = count(appraiseProviderLambdaQueryWrapper);
        if (0 != appraiseProviderCount) {
            return;
        }

        AppraiseProvider appraiseProvider = new AppraiseProvider();
        appraiseProvider.setId(StringUtil.newId());
        appraiseProvider.setAppraiseId(appraiseId);
        appraiseProvider.setSource(source);
        appraiseProvider.setUserId(userId);
        appraiseProvider.setUserName(userName);
        appraiseProvider.setOrgId(orgId);
        appraiseProvider.setLevelPath(levelPath);
        appraiseProvider.setLevelPathName(levelPathName);
        appraiseProvider.setLoginName(loginName);
        appraiseReferees.add(appraiseProvider);
    }

    @Override
    public ImportResultDTO importUser(String appraiseId, String excelFilePath) {
        ImportResultDTO importResultDTO = new ImportResultDTO();
        importResultDTO.setIsSuccess(true);
        ImportDataDTO importData = importDataFeign.getImportData(excelFilePath);
        Set<String> loginNameSet = new HashSet<>();
        List<String> errorMsg = new ArrayList<>();
        List<AppraiseProvider> appraiseProviders = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(importData.getRowList())) {
            doCheck(appraiseId, importData, loginNameSet, errorMsg, appraiseProviders);
        }
        if (!CollectionUtils.isEmpty(errorMsg)) {
            importResultDTO.setIsSuccess(false);
            importResultDTO.setMsg(JsonUtil.objToJson(errorMsg));
            return importResultDTO;
        }

        // 一个都没能添加时提示失败
        if (CollectionUtils.isEmpty(appraiseProviders)) {
            throw new BusinessException(AppraiseErrorNoEnum.ERR_USER_CANNOT_IS_PROVIDER);
        }

        // 查询评价标题
        Appraise byId = appraiseService.getById(appraiseId);
        var title = Optional.ofNullable(byId).isPresent() ? byId.getTitle() : "";

        // 批量添加被评人
        appraiseProviderDao.saveBatchAppraiseProvider(appraiseId, title, appraiseProviders);

        appraiseViewLimitService.processViewLimit(appraiseId);

        return importResultDTO;
    }

    private void doCheck(String appraiseId, ImportDataDTO importData, Set<String> loginNameSet, List<String> errorMsg,
        List<AppraiseProvider> appraiseProviders) {
        int index = 0;
        for (String[] row : importData.getRowList()) {
            index++;
            if (StringUtils.isBlank(row[0])) {
                errorMsg.add("第" + index + "行姓名不能为空!");
                continue;
            }
            if (StringUtils.isBlank(row[1])) {
                errorMsg.add("第" + index + "行用户账号不能为空!");
                continue;
            }
            if (loginNameSet.contains(row[1])) {
                errorMsg.add("第" + index + "行,账号不允许添加重复数据!");
                continue;
            } else {
                loginNameSet.add(row[1]);
            }
            UserDTO user = userFeign.getUserByLoginName(row[1]);
            if (user == null) {
                errorMsg.add("第" + index + "行,导入的用户不存在 ");
                continue;
            }
            if (Objects.equals(user.getIsAvailable(), AvailableEnum.NOT_AVAILABLE.getValue())) {
                errorMsg.add("第" + index + "行,导入的用户被禁用 ");
                continue;
            }
            if (!Objects.equals(user.getFullName(), row[0])) {
                errorMsg.add("第" + index + "行,账号与姓名不匹配!");
                continue;
            }
            filterAppraiseProviderList(appraiseProviders, appraiseId, user.getId(), 1, user);
        }
    }

    @Override
    public void delete(String ids) {
        if (StringUtils.isEmpty(ids)) {
            return;
        }

        String[] split = ids.split(CommonConstants.A_COMMA_IN_ENGLISH);

        // 获取评价
        List<AppraiseProvider> appraiseProviders = listByIds(Arrays.asList(split));

        // 遍历执行删除操作，同时记录业务日志
        appraiseProviders.forEach(e -> appraiseProviderDao.delAppraiseProvider(e));

        // 更新评价下发范围
        AppraiseProvider appraiseProvider = appraiseProviders.get(0);
        if (appraiseProvider != null) {
            appraiseViewLimitService.processViewLimit(appraiseProvider.getAppraiseId());
        }
    }

    @Override
    public void exportData(AppraiseProviderListQuery query) {

        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<IAppraiseProviderService, AppraiseProviderDTO>(
            query) {
            @Override
            protected IAppraiseProviderService getBean() {
                return SpringUtil.getBean("appraiseProviderService",
                    IAppraiseProviderService.class);
            }

            @Override
            protected PageInfo<AppraiseProviderDTO> getPageInfo() {
                return getBean().queryPage((AppraiseProviderListQuery) pageQueryDTO);

            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.AppraiseProvider;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.AppraiseProvider.getType();
            }

            @Override
            protected void afterCreateBeanMap(Map<String, Object> map) {
                Object source = map.get(SOURCE);
                if (Objects.equals(source, 1)) {
                    map.put(SOURCE, "名单导入");
                } else {
                    map.put(SOURCE, "在线录入");
                }

                Object isSubmitAppraiseFile = map.get(IS_SUBMIT_APPRAISE_FILE);
                if (Objects.equals(isSubmitAppraiseFile, 1)) {
                    map.put(IS_SUBMIT_APPRAISE_FILE, "已提交必填材料");
                } else {
                    map.put(IS_SUBMIT_APPRAISE_FILE, "未提交必填材料");
                }

                Object isFinishAppraise = map.get(IS_FINISH_APPRAISE);
                if (Objects.equals(isFinishAppraise, 1)) {
                    map.put(IS_FINISH_APPRAISE, "已评价");
                } else {
                    map.put(IS_FINISH_APPRAISE, "未评价");
                }
            }
        };

        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    public List<ViewLimitBeanDTO> getProviderAndRefereeByAppraiseId(String id) {
        return baseMapper.getProviderAndRefereeByAppraiseId(id);
    }

    @Override
    public void viewLimitChange(Map<String, List<SaveViewLimitDTO>> viewLimitChangeMap) {
        for (Map.Entry<String, List<SaveViewLimitDTO>> entry : viewLimitChangeMap.entrySet()) {
            String appraiseId = entry.getKey();
            //更新下发范围
            List<SaveViewLimitDTO> list = entry.getValue();
            log.info("List<SaveViewLimitDTO> list->" + JsonUtil.objToJson(list));
            if (CollectionUtils.isEmpty(list)) {
                return;
            }
            List<AppraiseProvider> appraiseProviders = Lists.newArrayList();
            for (SaveViewLimitDTO dto : list) {
                String userId = dto.getCategoryId();
                UserDTO user = userFeign.getUserById(userId);
                this.filterAppraiseProviderList(appraiseProviders, appraiseId, userId, 2, user);
            }
            if (!CollectionUtils.isEmpty(appraiseProviders)) {
                saveBatch2(appraiseProviders, 500);
            }
            log.info("appraiseProviders->" + JsonUtil.objToJson(appraiseProviders));
        }
    }
}
