<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <artifactId>learn-appraise-service</artifactId>
    <build>
        <plugins>
            <plugin>
                <groupId>io.fabric8</groupId>
                <artifactId>docker-maven-plugin</artifactId>
                <version>0.45.0</version>
                <configuration>
                    <skip>${skipDockerImage}</skip>
                    <!-- Docker 推送镜像仓库地址-->
                    <pushRegistry>${image.registry}</pushRegistry>
                    <images>
                        <image>
                            <!--由于推送到私有镜像仓库，镜像名需要添加仓库地址-->
                            <name>${image.registry}${image.registry.path}appraise:${image.tag}</name>
                            <!--定义镜像构建行为-->
                            <build>
                                <buildx>
                                    <builderName>mybuilder</builderName>
                                    <platforms>
                                        <platform>${docker.platforms}</platform>
                                    </platforms>
                                </buildx>
                                <dockerFileDir>${project.basedir}</dockerFileDir>
                                <dockerFile>${docker.file}</dockerFile>
                            </build>
                        </image>
                    </images>
                </configuration>
                <executions>
                    <execution>
                        <!--在哪个生命周期阶段执行-->
                        <phase>install</phase>
                        <!--执行别名-->
                        <id>build-image</id>
                        <goals>
                            <!--插件目标-->
                            <goal>build</goal>
                            <goal>push</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${java.version}</source>
                    <release>${java.version}</release>
                    <target>${java.version}</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <!--开启分层编译支持-->
                    <layers>
                        <enabled>true</enabled>
                    </layers>
                    <skip>${spring-boot.repackage.skip}</skip>
                    <finalName>${project.name}-${revision}-fat</finalName>
                </configuration>
                <groupId>org.springframework.boot</groupId>
                <version>${spring-boot.version}</version>
            </plugin>
        </plugins>

        <resources>
            <resource>
                <directory>src/main/java</directory>
                <filtering>false</filtering>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
                <includes>
                    <include>**/*</include>
                </includes>
            </resource>
        </resources>
    </build>
    <dependencies>
        <dependency>
            <groupId>com.wunding</groupId>
            <artifactId>learn-lecturer-api</artifactId>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.wunding</groupId>
            <artifactId>learn-common-field-sync</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wunding</groupId>
            <artifactId>learn-course-api</artifactId>
        </dependency>
        <dependency>
            <artifactId>learn-common-base</artifactId>
            <groupId>com.wunding</groupId>
        </dependency>

        <dependency>
            <groupId>com.wunding</groupId>
            <artifactId>learn-common-consumer</artifactId>
        </dependency>

        <dependency>
            <artifactId>learn-file-api</artifactId>
            <groupId>com.wunding</groupId>
        </dependency>

        <dependency>
            <artifactId>learn-appraise-api</artifactId>
            <groupId>com.wunding</groupId>
        </dependency>

        <dependency>
            <artifactId>learn-user-api</artifactId>
            <groupId>com.wunding</groupId>
        </dependency>

        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
            <version>2.3.1</version>
        </dependency>

        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
        </dependency>

        <dependency>
            <artifactId>spring-boot-starter-web</artifactId>
            <groupId>org.springframework.boot</groupId>
        </dependency>
        <dependency>
            <artifactId>spring-boot-starter-actuator</artifactId>
            <groupId>org.springframework.boot</groupId>
        </dependency>

        <dependency>
            <groupId>io.opentelemetry</groupId>
            <artifactId>opentelemetry-exporter-otlp</artifactId>
        </dependency>

        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
        </dependency>
        <dependency>
            <artifactId>druid-spring-boot-3-starter</artifactId>
            <groupId>com.alibaba</groupId>
        </dependency>

        <dependency>
            <artifactId>lombok</artifactId>
            <groupId>org.projectlombok</groupId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
            <groupId>org.springframework.cloud</groupId>
        </dependency>


        <dependency>
            <artifactId>spring-boot-starter-test</artifactId>
            <groupId>org.springframework.boot</groupId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <artifactId>guava</artifactId>
            <groupId>com.google.guava</groupId>
        </dependency>

        <!-- 添加Spring-boot-starter-redis依赖 -->
        <dependency>
            <artifactId>spring-boot-starter-data-redis</artifactId>
            <groupId>org.springframework.boot</groupId>
        </dependency>


        <dependency>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>mybatis</artifactId>
                    <groupId>org.mybatis</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mybatis-spring</artifactId>
                    <groupId>org.mybatis</groupId>
                </exclusion>
            </exclusions>
            <groupId>com.github.pagehelper</groupId>
        </dependency>
        <dependency>
            <groupId>com.wunding</groupId>
            <artifactId>learn-push-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel-core</artifactId>
            <version>3.1.1</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>
    <description>learn-appraise-service</description>
    <modelVersion>4.0.0</modelVersion>
    <name>learn-appraise-service</name>
    <parent>
        <artifactId>learn-appraise</artifactId>
        <groupId>com.wunding</groupId>

        <version>${revision}</version>
    </parent>


</project>
