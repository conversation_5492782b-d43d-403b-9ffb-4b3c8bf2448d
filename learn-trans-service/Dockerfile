FROM hengyunabc/arthas:3.7.2-no-jdk

FROM openjdk:11 AS builder
WORKDIR /app/
COPY ./learn-trans-service/target/learn-trans-service-*-fat.jar /app/trans.jar
RUN java -Djarmode=layertools -jar trans.jar extract

FROM registry-test.wunding.com/wunding/file-base:openjdk-11-liboffice24.2.0.3-ffmpge6.1.1

COPY --from=builder app/dependencies/ /app
COPY --from=builder app/snapshot-dependencies/ /app
COPY --from=builder app/spring-boot-loader/ /app
COPY --from=0 /opt/arthas /app/arthas
# 保证几乎只更新代码层
COPY --from=builder app/application/ /app

WORKDIR /app/

# 仅arthas运行需要，相关jdk命令运行需要有实际的用户存在
RUN groupadd -r myuser -g 1000 && useradd -d /home/<USER>/bin/bash -g myuser myuser

ENTRYPOINT ["tini", "--", "java", "-Duser.timezone=GMT+08", "org.springframework.boot.loader.launch.JarLauncher"]

EXPOSE 28003