# 应用服务 WEB 访问端口
server:
  port: 8080
# 应用名称
spring:

  servlet:
    multipart:
      max-file-size: 2048MB
      max-request-size: 2048MB



  #redis
  # Redis服务器地址
  data:
    redis:
      #host: 127.0.0.1
      host: redis
      # Redis服务器连接端口
      #port: 6379
      port: 6379
      # Redis数据库索引（默认为0）
      database: 5
      # Redis服务器连接密码（默认为空）
      #    password: 123456
      password: M0eHdhs9kk4VKLjRAb7J41
      # 连接超时时间（毫秒）
      timeout: 10000

  #rabbitmq 配置
  rabbitmq:
    host: rabbitmq
    port: 5672
    virtual-host: /
    username: guest
    password: guest


management:
  tracing:
    sampling:
      probability: 1.0
    enabled: true
    propagation:
      type: B3
  otlp:
    metrics:
      export:
        enabled: true
  zipkin:
    tracing:
      endpoint: http://jaeger-collector.observability.svc:14250

opentelemetry:
  traces:
    exporter: otlp
  service:
    name: ${spring.application.name}
  jaeger:
    endpoint: http://jaeger-collector.observability.svc:4317
    protocol: grpc
  instrumentation:
    feign:
      enabled: true
    spring-webmvc:
      enabled: true

learn:
  service:
    learn-user-service: "http://user:8080"


#seata:
#  client:
#    undo:
#      log-serialization: kryo
#  config:
#    type: file
#  application-id: ${spring.application.name}
#  enable-auto-data-source-proxy: true
#  registry:
#    type: file
#  service:
#    grouplist:
#      default: seata:8091
#    vgroup-mapping:
#      springboot-seata-group: default
#  # seata 事务组编号 用于TC集群名
#  tx-service-group: springboot-seata-group

app:
  signKey: bladexisapowerfulmicroservicearchitectureupgradedandoptimizedfromacommercialproject
  single:
    - web
    - api111
  # 存储方式 1 对象存储 2 本地存储
  storageType: 1
  endPoint: http://minio.minio.svc:80
  accessKey: Rh2BL2nK9RWjqiPX
  secretKey: Xu0uU8xiDtr7bnmjpMVa2N7ZxPgGuLEn
  bucketName: dev
  location: /data/
  root: dev
  ## 做课模板path，必须要保证在upload目录存在
  zuoKeTemplatePath: /app/template/zuoke
  transCodingUrl: http://wdweike.com/wps2html/uploadServlet
  ffmpegPath: ffmpeg
  staticBaseUrl: https://oss-test.wdxuexi.com:10084
  imgProxyUrl: https://imgproxy.oss-test.wdxuexi.com
  # 转码消息者数量
  transConcurrency: 10
  officeTransType: 0
  ffmpegPerformanceCpuNice: 19
  ffmpegPerformanceThreads: 2

  pdf2html:
    threadNum: 8
    pageTimeout: 120
    officeTimeout: 2400
    home: /data
    bin: pdf2htmlEX
    dataDir: /config/pdf2htmlEX_data



debug: false
############# springDoc配置 #############
springdoc:
  version: '@springdoc.version@'
  api-docs:
    groups:
      enabled: true
    enabled: true
  swagger-ui:
    operationsSorter: function
    tagsSorter: alpha
    docExpansion: none
    path: /swagger-ui.html
  group-configs:
    - group: rest
      packages-to-scan: com.wunding.learn.file.rest
    - group: feign
      packages-to-scan: com.wunding.learn.file.feign
  # 不配置下面 get请求为对象会变为 json
  default-flat-param-object: true
  disable-i18n: true

############# baidu API配置 #############
baidu:
  tokenUrl: https://aip.baidubce.com/oauth/2.0/token
  createUrl: https://aip.baidubce.com/rpc/2.0/aasr/v1/create
  queryUrl: https://aip.baidubce.com/rpc/2.0/aasr/v1/query
  accessKey: c5ZLGbjHz1QUZSnAoKUYx2p0
  secretKey: omt1SGcOafO7CWc01zf5UhykosZpFmUz