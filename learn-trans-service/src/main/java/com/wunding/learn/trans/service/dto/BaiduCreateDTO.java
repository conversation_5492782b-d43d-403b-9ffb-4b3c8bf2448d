package com.wunding.learn.trans.service.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 百度创建任务响应dto
 *
 * <AUTHOR>
 * @date 2023/06/07
 */
@Data
@Schema(name = "BaiduCreateDTO", description = "百度音频转写创建任务api响应对象")
public class BaiduCreateDTO {

    @Schema(description = "日志id")
    @JsonProperty("log_id")
    private Long logId;

    @Schema(description = "错误码")
    @JsonProperty("error_code")
    private Integer errorCode;

    @Schema(description = "错误信息")
    @JsonProperty("error_msg")
    private String errorMsg;

    @Schema(description = "任务状态")
    @JsonProperty("task_status")
    private String taskStatus;

    @Schema(description = "任务id,供查询使用")
    @JsonProperty("task_id")
    private String taskId;


}