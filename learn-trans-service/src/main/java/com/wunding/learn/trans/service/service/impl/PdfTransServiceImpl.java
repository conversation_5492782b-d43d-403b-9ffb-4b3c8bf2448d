package com.wunding.learn.trans.service.service.impl;

import com.wunding.learn.common.constant.trans.TransErrorNumEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.file.trans.grpc.lib.TransRequest;
import com.wunding.learn.trans.service.config.Pdf2htmlConfig;
import com.wunding.learn.trans.service.config.SysConfig;
import com.wunding.learn.trans.service.core.CommandExec;
import com.wunding.learn.trans.service.pdf.pojo.Pdf2htmlRequestParams;
import com.wunding.learn.trans.service.util.CmdUtil;
import com.wunding.learn.trans.service.util.FileUtil;
import com.wunding.learn.trans.service.util.ZipCompressorUtil;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.Locale;
import java.util.Objects;
import java.util.UUID;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> href="mailto:<EMAIL>">yuxueyi</a>
 * @version V7.0.0
 * @since 2022/5/25  14:09
 */
@Slf4j
@Service
public class PdfTransServiceImpl {

    @Resource
    private SysConfig sysConfig;

    @Resource
    private CommandExec commandExec;

    @Resource
    private Pdf2htmlConfig config;

    /**
     * pdf 转码-> 转为html
     */
    //@Override
    public String doTrans(TransRequest transRequest) {


        // 原文件
        String needTransPdfPath = transRequest.getTempFilePath();
        String needTransFileCurrentPath = transRequest.getFilePath();

        File needTransPdfFile = new File(sysConfig.getPhysicalPath(needTransPdfPath));
        File needTransCurrentFile = new File(sysConfig.getPhysicalPath(needTransFileCurrentPath));

        if(!needTransCurrentFile.exists()){
            try {
                FileUtils.copyFile(needTransPdfFile, needTransCurrentFile);
            } catch (IOException e) {
                log.error("pdf copy error", e);
            }
        }

        if (!Objects.equals(FilenameUtils.getBaseName(needTransPdfPath), "index")) {

            needTransPdfPath = FilenameUtils.getFullPath(needTransFileCurrentPath)
                + FilenameUtils.getBaseName(needTransFileCurrentPath)
                + File.separator
                + "index.pdf";
            try {
                File indexPdfFile = new File(sysConfig.getPhysicalPath(needTransPdfPath));
                if (
                    indexPdfFile.exists() &&
                    !Objects.equals(needTransPdfFile.getAbsolutePath(),indexPdfFile.getAbsolutePath())
                ) {
                    Files.delete(indexPdfFile.toPath());
                }
                if(!Objects.equals(needTransPdfFile.getAbsolutePath(),indexPdfFile.getAbsolutePath())){
                    FileUtils.copyFile(needTransPdfFile, indexPdfFile);
                }
                needTransPdfFile = indexPdfFile;
            } catch (IOException e) {
                log.error("pdf copy error", e);
            }
        }

        String destPath = FilenameUtils.getFullPath(needTransFileCurrentPath);
        if(!Objects.equals(FilenameUtils.getBaseName(needTransFileCurrentPath),"index")){
            destPath = FilenameUtils.getFullPath(destPath)
                + FilenameUtils.getBaseName(needTransFileCurrentPath)
                + File.separator;
        }
        File destDir = new File(sysConfig.getPhysicalPath(destPath));

        String id = UUID.randomUUID().toString().replace("-", "").toLowerCase(Locale.ROOT);
        Pdf2htmlRequestParams params =
            Pdf2htmlRequestParams.builder()
                .zoom(1.5)
                .fitWidth(1200)
                .pageFileName(id + "-%d.html")
                .destDir(destDir.getAbsolutePath())
                .dataDir(config.getDataDir())
                .noDrm(transRequest.getPdfNoDRM())
                .proof(transRequest.getPdfProof())
                .build();

        String pdfTransCmd = CmdUtil.buildPdfTransCmd(config.getBin(), params, needTransPdfFile.getAbsolutePath());
        // 日志
        int execResult = commandExec.exec(pdfTransCmd, log::info);
        if (execResult != 0) {
            //转码失败
            log.error("commandExec fail execResult is [{}] and pdfTransCmd :[{}]", execResult, pdfTransCmd);
            throw new BusinessException(TransErrorNumEnum.EXECUTE_TRANS_FAILED);
        }

        // 打包zip
        String zipPath = needTransPdfFile.getParent() + ".zip";
        ZipCompressorUtil zc = new ZipCompressorUtil(zipPath);
        zc.compress(FileUtil.buildParams(needTransPdfFile.getParent()));

        return FilenameUtils.getPath(destPath)+FilenameUtils.getBaseName(needTransPdfPath)+".html";
    }
}
