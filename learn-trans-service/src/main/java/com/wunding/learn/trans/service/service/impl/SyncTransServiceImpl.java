package com.wunding.learn.trans.service.service.impl;

import com.wunding.learn.common.constant.file.FileErrorNoEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.trans.service.config.Pdf2htmlConfig;
import com.wunding.learn.trans.service.config.SysConfig;
import com.wunding.learn.trans.service.core.CommandExec;
import com.wunding.learn.trans.service.pdf.pojo.Pdf2htmlRequestParams;
import com.wunding.learn.trans.service.service.SyncTransService;
import com.wunding.learn.trans.service.util.CmdUtil;
import com.wunding.learn.trans.service.util.FileUtil;
import com.wunding.learn.trans.service.util.ZipCompressorUtil;
import java.io.File;
import java.io.IOException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 同步转码
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class SyncTransServiceImpl implements SyncTransService {

    @Resource
    private SysConfig sysConfig;

    @Resource
    private Pdf2htmlConfig config;

    @Resource
    private CommandExec commandExec;

    private static final String SEPARATOR = "/";

    @Override
    public synchronized File transOffice(String filePath) {
        File needTransFile = new File(sysConfig.getPhysicalPath(filePath));
        String outDir = needTransFile.getAbsolutePath().substring(0, needTransFile.getAbsolutePath().lastIndexOf("."));
        String officeTransCmd = CmdUtil.buildOfficeTransCmd("soffice", outDir, needTransFile.getAbsolutePath());
        int timeout = config.getOfficeTimeout() == null ? 600 : config.getOfficeTimeout();
        // 日志
        int execResult = commandExec.exec(officeTransCmd, timeout, log::info);
        log.info("execResult:" + execResult);
        if (execResult != 0) {
            return null;
        }
        String pdfPath = outDir + SEPARATOR + FilenameUtils.getBaseName(filePath) + ".pdf";
        log.info("pdfPath:" + pdfPath);
        return transPdfByAbsPath(pdfPath);


    }

    @Override
    public File transPdf(String filePath) {
        return transPdfByAbsPath(sysConfig.getPhysicalPath(filePath));
    }

    private File transPdfByAbsPath(String absFilePath) {
        File needTransPdfFile = new File(absFilePath);
        absFilePath = needTransPdfFile.getParent() + SEPARATOR + StringUtil.newId() + SEPARATOR + "index.pdf";
        try {
            FileUtils.moveFile(
                needTransPdfFile,
                new File(absFilePath)
            );
            needTransPdfFile = new File(absFilePath);
        } catch (IOException e) {
            throw new BusinessException(FileErrorNoEnum.FILE_OPERATION_FAIL);
        }
        String id = StringUtil.newId();
        String cmdDestDit = StringUtils
            .replace(StringUtils.replace(needTransPdfFile.getParent(), "\\", SEPARATOR), SEPARATOR, File.separator);
        Pdf2htmlRequestParams params =
            Pdf2htmlRequestParams.builder().zoom(1.5).fitWidth(1200).pageFileName(id + "-%d.html").destDir(cmdDestDit)
                .dataDir(config.getDataDir()).build();
        String pdfTransCmd = CmdUtil.buildPdfTransCmd(config.getBin(), params, needTransPdfFile.getAbsolutePath());
        // 日志
        int execResult = commandExec.exec(pdfTransCmd, log::info);

        if (execResult != 0) {
            return null;
        }

        // 打包zip
        String zipPath = cmdDestDit + ".zip";
        ZipCompressorUtil zc = new ZipCompressorUtil(zipPath);
        zc.compress(FileUtil.buildParams(cmdDestDit));
        return new File(zipPath);
    }

    @Override
    public File transVideo(String filePath) {
        return null;
    }
}
