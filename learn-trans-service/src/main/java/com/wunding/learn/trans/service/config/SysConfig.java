package com.wunding.learn.trans.service.config;

import com.wunding.learn.trans.api.enums.OfficeTransTypeEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 配置文件读取
 *
 * <AUTHOR>
 * @date 2022/2/28
 */
@Configuration
@ConfigurationProperties("app")
@Data
@Slf4j
public class SysConfig {

    private static final String SEPARATOR = "/";

    /**
     * 上传目录的系统绝对路径
     */
    private String location;
    /**
     * 上传目录
     */
    private String root;

    /**
     * 转码服务器地址
     */
    private String transCodingUrl;

    /**
     * ffmpeg执行插件
     */
    private String ffmpegPath;

    /**
     * ffmpeg性能优化参数：cpu-nice
     */
    private String ffmpegPerformanceCpuNice;

    /**
     * ffmpeg性能优化参数：threads
     */
    private String ffmpegPerformanceThreads;


    /**
     * office转码类型：{@link OfficeTransTypeEnum}
     */
    private Integer officeTransType;


    public String getPhysicalPath(String path) {
        String physicalPath = location;
        if (StringUtils.endsWith(physicalPath, SEPARATOR)) {
            physicalPath = physicalPath.substring(0, physicalPath.length() - 1);
        }
        if (!StringUtils.startsWith(path, SEPARATOR)) {
            physicalPath = physicalPath + SEPARATOR + path;
        } else {
            physicalPath = physicalPath + path;
        }
        return physicalPath;
    }




}
