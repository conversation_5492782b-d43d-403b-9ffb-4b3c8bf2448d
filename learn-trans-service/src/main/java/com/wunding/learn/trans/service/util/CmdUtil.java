package com.wunding.learn.trans.service.util;


import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.trans.service.pdf.pojo.Pdf2htmlRequestParams;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.OutputStream;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeoutException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.exec.CommandLine;
import org.apache.commons.exec.DefaultExecutor;
import org.apache.commons.exec.ExecuteStreamHandler;
import org.apache.commons.exec.ExecuteWatchdog;
import org.apache.commons.exec.PumpStreamHandler;

/**
 * 命令生成工具
 * <AUTHOR>
 */
@Slf4j
public class CmdUtil {

    private CmdUtil(){

    }

    /**
     * office 转换命令生成工具
     *
     * @param cmd      命令
     * @param outDir   转码之后的文件输出路径
     * @param filePath 待转吗文件绝对路径
     */
    public static String buildOfficeTransCmd(String cmd, String outDir, String filePath) {
        String osName = System.getProperty("os.name");
        String tmpPath = "tmp/" + StringUtil.newId();
        if(osName.contains("Windows")){
            tmpPath = "\"C:/" + tmpPath + "\"";
        }
        return cmd + " --headless --convert-to pdf:writer_pdf_Export " + filePath + " --outdir " + outDir
            + " -env:UserInstallation=file:///"+tmpPath;
    }

    /**
     * word转jpg格式图片
     *
     * @param cmd      命令
     * @param outDir   转码之后的文件输出路径
     * @param filePath 待转吗文件绝对路径
     */
    public static String[] transDocxToImageCmd(String cmd, String outDir, String filePath) {
        return List.of(cmd, "--headless", "--convert-to", "jpg", filePath, "--outdir", outDir,
            "-env:UserInstallation=file:///tmp/" + StringUtil.newId()).toArray(new String[]{});
    }

    /**
     * pdf 转换命令生成工具
     */
    public static String buildPdfTransCmd(String cmd, Pdf2htmlRequestParams params, String pdfPath) {
        return cmd + " " + pdfPath + " " + paramsObjToCommand(params) + " ";
    }

    /**
     * 参数据对象转执行命令
     */
    private static String paramsObjToCommand(Pdf2htmlRequestParams params) {

        StringBuilder sb = new StringBuilder();
        sb.append(getParamStr("first-page", params.getFirstPage()));
        sb.append(getParamStr("last-page", params.getLastPage()));
        sb.append(getParamStr("zoom", params.getZoom()));
        sb.append(getParamStr("fit-width", params.getFitWidth()));
        sb.append(getParamStr("fit-height", params.getFitHeight()));
        sb.append(getParamStr("use-cropbox", params.getUseCropBox()));
        sb.append(getParamStr("embed-css", params.getEmbedCss()));
        sb.append(getParamStr("embed-font", params.getEmbedFont()));
        sb.append(getParamStr("embed-image", params.getEmbedImage()));
        sb.append(getParamStr("embed-javascript", params.getEmbedJavascript()));
        sb.append(getParamStr("embed-outline", params.getEmbedOutline()));
        sb.append(getParamStr("split-pages", params.getSplitPages()));
        sb.append(getParamStr("dest-dir", params.getDestDir()));
        sb.append(getParamStr("css-filename", params.getCssFileName()));
        sb.append(getParamStr("page-filename", params.getPageFileName()));
        sb.append(getParamStr("outline-filename", params.getOutlineFileName()));
        sb.append(getParamStr("process-nontext", params.getProcessNonText()));
        sb.append(getParamStr("process-outline", params.getProcessOutline()));
        sb.append(getParamStr("printing", params.getPrinting()));
        sb.append(getParamStr("fallback", params.getFallback()));
        sb.append(getParamStr("embed-external-font", params.getEmbedExternalFont()));
        sb.append(getParamStr("font-format", params.getFontFormat()));
        sb.append(getParamStr("decompose-ligature", params.getDecomposeLigature()));
        sb.append(getParamStr("auto-hint", params.getAutoHint()));
        sb.append(getParamStr("external-hint-tool", params.getExternalHintTool()));
        sb.append(getParamStr("stretch-narrow-glyph", params.getStretchNarrowGlyph()));
        sb.append(getParamStr("squeeze-wide-glyph", params.getSqueezeWideGlyph()));
        sb.append(getParamStr("override-fstype", params.getOverrideFsType()));
        sb.append(getParamStr("process-type3", params.getProcessTpype3()));
        sb.append(getParamStr("heps", params.getHeps()));
        sb.append(getParamStr("veps", params.getVeps()));
        sb.append(getParamStr("space-threshold", params.getSpaceThreshold()));
        sb.append(getParamStr("font-size-multiplier", params.getFontSizeMultiplier()));
        sb.append(getParamStr("space-as-offset", params.getSpaceAsOffset()));
        sb.append(getParamStr("tounicode", params.getTounicode()));
        sb.append(getParamStr("optimize-text", params.getOptimizeText()));
        sb.append(getParamStr("bg-format", params.getBgFormat()));
        sb.append(getParamStr("owner-password", params.getOwnerPassword()));
        sb.append(getParamStr("user-password", params.getUserPassword()));
        sb.append(getParamStr("no-drm", params.getNoDrm()));
        sb.append(getParamStr("clean-tmp", params.getCleanTemp()));
        sb.append(getParamStr("data-dir", params.getDataDir()));
        sb.append(getParamStr("proof", params.getProof()));
        sb.append(getParamStr("correct-text-visibility", "0"));

        return sb.toString();
    }

    private static String getParamStr(String key, Object value) {
        if (value != null) {
            return " --" + key + " " + value;
        }
        return "";
    }

    /**
     * @param line    命令行
     * @param timeOut -1 表示不超时
     * @param workDir workdir，可以为null
     * @param stdout  可以为空
     * @param error   可以为空
     *
     *                <p>
     *                https://segmentfault.com/a/1190000039782685
     *                <p>
     *                做到第三步
     */
    public static void processRun(String line, long timeOut, File workDir, OutputStream stdout, OutputStream error) {
        CommandLine commandline = CommandLine.parse(line);

        OutputStream outputStream = new ByteArrayOutputStream();
        OutputStream errorStream = new ByteArrayOutputStream();
        if (Objects.nonNull(stdout)) {
            outputStream = stdout;
        }

        if (Objects.nonNull(error)) {
            errorStream = stdout;
        }

        try {
            DefaultExecutor exec = new DefaultExecutor();
            exec.setExitValues(null);
            ExecuteWatchdog watchdog = new ExecuteWatchdog(timeOut);
            exec.setWatchdog(watchdog);
            if (Objects.nonNull(workDir)) {
                exec.setWorkingDirectory(workDir);
            }
            ExecuteStreamHandler streamHandler = new PumpStreamHandler(outputStream, errorStream);

            exec.setStreamHandler(streamHandler);
            int ret = exec.execute(commandline);

            if (ret != 0) {
                if (watchdog.killedProcess()) {
                    // 只在linux上验证过
                    throw new TimeoutException(
                        String.format("Timed out waiting for to finish. Wait %d ms. return code %d", timeOut, ret));
                }

                if (errorStream != null) {
                    log.error("arguments: {} , result code: {}, error info: {}",
                        String.join(" ", commandline.getArguments()),
                        ret,
                        errorStream);
                }

            }

        } catch (Exception e) {
            log.error(commandline.getExecutable(), e);
        }

    }


}
