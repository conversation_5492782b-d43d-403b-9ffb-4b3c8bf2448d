package com.wunding.learn.trans.service.rpc;

import com.wunding.learn.common.enums.file.TranscodeStatusEnum;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.file.trans.grpc.lib.TransReply;
import com.wunding.learn.file.trans.grpc.lib.TransReply.Builder;
import com.wunding.learn.file.trans.grpc.lib.TransRequest;
import com.wunding.learn.file.trans.grpc.lib.TransServiceGrpc.TransServiceImplBase;
import com.wunding.learn.file.trans.grpc.lib.TransWaterMarkImageReply;
import com.wunding.learn.file.trans.grpc.lib.TransWaterMarkImageRequest;
import com.wunding.learn.file.trans.grpc.lib.VideoClarity;
import com.wunding.learn.trans.api.callback.AsyncCallback;
import com.wunding.learn.trans.api.enums.ReplyType;
import com.wunding.learn.trans.service.ob.CallbackObservable;
import com.wunding.learn.trans.service.service.impl.AudioExtractText;
import com.wunding.learn.trans.service.service.impl.AudioTransServiceImpl;
import com.wunding.learn.trans.service.service.impl.MpsVideoTransServiceImpl;
import com.wunding.learn.trans.service.service.impl.OfficeTransServiceImpl;
import com.wunding.learn.trans.service.service.impl.PdfExtractText;
import com.wunding.learn.trans.service.service.impl.PdfTransServiceImpl;
import com.wunding.learn.trans.service.service.impl.PptExtractText;
import com.wunding.learn.trans.service.service.impl.VideoTransServiceImpl;
import com.wunding.learn.trans.service.service.impl.WordExtractText;
import io.grpc.stub.StreamObserver;
import jakarta.annotation.Resource;
import java.io.IOException;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import net.devh.boot.grpc.server.service.GrpcService;
import org.apache.commons.io.FilenameUtils;

/**
 * <AUTHOR>
 */
@Slf4j
@GrpcService
public class TransRpcService extends TransServiceImplBase {

    @Resource
    private AudioExtractText audioExtractText;

    @Resource
    private AudioTransServiceImpl audioTransService;

    @Resource
    private OfficeTransServiceImpl officeTransService;

    @Resource
    private PdfExtractText pdfExtractText;

    @Resource
    private PdfTransServiceImpl pdfTransService;

    @Resource
    private PptExtractText pptExtractText;

    @Resource
    private VideoTransServiceImpl videoTransService;

    @Resource
    private MpsVideoTransServiceImpl mpsVideoTransService;

    @Resource
    private WordExtractText wordExtractText;

    @Resource
    private CallbackObservable callbackObservable;


    @Override
    public void audioTrans(TransRequest request, StreamObserver<TransReply> responseObserver) {
        replyMsg("audio",request,responseObserver);
    }

    @Override
    public void officeTrans(TransRequest request, StreamObserver<TransReply> responseObserver) {
        replyMsg("office",request,responseObserver);
    }

    @Override
    public void pdfTrans(TransRequest request, StreamObserver<TransReply> responseObserver) {
        replyMsg("pdf",request,responseObserver);
    }

    @Override
    public void videoTrans(TransRequest request, StreamObserver<TransReply> responseObserver) {
        replyMsg("video",request,responseObserver);
    }

    @Override
    public void mpsVideoTrans(TransRequest request, StreamObserver<TransReply> responseObserver) {
        replyMsg("mpsVideo",request,responseObserver);
    }

    @Override
    public void audioExtractText(TransRequest request, StreamObserver<TransReply> responseObserver) {
        replyMsg("audioExtractText",request,responseObserver);
    }

    @Override
    public void pdfExtractText(TransRequest request, StreamObserver<TransReply> responseObserver) {
        replyMsg("pdfExtractText",request,responseObserver);

    }

    @Override
    public void wordExtractText(TransRequest request, StreamObserver<TransReply> responseObserver) {
        replyMsg("wordExtractText",request,responseObserver);
    }

    @Override
    public void pptExtractText(TransRequest request, StreamObserver<TransReply> responseObserver) {
        replyMsg("pptExtractText",request,responseObserver);
    }

    @Override
    public void transWaterMarkImage(TransWaterMarkImageRequest request,
        StreamObserver<TransWaterMarkImageReply> responseObserver) {
        try {
            String waterMarkImage = officeTransService.transWaterMarkImage(request.getTempDir(),
                request.getSourcePath(), request.getNewImageName());
            responseObserver.onNext(TransWaterMarkImageReply.newBuilder().setWaterMarkImgPath(waterMarkImage).build());
            responseObserver.onCompleted();
        } catch (IOException e) {
            responseObserver.onError(e);
        }
    }



    private void addListener(String transId,StreamObserver<TransReply> responseObserver){
        callbackObservable.addListener(transId, new AsyncCallback() {
            @Override
            public void beginExec() {
                responseObserver.onNext(
                    TransReply.newBuilder()
                        .setStatus(TranscodeStatusEnum.TRANSFORMING.value)
                        .setLog("开始转码")
                        .setType("action")
                        .setTransId(transId)
                        .build()
                );
            }
            @Override
            public void logEvent(String log) {
                responseObserver.onNext(
                    TransReply.newBuilder()
                        .setStatus(TranscodeStatusEnum.TRANSFORMING.value)
                        .setLog(log)
                        .setType("log")
                        .setTransId(transId)
                        .build()
                );
            }
            @Override
            public void finish(TransReply transReply) {
                log.info("transReply status:{}, filePath:{}",transReply.getStatus(),transReply.getFilePath());
            }
        });
    }

    private void replyMsg(String type,TransRequest request,StreamObserver<TransReply> responseObserver){
        String transId = StringUtil.newId();
        addListener(transId,responseObserver);
        try {
            Builder builder = TransReply.newBuilder();
            String filePath = "";
            switch (type) {
                case "audio" -> {
                    filePath = audioTransService.doTrans(request);
                    builder.setFilePath(filePath);
                }
                case "office" -> {
                    filePath = officeTransService.doTrans(request);
                    builder.setFilePath(filePath);
                }
                case "pdf" -> {
                    filePath = pdfTransService.doTrans(request);
                    builder.setFilePath(filePath);
                }
                case "video" -> {
                    String outMp3Path = FilenameUtils.getPath(request.getFilePath()) + FilenameUtils.getBaseName(
                        request.getFilePath()) + ".mp3";
                    builder
                        .setTransId(transId)
                        .setType(ReplyType.ACTION.name())
                        .setStatus(TranscodeStatusEnum.TRANSFORMED.value)
                        .setMp3FilePath(outMp3Path);
                    List<VideoClarity> videoClaritieList = videoTransService.doTrans(request);
                    if (!videoClaritieList.isEmpty()) {
                        builder.setFilePath(videoClaritieList.get(0).getUrl())
                            .addAllVideoClarity(videoClaritieList);
                    } else {
                        builder.setFilePath(request.getFilePath());
                    }
                    builder.addAllVideoClarity(videoClaritieList);
                }
                case "mpsVideo" -> {
                    String outMp3Path = FilenameUtils.getPath(request.getFilePath()) + FilenameUtils.getBaseName(
                        request.getFilePath()) + ".mp3";
                    builder
                        .setTransId(transId)
                        .setType(ReplyType.ACTION.name())
                        .setStatus(TranscodeStatusEnum.TRANSFORMED.value)
                        .setMp3FilePath(outMp3Path);
                    List<VideoClarity> videoClaritieList = mpsVideoTransService.doTrans(request);
                    if (!videoClaritieList.isEmpty()) {
                        builder.setFilePath(videoClaritieList.get(0).getUrl())
                            .addAllVideoClarity(videoClaritieList);
                    } else {
                        builder.setFilePath(request.getFilePath());
                    }
                    builder.addAllVideoClarity(videoClaritieList);
                }
                case "audioExtractText" -> {
                    filePath = audioExtractText.extractText(request.getFilePath());
                    builder.setExtractTextPath(filePath);
                }
                case "pdfExtractText" -> {
                    filePath = pdfExtractText.extractText(request.getFilePath());
                    builder.setExtractTextPath(filePath);
                }
                case "wordExtractText" -> {
                    filePath = wordExtractText.extractText(request.getFilePath());
                    builder.setExtractTextPath(filePath);
                }
                case "pptExtractText" -> {
                    filePath = pptExtractText.extractText(request.getFilePath());
                    builder.setExtractTextPath(filePath);
                }
                default -> log.warn("Unexpected value: {}" , type);
            }
            builder.setTransId(transId)
                .setType(ReplyType.ACTION.name())
                .setStatus(TranscodeStatusEnum.TRANSFORMED.value);
            TransReply transReply = builder.build();
            responseObserver.onNext(
                transReply
            );
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("type:{} 转码出错,filePath:{},id:{},bucketName:{},tempFilePath:{},转码出错",
                type, request.getFilePath(), request.getId(), request.getBucketName(), request.getTempFilePath(), e);
            Builder builder = TransReply.newBuilder();
            builder.setExtractTextPath("");
            builder.setFilePath("");
            builder.setTransId(transId)
                .setType(ReplyType.ACTION.name())
                .setStatus(TranscodeStatusEnum.TRANSFORM_FAILED.value);
            TransReply transReply = builder.build();
            responseObserver.onNext(
                transReply
            );
            responseObserver.onCompleted();
        }
    }
}
