package com.wunding.learn.trans.service.util;

import com.aliyun.mts20140618.Client;
import com.aliyun.mts20140618.models.QueryJobListRequest;
import com.aliyun.mts20140618.models.QueryJobListResponse;
import com.aliyun.mts20140618.models.QueryJobListResponseBody;
import com.aliyun.mts20140618.models.QueryJobListResponseBody.QueryJobListResponseBodyJobListJob;
import com.aliyun.mts20140618.models.SearchPipelineRequest;
import com.aliyun.mts20140618.models.SearchPipelineResponse;
import com.aliyun.mts20140618.models.SearchPipelineResponseBody.SearchPipelineResponseBodyPipelineListPipeline;
import com.aliyun.mts20140618.models.SearchTemplateRequest;
import com.aliyun.mts20140618.models.SearchTemplateResponse;
import com.aliyun.mts20140618.models.SearchTemplateResponseBody.SearchTemplateResponseBodyTemplateListTemplate;
import com.aliyun.mts20140618.models.SubmitJobsRequest;
import com.aliyun.mts20140618.models.SubmitJobsResponse;
import com.aliyun.mts20140618.models.SubmitJobsResponseBody.SubmitJobsResponseBodyJobResultListJobResult;
import com.aliyun.tea.TeaException;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.wunding.learn.common.util.json.JsonUtil;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */
@Slf4j
public class MpsUtil {

    public static final String REFERER = "Referer";

    private MpsUtil(){

    }

    private static final String BOOLEAN_FALSE = "false";
    private static final String BOOLEAN_TRUE = "false";

    /**
     * <b>description</b> :
     * <p>使用AK&amp;SK初始化账号Client</p>
     * @return Client
     *
     * @throws Exception
     */
    public static Client createClient(String accessKey,String secret) throws Exception {

        Config config = new Config()
            .setAccessKeyId(accessKey)
            .setAccessKeySecret(secret);
        config.endpoint = "mts.cn-shenzhen.aliyuncs.com";
        return new Client(config);
    }

    public static List<SearchPipelineResponseBodyPipelineListPipeline> getPipeline(String accessKeyId,
        String accessKeySecret) throws Exception {

        List<SearchPipelineResponseBodyPipelineListPipeline> list = new ArrayList<>();

        Client client = createClient(accessKeyId,accessKeySecret);

        SearchPipelineRequest request = new SearchPipelineRequest()
            //需要搜索的管道状态
            .setState("Active")
            //分页查询时设置的每页行数大小
            .setPageSize(100L)
            //当前页号
            .setPageNumber(1L);
        RuntimeOptions runtime = new RuntimeOptions();

        SearchPipelineResponse response = client.searchPipelineWithOptions(request, runtime);
        for (SearchPipelineResponseBodyPipelineListPipeline pipeline : response.getBody()
            .getPipelineList().getPipeline()) {
            log.info("pipeline id:{},name:{},state:{}",pipeline.getId(),pipeline.getName(),pipeline.getState());
            list.add(pipeline);
        }

        return list;
    }

    public static List<SearchTemplateResponseBodyTemplateListTemplate> getTemplate(String accessKeyId,String accessKeySecret,String format,String rate) throws Exception {


        List<SearchTemplateResponseBodyTemplateListTemplate> list = new ArrayList<>();

        Client client = createClient(accessKeyId,accessKeySecret);

        SearchTemplateRequest request = new SearchTemplateRequest();
        request.setState("Normal");
        request.setNamePrefix(format + "_" + rate);

        SearchTemplateResponse response = client.searchTemplate(request);

        if(response.getBody().getTemplateList().getTemplate().isEmpty()){
            log.info("createTemplate begin searchTemplate");
            createTemplate(accessKeyId,accessKeySecret,format,rate);
            response = client.searchTemplate(request);
        }

        for (SearchTemplateResponseBodyTemplateListTemplate template : response.getBody().getTemplateList().getTemplate()) {
            log.info("getTemplate id:{},name:{},state:{}",template.getId(),template.getName(),template.getState());
            list.add(template);
        }
        log.info("getTemplate result:{}",JsonUtil.objToJson(list));
        return list;
    }

    public static void createTemplate(String accessKeyId,String accessKeySecret,String format,String rate) throws Exception {
        Client client = createClient(accessKeyId,accessKeySecret);
        Map<String, Object> transConfig = Map.of(
            "IsCheckAudioBitrate",BOOLEAN_TRUE,
            "IsCheckAudioBitrateFail",BOOLEAN_FALSE,
            "IsCheckVideoBitrateFail",BOOLEAN_FALSE,
            "IsCheckReso",BOOLEAN_TRUE,
            "IsCheckVideoBitrate",BOOLEAN_TRUE,
            "TransMode","onepass",
            "IsCheckResoFail",BOOLEAN_FALSE
        );
        Map<String, Object> muxConfig = Map.of(
            "Segment",Map.of("Duration","10")
        );
        Map<String, Object> container = Map.of("Format",format);
        Map<String, Object> video = Map.of(
            "Codec","H.264",
            "LongShortMode",BOOLEAN_TRUE,
            "Gop","10s",
            "Remove",BOOLEAN_FALSE,
            "PixFmt","",
            "Bitrate","-1",
            "Width",rate,
            "Profile","high" //,
            //"ScanMode",null
        );

        Map<String, Object> audio = Map.of(
            "Codec","AAC",
//            "Volume",null,
            "Remove",BOOLEAN_FALSE,
            "Samplerate","44100",
            "Profile","aac_low",
            "Channels","2"
        );

        com.aliyun.mts20140618.models.AddTemplateRequest addTemplateRequest = new com.aliyun.mts20140618.models.AddTemplateRequest()
            //模板名称
            .setName(format+"_"+rate)
            //容器
            .setContainer(JsonUtil.objToJson(container))
            //视频流配置
            .setVideo(JsonUtil.objToJson(video))
            //音频流配置
            .setAudio(JsonUtil.objToJson(audio))
            //转码通用配置
            .setTransConfig(JsonUtil.objToJson(transConfig))
            //切片配置字段
            .setMuxConfig(JsonUtil.objToJson(muxConfig));
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        client.addTemplateWithOptions(addTemplateRequest, runtime);
    }

    @SuppressWarnings("java:S3776")
    public static List<String> submitJobs(
        String accessKeyId,
        String accessKeySecret,
        String bucket,
        String inputObject,
        String outObject,
        String format,
        String rate
    ) throws Exception {

        List<String> jobIds = new ArrayList<>();


        Client client = createClient(accessKeyId,accessKeySecret);


        // https://wd-saas-sit-bucket.oss-cn-shenzhen.aliyuncs.com/wunding/file/202409/CourseWareFile/20240718170949085604364d474ba287ece3.mp4

        Map<String, Object> inputMap = new HashMap<>();
        inputMap.put("Bucket", bucket);
        inputMap.put("Location", "oss-cn-shenzhen");
        inputMap.put("Object", inputObject);
        String k8SNamespace = System.getenv("K8S_NAMESPACE");
        if (Objects.equals(k8SNamespace,"saas-uat")) {
            inputMap.put(REFERER, "https://saasdev.oss.wdxuexi.com");
        }else if(Objects.equals(k8SNamespace,"saas-sit")){
            inputMap.put(REFERER, "https://saassit.oss.wdxuexi.com");
        }else if(Objects.equals(k8SNamespace,"saas-prod")){
            inputMap.put(REFERER, "https://saasprod.oss.wdxuexi.com");
        }else {
            inputMap.put(REFERER, "*");
        }

        String inputJson = JsonUtil.objToJson(inputMap);

        Map<String,Object> outputMap = new HashMap<>();
        outputMap.put("OutputObject", outObject);

        List<SearchTemplateResponseBodyTemplateListTemplate> template = getTemplate(accessKeyId, accessKeySecret, format, rate);
        String templateId = template.get(0).getId();
        outputMap.put("TemplateId", templateId);

        String outputJson = JsonUtil.objToJson(List.of(outputMap));

        List<SearchPipelineResponseBodyPipelineListPipeline> pipeline = getPipeline(accessKeyId, accessKeySecret);
        if(pipeline.isEmpty()){
            return new ArrayList<>();
        }
        String pipelineId = pipeline.get(0).getId();

        SubmitJobsRequest submitJobsRequest = new SubmitJobsRequest()
            //作业输入
            .setInput(inputJson)
            //作业输出配置
            .setOutputs(outputJson)
            //输出文件所在的OSS Bucket
            .setOutputBucket(bucket)
            //输出文件所在的 OSS Bucket 的地域（OSS Region）
            .setOutputLocation("oss-cn-shenzhen")
            //管道ID
            .setPipelineId(pipelineId);
        log.info("submitJobsRequest:{}",JsonUtil.objToJson(submitJobsRequest));
        RuntimeOptions runtime = new RuntimeOptions();
        try {
            // 复制代码运行请自行打印 API 的返回值
            SubmitJobsResponse response = client.submitJobsWithOptions(submitJobsRequest, runtime);
            for (SubmitJobsResponseBodyJobResultListJobResult jobResult : response.getBody()
                .getJobResultList().getJobResult()) {
                jobIds.add(jobResult.getJob().getJobId());
            }
        } catch (TeaException error) {
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            log.error("message:{},Recommend:{}",error.getMessage(),error.getData().get("Recommend"),error);
        } catch (Exception e) {
            TeaException error = new TeaException(e.getMessage(), e);
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            log.error("message:{},Recommend:{}",error.getMessage(),error.getData().get("Recommend"),e);
        }
        int count = 0;
        while (count < 3 * 60 * 60){
            boolean done = true;
            List<QueryJobListResponseBodyJobListJob> jobs = queryJobList(accessKeyId,accessKeySecret,jobIds);
            for (QueryJobListResponseBodyJobListJob job : jobs) {
                log.info(JsonUtil.objToJson(job));

                // 已提交 或 转码中 算未完成
                if("Submitted".equals(job.getState()) || "Transcoding".equals(job.getState())){
                    done = false;
                }
            }
            if(done){
                break;
            }
            Thread.sleep(1000);
            count++;
        }
        return jobIds;
    }

    public static List<QueryJobListResponseBodyJobListJob> queryJobList(
        String accessKeyId,
        String accessKeySecret,
        Collection<String> jobIds
    ) throws Exception {


        List<QueryJobListResponseBodyJobListJob> list = new ArrayList<>();

        Client client = createClient(accessKeyId,accessKeySecret);

        QueryJobListRequest request = new QueryJobListRequest();
        request.setJobIds(StringUtils.join(jobIds, ","));
        RuntimeOptions runtime = new RuntimeOptions();

        QueryJobListResponse response = client.queryJobListWithOptions(request, runtime);
        for (QueryJobListResponseBody.QueryJobListResponseBodyJobListJob job : response.getBody().getJobList().getJob()) {
            log.info("id:{},message:{},state:{}",job.getJobId(),job.getMessage(),job.getState());
            list.add(job);
        }

        return list;
    }



}
