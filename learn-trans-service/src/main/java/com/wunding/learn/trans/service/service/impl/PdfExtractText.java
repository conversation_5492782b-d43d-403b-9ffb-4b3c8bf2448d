package com.wunding.learn.trans.service.service.impl;

import com.wunding.learn.common.mq.service.MqProducer;
import com.wunding.learn.trans.service.config.SysConfig;
import com.wunding.learn.trans.service.dto.ExtractTextResult;
import com.wunding.learn.trans.service.service.ExtractText;
import io.minio.PutObjectArgs;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.pdfbox.text.PDFTextStripperByArea;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component("pdfExtractText")
public class PdfExtractText implements ExtractText {


    @Resource
    private SysConfig sysConfig;



    @Override
    public String extractText(String filePath) throws IOException{


        File needTransFile = new File(sysConfig.getPhysicalPath(filePath));
        try (PDDocument document = PDDocument.load(needTransFile)) {

            if (!document.isEncrypted()) {

                PDFTextStripperByArea stripper = new PDFTextStripperByArea();

                stripper.setSortByPosition(true);

                PDFTextStripper tStripper = new PDFTextStripper();

                String text = tStripper.getText(document);
                // 文本为空不做处理
                if (StringUtils.isBlank(text)) {
                    text = "";
                }


                String outTextFilePath = FilenameUtils.getPath(filePath) + FilenameUtils.getBaseName(filePath) + ".txt";
                File file = new File(sysConfig.getPhysicalPath(outTextFilePath));
                if (!file.exists()) {
                    boolean newFileStatus = file.createNewFile();
                    log.info("createNewFile {}",newFileStatus);
                }

                IOUtils.write(text,new FileWriter(file));

                return outTextFilePath;

            }
        } catch (IOException e) {
            log.error("pdf extractText error ",e);
        }
        return "";
    }

}
