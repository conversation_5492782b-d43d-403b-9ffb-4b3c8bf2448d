package com.wunding.learn.trans.service.service.impl;

import com.wunding.learn.common.constant.trans.TransErrorNumEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.redis.util.RedisLockUtil;
import com.wunding.learn.common.util.string.StringPool;
import com.wunding.learn.file.trans.grpc.lib.TransRequest;
import com.wunding.learn.trans.service.config.SysConfig;
import com.wunding.learn.trans.service.util.FileParseUtil;
import com.wunding.learn.trans.service.util.FileUtil;
import java.io.File;
import java.io.IOException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class AudioTransServiceImpl {


    @Resource
    private SysConfig sysConfig;


    /**
     * 音频转码
     *
     * @throws Exception 异常信息
     */
    public String doTrans(TransRequest transRequest) throws Exception {

        String key = "trans:file:"+transRequest.getFilePath();
        //音频文件30分钟等待时间，超过30分钟，则认为转码失败
        if (RedisLockUtil.acquire(key, 1800, 1800)) {
            try {

                // 原文件

                File needTransAudioFile = new File(sysConfig.getPhysicalPath(transRequest.getTempFilePath()));

                String needTransFileCurrentPath = transRequest.getFilePath();

                File needTransCurrentFile = new File(sysConfig.getPhysicalPath(needTransFileCurrentPath));
                if(!needTransCurrentFile.exists()){
                    try {
                        FileUtils.copyFile(needTransAudioFile, needTransCurrentFile);
                    } catch (IOException e) {
                        log.error("pdf copy error", e);
                    }
                }

                // 转码后文件路径
                String newFileName = needTransAudioFile.getName().substring(0, needTransAudioFile.getName().lastIndexOf(StringPool.DOT))
                    .concat(".mp3");
                String relativePath = needTransFileCurrentPath.substring(0, needTransFileCurrentPath.lastIndexOf(StringPool.SLASH))
                    .concat(StringPool.SLASH + "new" + StringPool.SLASH);

                FileUtil.mkdir(sysConfig.getPhysicalPath(relativePath));
                String targetFilePath = relativePath.concat(newFileName);
                String targetAllPath = sysConfig.getPhysicalPath(targetFilePath);
                // 防止重复转码
                File targetFile = new File(targetAllPath);
                if (!targetFile.exists()) {
                    FileParseUtil.convertAudioToMp3(needTransAudioFile.getAbsolutePath(), targetAllPath);
                }

                return relativePath+newFileName;
            }finally {
                RedisLockUtil.release(key);
            }
        }else{
            log.error("音频转码失败，等待超时,transRequest.FilePath={}, transRequest.Id={}, transRequest.BucketName={}, transRequest.TempFilePath={}",
                transRequest.getFilePath(), transRequest.getId(), transRequest.getBucketName(), transRequest.getTempFilePath());
            throw new BusinessException(TransErrorNumEnum.WAITING_FOR_TIMEOUT);
        }
    }


}
