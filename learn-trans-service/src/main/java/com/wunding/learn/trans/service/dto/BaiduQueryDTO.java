package com.wunding.learn.trans.service.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;


/**
 * 百度音频转写任务查询结果dto
 *
 * <AUTHOR>
 * @date 2023/06/07
 */
@Data
@Accessors(chain = true)
@Schema(name = "BaiduQueryDTO", description = "百度音频转写任务查询结果dto")
public class BaiduQueryDTO {

    /**
     * 错误代码
     */
    @Schema(description = "错误码")
    @JsonProperty("error_code")
    private Integer errorCode;

    /**
     * 错误消息
     */
    @Schema(description = "错误信息")
    @JsonProperty("error_msg")
    private Integer errorMsg;

    /**
     * 错误的或查询不存在的taskId
     */
    @Schema(description = "错误的或查询不存在的taskid数组")
    @JsonProperty("error_info")
    private List<String> errorInfo;

    /**
     * 日志id
     */
    @Schema(description = "日志id")
    @JsonProperty("log_id")
    private String logId;

    /**
     * 任务信息
     */
    @Schema(description = "任务信息")
    @JsonProperty("tasks_info")
    private List<TaskInfo> tasksInfo;


    @Data
    @Schema(name = "TaskInfo", description = "任务信息")
    public static class TaskInfo {

        /**
         * 任务状态
         */
        @Schema(description = "任务状态")
        @JsonProperty("task_status")
        private String taskStatus;

        /**
         * 任务结果
         */
        @Schema(description = "任务结果")
        @JsonProperty("task_result")
        private TaskResult taskResult;

        /**
         * 任务id
         */
        @Schema(description = "任务id")
        @JsonProperty("task_id")
        private String taskId;
    }

    @Data
    @Schema(name = "TaskResult", description = "任务结果")
    public static class TaskResult {

        /**
         * 转写结果
         */
        @Schema(description = "转写结果")
        @JsonProperty("result")
        private List<String> result;

        /**
         * 音频时长（毫秒）
         */
        @Schema(description = "音频时长（毫秒）")
        @JsonProperty("audio_duration")
        private Long audioDuration;

        /**
         * 转写详细结果
         */
        @Schema(description = "转写详细结果")
        @JsonProperty("detailed_result")
        private List<DetailedResult> detailedResult;

        /**
         *
         */
        @Schema(description = "无")
        @JsonProperty("corpus_no")
        private String corpusNo;
    }

    @Data
    @Schema(name = "DetailedResult", description = "任务结果")
    public static class DetailedResult {

        /**
         * 一小段文本
         */
        @Schema(description = "一小段文本")
        @JsonProperty("res")
        private List<String> results;

        /**
         * 文本开始所在音频结束时间
         */
        @Schema(description = "文本开始所在音频结束时间")
        @JsonProperty("end_time")
        private Integer endTime;

        /**
         * 文本开始所在音频开始时间
         */
        @Schema(description = "开始时间")
        @JsonProperty("begin_time")
        private Integer beginTime;

        /**
         *
         */
        @Schema(description = "无")
        @JsonProperty("words_info")
        private List<String> wordsInfo;

        /**
         *
         */
        @Schema(description = "无")
        @JsonProperty("sn")
        private String sn;

        /**
         *
         */
        @Schema(description = "无")
        @JsonProperty("corpus_no")
        private String corpusNo;
    }
}
