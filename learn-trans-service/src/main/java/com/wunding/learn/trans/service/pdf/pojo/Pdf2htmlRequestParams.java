package com.wunding.learn.trans.service.pdf.pojo;

import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2021/7/12
 */
@Builder
@Getter
public class Pdf2htmlRequestParams {

    /**
     * 需要转换的起始页 (默认: 1)
     */
    @Builder.Default
    private final Integer firstPage = 1;

    /**
     * 需要转换的最后一页 (默认: 2147483647)
     */
    @Builder.Default
    private final Integer lastPage = 2147483647;

    /**
     * 缩放比例
     */
    @Builder.Default
    private final Double zoom = 1.5;

    /**
     * 适合宽度  像素
     */
    private final Integer fitWidth;

    /**
     * 适合高度  像素
     */
    private final Integer fitHeight;

    /**
     * 使用剪切框 (default: 1)
     */
    @Builder.Default
    private final Integer useCropBox = 1;

    /**
     * 将CSS文件嵌入到输出中 (default: 1)
     */
    @Builder.Default
    private final Integer embedCss = 0;

    /**
     * 将字体文件嵌入到输出中 (default: 1)
     */
    @Builder.Default
    private final Integer embedFont = 0;

    /**
     * 将图片文件嵌入到输出中 (default: 1)
     */
    @Builder.Default
    private final Integer embedImage = 0;

    /**
     * 将javascript文件嵌入到输出中 (default: 1)
     */
    @Builder.Default
    private final Integer embedJavascript = 1;

    /**
     * 将链接嵌入到输出中 (default: 1)
     */
    @Builder.Default
    private final Integer embedOutline = 1;

    /**
     * 将页面分割为单独的文件 java程序默认是1， 命令默认是0
     */
    @Builder.Default
    private final Integer splitPages = 1;

    /**
     * 指定目标目录
     */
    @Builder.Default
    private final String destDir = "./dist";

    /**
     * 生成的css文件的文件名
     */
    private final String cssFileName;

    /**
     * 分割的网页名称
     */
    private final String pageFileName;

    /**
     * 生成的链接文件名称
     */
    private final String outlineFileName;

    /**
     * 渲染图行，文字除外 (default: 1)
     */
    @Builder.Default
    private final Integer processNonText = 1;

    /**
     * 在html中显示链接 (default: 1)
     */
    @Builder.Default
    private final Integer processOutline = 1;

    /**
     * 支持打印 (default: 1)
     */
    @Builder.Default
    private final Integer printing = 1;

    /**
     * 在备用模式下输出 (default: 0)
     */
    @Builder.Default
    private final Integer fallback = 1;

    /**
     * 嵌入局部匹配的外部字体 (default: 1)
     */
    @Builder.Default
    private final Integer embedExternalFont = 1;

    /**
     * 嵌入的字体文件后缀 (ttf,otf,woff,svg) (default: "woff")
     */
    @Builder.Default
    private final String fontFormat = "woff";

    /**
     * 分解连字-> fi (default:0)
     */
    @Builder.Default
    private final Integer decomposeLigature = 0;

    /**
     * 使用fontforge的autohint上的字体时不提示 (default: 0)
     */
    @Builder.Default
    private final Integer autoHint = 0;

    /**
     * 字体外部提示工具 (overrides --auto-hint) (default: "")
     */
    private final String externalHintTool;

    /**
     * 伸展狭窄的字形，而不是填充 (default: 0)
     */
    @Builder.Default
    private final Integer stretchNarrowGlyph = 0;

    /**
     * 收缩较宽的字形，而不是截断 (default: 1)
     */
    @Builder.Default
    private final Integer squeezeWideGlyph = 1;

    /**
     * clear the fstype bits in TTF/OTF fonts (default:0)
     */
    @Builder.Default
    private final Integer overrideFsType = 0;

    /**
     * convert Type 3 fonts for web (experimental) (default: 0)
     */
    @Builder.Default
    private final Integer processTpype3 = 0;

    /**
     * 合并文本的水平临界值，单位：像素(default: 1)
     */
    @Builder.Default
    private final Integer heps = 1;

    /**
     * vertical threshold for merging text, in pixels (default: 1)
     */
    @Builder.Default
    private final Integer veps = 1;

    /**
     * 断字临界值 (临界值 * em) (default:0.125)
     */
    @Builder.Default
    private final Double spaceThreshold = 1.125;

    /**
     * 一个大于1的值增加渲染精度 (default: 4)
     */
    @Builder.Default
    private final Double fontSizeMultiplier = 4.0;

    /**
     * 把空格字符作为偏移量 (default: 0)
     */
    @Builder.Default
    private final Integer spaceAsOffset = 0;

    /**
     * 如何处理ToUnicode的CMap (0=auto, 1=force,-1=ignore) (default: 0)
     */
    @Builder.Default
    private final Integer tounicode = 0;

    /**
     * 尽量减少用于文本的HTML元素的数目 (default: 0)
     */
    @Builder.Default
    private final Integer optimizeText = 0;

    /**
     * 指定背景图像格式 (default: "png")
     */
    @Builder.Default
    private final String bgFormat = "png";

    /**
     * 所有者密码 (为了加密文件)
     */
    private final String ownerPassword;

    /**
     * 用户密码 (为了加密文件)
     */
    private final String userPassword;

    /**
     * 覆盖文档的 DRM 设置 (default: 0)
     */
    @Builder.Default
    private final Integer noDrm = 1;

    /**
     * 转换后删除临时文件 (default: 1)
     */
    @Builder.Default
    private final Integer cleanTemp = 1;

    /**
     * 指定的数据目录 (default: ".\share\pdf2htmlEX")
     */
    @Builder.Default
    private final String dataDir = "./data";

    /**
     * texts are drawn on both text layer and background for proof (default: 0)
     * 文本在文本图层和背景上绘制以进行校样（默认值：0）
     */
    @Builder.Default
    private final Integer proof = 1;

}
