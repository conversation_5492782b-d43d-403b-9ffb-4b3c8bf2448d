package com.wunding.learn.trans.service.config;

import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.exception.GrpcException;
import com.wunding.learn.common.util.json.JsonUtil;
import io.grpc.Metadata;
import io.grpc.Status;
import io.grpc.StatusException;
import lombok.extern.slf4j.Slf4j;
import net.devh.boot.grpc.server.advice.GrpcAdvice;
import net.devh.boot.grpc.server.advice.GrpcExceptionHandler;

/**
 * <AUTHOR>
 */
@GrpcAdvice
@Slf4j
public class GrpcExceptionAdvice {

    private static final String MESSAGE = "message";
    @GrpcExceptionHandler(Exception.class)
    public StatusException handleException(Exception e) {
        log.error("GrpcExceptionAdvice handleException Exception",e);
        Status status = Status.INTERNAL.withDescription("远程错误").withCause(e);
        Metadata metadata = new Metadata();
        metadata.put(Metadata.Key.of(MESSAGE, Metadata.ASCII_STRING_MARSHALLER),e.getMessage());
        return status.asException(metadata);
    }

    @GrpcExceptionHandler(GrpcException.class)
    public StatusException handleException(GrpcException e) {
        log.error("GrpcExceptionAdvice handleException GrpcException",e);
        Status status = Status.INTERNAL.withDescription("远程错误").withCause(e);
        Metadata metadata = new Metadata();
        metadata.put(Metadata.Key.of("code", Metadata.ASCII_STRING_MARSHALLER),e.getCode()+"");
        String data = JsonUtil.objToJson(e.getData());
        if(data != null){
            metadata.put(Metadata.Key.of("data", Metadata.ASCII_STRING_MARSHALLER),data);
        }
        metadata.put(Metadata.Key.of(MESSAGE, Metadata.ASCII_STRING_MARSHALLER),e.getMessage());
        return status.asException(metadata);
    }

    @GrpcExceptionHandler(BusinessException.class)
    public StatusException handleBusinessException(BusinessException e) {
        log.error("GrpcExceptionAdvice handleException GrpcException",e);
        Status status = Status.INTERNAL.withDescription("远程错误").withCause(e);
        Metadata metadata = new Metadata();
        metadata.put(Metadata.Key.of("code", Metadata.ASCII_STRING_MARSHALLER),e.getErrorCode()+"");
        metadata.put(Metadata.Key.of(MESSAGE, Metadata.ASCII_STRING_MARSHALLER),e.getMessage());
        String data = JsonUtil.objToJson(e.getData());
        if(data != null){
            metadata.put(Metadata.Key.of("data", Metadata.ASCII_STRING_MARSHALLER),data);
        }
        return status.asException(metadata);
    }

}
