package com.wunding.learn.trans.service.service.impl;

import com.wunding.learn.trans.service.service.ExtractText;
import java.io.IOException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component("videoExtractText")
public class VideoExtractText implements ExtractText {

    @Resource
    private AudioExtractText audioExtractText;



    @Override
    public String extractText(String filePath) throws IOException {
        return audioExtractText.extractText(filePath);
    }


}
