package com.wunding.learn.trans.service.service.impl;

import com.wunding.learn.common.util.string.StringPool;
import com.wunding.learn.trans.service.config.SysConfig;
import com.wunding.learn.trans.service.dto.BaiduCreateDTO;
import com.wunding.learn.trans.service.dto.BaiduQueryDTO;
import com.wunding.learn.trans.service.dto.BaiduQueryDTO.DetailedResult;
import com.wunding.learn.trans.service.dto.BaiduQueryDTO.TaskInfo;
import com.wunding.learn.trans.service.dto.BaiduQueryDTO.TaskResult;
import com.wunding.learn.trans.service.service.ExtractText;
import com.wunding.learn.trans.service.util.BaiduApiUtil;
import jakarta.annotation.Resource;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.net.URI;
import java.text.DecimalFormat;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 */
@Slf4j
@Component("audioExtractText")
public class AudioExtractText implements ExtractText {

    @Resource
    private BaiduApiUtil baiduApiUtil;

    @Resource
    private SysConfig sysConfig;


    /**
     * @param filePath 音频文件URL
     */
    @Override
    public String extractText(String filePath) throws IOException {

        // 获取视频外网地址
        String speechUrl = filePath;
        if (filePath.startsWith("http")) {
            filePath = URI.create(filePath).getPath();
        }
        if (filePath.startsWith(StringPool.SLASH)) {
            filePath = filePath.substring(1);
        }
        if (!filePath.startsWith(sysConfig.getRoot())) {
            filePath = sysConfig.getRoot() + StringPool.SLASH + filePath;
        }
        String txtPath = FilenameUtils.getPath(filePath) + FilenameUtils.getBaseName(filePath) + ".txt";
        // 因为这个目录是公有读,因此直接拼接可访问路径即可
        BaiduCreateDTO baiduCreateDTO = baiduApiUtil.obtainMp3Text(speechUrl);

        if (baiduCreateDTO == null || baiduCreateDTO.getErrorCode() != null) {
            log.error("音频文件转写任务创建失败,文件url:{}", speechUrl);
            File txtFile = new File(sysConfig.getPhysicalPath(txtPath));
            if (!txtFile.exists()) {
                boolean newFileStatus = txtFile.createNewFile();
                log.info("create txt file status: {}", newFileStatus);
            }
            return txtPath;
        }
        while (taskStatusRunning(baiduCreateDTO.getTaskId(), txtPath)) {
            try {
                Thread.sleep(3000);
            } catch (InterruptedException e) {
                log.error("sleep error", e);
                Thread.currentThread().interrupt();
            }
        }
        try {
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            log.error("sleep error", e);
            Thread.currentThread().interrupt();
        }

        return txtPath;
    }


    /**
     * 任务状态是否正在运行
     *
     * @return boolean
     */
    private boolean taskStatusRunning(String taskId, String txtPath) throws IOException {
        BaiduQueryDTO baiduQueryDTO = baiduApiUtil.queryTextFromId(taskId);
        Integer errorCode = baiduQueryDTO.getErrorCode();

        // 有错误信息直接ack掉这条数据
        List<String> errorInfo = baiduQueryDTO.getErrorInfo();
        if (errorCode != null || !CollectionUtils.isEmpty(errorInfo)) {
            log.error("音频转写失败,taskId:{},响应信息:{}", taskId, baiduQueryDTO);
            return false;
        }

        // 以防后面批量查询id,现在只会有单个id情况
        Map<String, TaskInfo> taskIdMap = baiduQueryDTO.getTasksInfo().stream()
            .collect(Collectors.toMap(TaskInfo::getTaskId, Function.identity()));

        TaskInfo taskInfo = taskIdMap.get(taskId);

        // 运行中直接 拒绝消息,重回队列
        String taskStatus = taskInfo.getTaskStatus();

        if (Objects.equals(taskStatus, "Running")) {
            return true;
        }

        return handlerSuccessInfo(taskInfo, txtPath);
    }

    /**
     * 任务成功的具体的业务处理方法
     * <p>写入txt文件</p>
     * <p>发送消息至课程</p>
     */
    private Boolean handlerSuccessInfo(TaskInfo info, String path) throws IOException {
        TaskResult taskResult = info.getTaskResult();
        // 百度api返回的是个列表可能存在多个字符串的情况直接拼接起来
        String contentText = handlerTimestampResult(taskResult.getDetailedResult());

        File dir = new File(sysConfig.getPhysicalPath(FilenameUtils.getPath(path)));
        if (!dir.exists()) {
            boolean mkdirs = dir.mkdirs();
            log.info("handlerSuccessInfo mkdirs {}", mkdirs);
        }

        File file = new File(sysConfig.getPhysicalPath(path));
        if (!file.exists()) {
            boolean newFile = file.createNewFile();
            log.info("handlerSuccessInfo newFile {}", newFile);
        }

        log.info("handlerSuccessInfo contentText {}, path {}", contentText, path);
        try (FileWriter writer = new FileWriter(file)) {
            IOUtils.write(contentText, writer);
        } catch (IOException e) {
            log.error("handlerSuccessInfo failedToFile: {}", file.getAbsolutePath(), e);
        }

        log.info("handlerSuccessInfo file length:{}", file.length());

        return false;
    }

    /**
     * 将文本和对应时间戳搭配使用
     *
     * @param detailedResult 详细结果
     * @return {@link String}
     */
    private String handlerTimestampResult(List<DetailedResult> detailedResult) {
        return detailedResult.stream().map(item -> {
            Integer beginTime = item.getBeginTime();
            Integer endTime = item.getEndTime();

            String beginTimeStr = convertToTime(beginTime);
            String endTimeStr = convertToTime(endTime);

            return beginTimeStr + "-" + endTimeStr + " "
                + String.join("", item.getResults());
        }).collect(
            Collectors.joining(" ")
        );
    }


    private String convertToTime(long milliseconds) {
        // 将毫秒值转换为小时、分钟和秒钟
        long hours = TimeUnit.MILLISECONDS.toHours(milliseconds);
        long minutes = TimeUnit.MILLISECONDS.toMinutes(milliseconds) % 60;
        long seconds = TimeUnit.MILLISECONDS.toSeconds(milliseconds) % 60;

        // 格式化时间值为 "hh:mm:ss" 格式
        DecimalFormat decimalFormat = new DecimalFormat("00");
        return decimalFormat.format(hours) + ":" +
            decimalFormat.format(minutes) + ":" +
            decimalFormat.format(seconds);
    }


}
