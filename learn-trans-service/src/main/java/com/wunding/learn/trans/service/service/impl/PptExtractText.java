package com.wunding.learn.trans.service.service.impl;

import com.wunding.learn.trans.service.config.SysConfig;
import com.wunding.learn.trans.service.service.ExtractText;
import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.util.List;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.extractor.POITextExtractor;
import org.apache.poi.hslf.extractor.PowerPointExtractor;
import org.apache.poi.poifs.filesystem.FileMagic;
import org.apache.poi.sl.extractor.SlideShowExtractor;
import org.apache.poi.xslf.usermodel.XMLSlideShow;
import org.apache.poi.xslf.usermodel.XSLFShape;
import org.apache.poi.xslf.usermodel.XSLFSlide;
import org.apache.poi.xslf.usermodel.XSLFTextParagraph;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component("pptExtractText")
public class PptExtractText implements ExtractText {

    @Resource
    private SysConfig sysConfig;

    @Override
    public String extractText(String filePath) throws IOException {
        File needTransFile = new File(sysConfig.getPhysicalPath(filePath));
        StringBuilder text = new StringBuilder();
        try (FileInputStream fileInputStream = new FileInputStream(
            needTransFile); BufferedInputStream bufferedInputStream = new BufferedInputStream(fileInputStream)) {
            log.info(FileMagic.valueOf(bufferedInputStream).toString());
            XMLSlideShow xmlSlideShow = new XMLSlideShow(bufferedInputStream);
            List<XSLFSlide> slides = xmlSlideShow.getSlides();
            SlideShowExtractor<XSLFShape,XSLFTextParagraph> slideShowExtractor = new SlideShowExtractor<>(xmlSlideShow);
            for (XSLFSlide slide : slides) {
                text.append(slideShowExtractor.getText(slide));
            }
            slideShowExtractor.close();
            log.info(text.toString());
        } catch (IOException e) {
            log.error("param filePath: {} , error:", filePath, e);
        }

        // 文本为空不做处理
        if (StringUtils.isBlank(text.toString())) {
            text = new StringBuilder();
        }

        String outTextFilePath = FilenameUtils.getPath(filePath) + FilenameUtils.getBaseName(filePath) + ".txt";
        File file = new File(sysConfig.getPhysicalPath(outTextFilePath));
        if (!file.exists()) {
            boolean newFileStatus = file.createNewFile();
            log.info("newFileStatus {}",newFileStatus);
        }

        IOUtils.write(text.toString(),new FileWriter(file));

        return outTextFilePath;
    }

}
