package com.wunding.learn.project.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @program: mlearn
 * @description: <p>获取单个岗位发展信息-编辑前回显</p>
 * @author: 赖卓成
 * @create: 2022-07-12 13:27
 **/
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(name = "PositionDTO", description = "获取单个岗位发展信息对象")
public class PositionDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "岗位发展id")
    private String id;

    @Schema(description = "原岗位id")
    private String sourcePositionId;

    @Schema(description = "目标岗位id")
    private String targetPositionId;

    @Schema(description = "原岗位名称")
    private String sourcePositionName;

    @Schema(description = "目标岗位名称")
    private String targetPositionName;

    @Schema(description = "岗位发展类型 0同岗位 1跨岗位")
    private Integer progressType;

    @Schema(description = "创建、归属部门Id")
    private String orgId;

    @Schema(description = "是否发布")
    private Integer isPublish;

    @Schema(description = "发布人")
    private String publishBy;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "发布时间")
    private Date publishTime;

    @Schema(description = "活动列表")
    private List<PositionActivityDTO> activityList;

}
