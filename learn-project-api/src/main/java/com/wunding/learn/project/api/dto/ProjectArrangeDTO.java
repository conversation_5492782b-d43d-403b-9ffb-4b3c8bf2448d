package com.wunding.learn.project.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p> 学习项目创建-用户对象
 *
 * <AUTHOR> href="mailto:<EMAIL>">Herilo</a>
 * @since 2023-12-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(name = "ProjectArrangeDTO", description = "计划编排-学习项目创建对象")
public class ProjectArrangeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "胜任力地图id")
    @NotBlank(message = "胜任力地图id不可为空")
    private String mapId;

    @Schema(description = "学习项目名称")
    @NotBlank(message = "学习项目名称不可为空")
    private String projectName;

    @Schema(description = "计划类型：0初训 1复训")
    @NotNull(message = "计划类型不可为空")
    private Integer planningType;

    @Schema(description = "能力id")
    @NotBlank(message = "能力id不能为空")
    private String abilityId;

    @Schema(description = "开始时间")
    @NotNull(message = "开始时间不能为空")
    private Date startTime;

    @Schema(description = "结束时间")
    @NotNull(message = "结束时间不能为空")
    private Date endTime;

    @Schema(description = "待安排学员列表")
    @NotNull(message = "待安排学员列表不能为空")
    @Size(min = 1, message = "待安排学员列表不能为空")
    private List<ProjectArrangeUserDTO> arrangeUserList;
}
