package com.wunding.learn.project.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p> 学习项目创建-用户对象
 *
 * <AUTHOR> href="mailto:<EMAIL>">Herilo</a>
 * @since 2023-12-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(name = "ProjectArrangeUserDTO", description = "学习项目创建-用户对象")
public class ProjectArrangeUserDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "用户id")
    private String userId;

    @Schema(description = "账号")
    private String loginName;

    @Schema(description = "用户名称")
    private String fullName;

    @Schema(description = "部门名称")
    private String orgName;

    @Schema(description = "岗位名称")
    private String postName;

    @Schema(description = "需要去培训 0否 1是")
    private Integer needTrain;

    @Schema(description = "需要去实操 0否 1是")
    private Integer needPractical;

    @Schema(description = "需要去监督 0否 1是")
    private Integer needSupervision;

    @Schema(description = "需要去教授 0否 1是")
    private Integer needTeaching;

    @Schema(description = "各学习形式申请记录")
    private List<LearnMapUserApplyOperationRecord> applyOperationRecordList;
}
