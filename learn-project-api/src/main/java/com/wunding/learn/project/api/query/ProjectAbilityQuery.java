package com.wunding.learn.project.api.query;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 学习项目能力关联查询计划人数对象
 *
 * <AUTHOR>
 * @date 2024/4/26
 */
@Data
@Schema(name = "ProjectAbilityQuery", description = "学习项目能力关联查询计划人数对象")
@AllArgsConstructor
@NoArgsConstructor
public class ProjectAbilityQuery implements Serializable {

    private static final long serialVersionUID = -3949398623879149856L;

    @Schema(description = "能力id列表")
    private List<String> abilityIdList;

    @Schema(description = "用户id列表")
    private Set<String> userIdList;

    @Schema(description = "截至时间")
    private Date endDate;
}
