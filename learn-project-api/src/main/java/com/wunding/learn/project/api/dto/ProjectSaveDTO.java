package com.wunding.learn.project.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p>
 * 学习项目培训项目创建对象
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">wgr</a>
 * @date 2023/3/14 10:32
 */
@Data
@Schema(name = "ProjectSaveDTO", description = "学习项目创建对象")
public class ProjectSaveDTO extends ProjectBaseDTO{
    /**
     *  1 是学习项目,3 是培训项目
     *
     */
    @Schema(description = " 1 是学习项目,3 是培训项目")
    private Integer isTrain;

    @Schema(description = "创建、归属部门Id", hidden = true)
    private String orgId;
}
