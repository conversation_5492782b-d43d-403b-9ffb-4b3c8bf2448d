package com.wunding.learn.project.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 用户能力实操列表对象DTO
 *
 * <AUTHOR>
 * @date 2024/3/27
 */
@Data
@Schema(name = "UserAbilityPracticalListDTO", description = "用户能力实操列表对象")
public class UserAbilityPracticalListDTO {

    @Schema(description = "实操id")
    private String id;

    @Schema(description = "实操时间")
    private Date practicalDate;

    @Schema(description = "项目id")
    private String proId;

    @Schema(description = "任务id")
    private String taskId;

    @Schema(description = "项目名称")
    private String proName;

    @Schema(description = "实操名称")
    private String practicalName;

    @Schema(description = "状态 0-不通过 1-通过")
    private Integer status;

    @Schema(description = "详情")
    private BigDecimal practicalDetails;

}
