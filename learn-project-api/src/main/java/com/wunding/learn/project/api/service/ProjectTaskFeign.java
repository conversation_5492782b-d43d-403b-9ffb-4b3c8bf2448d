package com.wunding.learn.project.api.service;

import com.github.pagehelper.PageInfo;
import com.wunding.learn.project.api.dto.ProjectPracticalOperationSuperviseInfoDTO;
import com.wunding.learn.project.api.dto.ProjectPracticalOperationUserInfoDTO;
import com.wunding.learn.project.api.dto.ProjectTaskDTO;
import com.wunding.learn.project.api.dto.UserAbilityPracticalListDTO;
import com.wunding.learn.project.api.query.AbilityPracticalListQuery;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @title: ProjectTaskService
 * @projectName: mlearn
 * @description：
 * @date 2022/8/3 9:59
 */
@FeignClient(url = "${learn.service.learn-project-service}", path = "/project", name = "learn-project-service")
public interface ProjectTaskFeign {

    /**
     * 直接创建项目任务 保存项目任务内容关联
     *
     * @param projectTaskId 任务id
     * @param taskContent   任务内容
     */
    @PostMapping("/project/saveTaskContent")
    void saveTaskContent(@RequestParam("projectTaskId") String projectTaskId,
        @RequestParam("taskContent") String taskContent);

    @GetMapping("/project/getProjectTask")
    ProjectTaskDTO getProjectTask(@RequestParam("") String id);

    /**
     * 获取用户参与的项目任务的实操信息
     *
     * @param query 查询对象
     * @return 实操信息
     */
    @GetMapping("/project/getProjectTaskByUserIdList")
    PageInfo<UserAbilityPracticalListDTO> getProjectTaskByUserIdList(@RequestBody AbilityPracticalListQuery query);

    /**
     * 获取用户参与的项目任务的监督信息
     *
     * @param query 查询对象
     * @return 实操信息
     */
    @GetMapping("/project/getUserAbilitySuperviseProjectList")
    PageInfo<UserAbilityPracticalListDTO> getUserAbilitySuperviseProjectList(
        @RequestBody AbilityPracticalListQuery query);

    /**
     * 获取实操信息
     *
     * @param idList
     * @return
     */
    @PostMapping("/project/getPracticalUserInfoList")
    List<ProjectPracticalOperationUserInfoDTO> getPracticalUserInfoList(
        @RequestBody Collection<String> userIdList,
        @RequestParam("idList") Collection<String> idList);

    /**
     * 获取监督评价信息
     *
     * @param idList
     * @return
     */
    @PostMapping("/project/getSuperviseListByParam")
    List<ProjectPracticalOperationSuperviseInfoDTO> getSuperviseListByParam(
        @RequestBody Collection<String> userIdList, @RequestParam("idList") Collection<String> idList);

    /**
     * 根据学习项目id列表获取实操信息
     *
     * @param proIdList 学习项目id
     * @return {@link Set}
     */
    @GetMapping("/project/getProjectPracticalTaskList")
    Set<String> getProjectPracticalTaskList(@RequestParam("proIdList") List<String> proIdList);

    /**
     * 更新用户学习状况
     *
     * @param userId 用户id
     * @param projectId 项目id
     */
    @PostMapping("/project/addProjectUserStudyCondition")
    void addProjectUserStudyCondition(@RequestParam("userId") String userId,
        @RequestParam("projectId") String projectId,
        @RequestParam("type") String type);

}
