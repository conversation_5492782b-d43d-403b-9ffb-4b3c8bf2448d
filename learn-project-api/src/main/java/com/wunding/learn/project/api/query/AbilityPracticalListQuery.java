package com.wunding.learn.project.api.query;

import com.wunding.learn.common.bean.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Set;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 学员查看-实操/监督-查询对象
 *
 * <AUTHOR>
 * @date 2024/5/13 下午9:20
 */
@Data
@Schema(name = "AbilityPracticalListQuery", description = "学员查看-实操/监督-查询对象")
public class AbilityPracticalListQuery extends BaseEntity {

    private static final long serialVersionUID = -2184613053203874074L;

    @Schema(description = "类型 0-初训 1-复训")
    @Min(value = 0, message = "类型 0-初训 1-复训")
    @Max(value = 1, message = "类型 0-初训 1-复训")
    private Integer type;

    @Schema(description = "能力项完成记录ID")
    @NotBlank(message = "能力项完成记录ID不能为空")
    private String recordId;

    @Schema(description = "用户id", hidden = true)
    private String userId;

    @Schema(description = "学习项目id列表", hidden = true)
    private Set<String> projectIdList;

    @Schema(description = "学习形式id")
    private String categoryId;

    @Schema(description = "是否参与，0-待参与列表，1-已参与列表")
    private Integer isJoin;
}
