package com.wunding.learn.project.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;

/**
 * <p>
 * 学习项目列表DTO
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">wgr</a>
 * @date 2023/3/14 15:14
 */
@Data
@Schema(name = "ProjectListDTO", description = "学习项目管理列表返回对象")
public class ProjectListDTO {

    @Schema(description = "引用资源的ID,如培训项目ID")
    private String referencedId;

    @Schema(description = "引用资源的名称，比如培训项目的名称")
    private String referencedName;

    @Schema(description = "项目id")
    private String id;

    @Schema(description = "项目编号")
    private String proNo;

    @Schema(description = "项目名称")
    private String proName;

    @Schema(description = "计划类型 0固定日期，1固定周期")
    private Integer type;

    @Schema(description = "固定周期天数")
    private Long cycleDay;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "开始时间")
    private Date startTime;

    @Schema(description = "结束时间")
    private Date endTime;

    @Schema(description = "发布人")
    private String publishBy;

    @Schema(description = "发布时间")
    private Date publishTime;

    @Schema(description = "发布状态")
    private Integer isPublish;

    @Schema(description = "发布状态名称")
    private String publishStatus;

    @Schema(description = "任务数")
    private Integer taskNum;

    @Schema(description = "参与人数")
    private Integer peopleNum;

    @Schema(description = "关联项目数")
    private Integer relatedProjectNum;


}
