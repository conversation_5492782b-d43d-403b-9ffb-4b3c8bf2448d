package com.wunding.learn.project.api.service;

import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.dto.CerDitchDTO;
import com.wunding.learn.common.dto.CertificationContentDTO;
import com.wunding.learn.common.dto.ProjectInfoQuery;
import com.wunding.learn.common.dto.PublishDTO;
import com.wunding.learn.common.dto.ResourceBaseDTO;
import com.wunding.learn.common.dto.ResourceDeleteInfoDTO;
import com.wunding.learn.common.dto.ResourceMemberDTO;
import com.wunding.learn.common.query.ResourceBaseQuery;
import com.wunding.learn.common.query.ResourceUserQuery;
import com.wunding.learn.project.api.dto.FaceProjectApiDTO;
import com.wunding.learn.project.api.dto.PositionDTO;
import com.wunding.learn.project.api.dto.ProjectApiDTO;
import com.wunding.learn.project.api.dto.ProjectArrangeDTO;
import com.wunding.learn.project.api.dto.ProjectDetailDTO;
import com.wunding.learn.project.api.dto.ProjectListDTO;
import com.wunding.learn.project.api.dto.ProjectSaveDTO;
import com.wunding.learn.project.api.dto.ProjectStatisticDTO;
import com.wunding.learn.project.api.dto.ProjectUpdateDTO;
import com.wunding.learn.project.api.query.ProjectAbilityQuery;
import com.wunding.learn.project.api.query.ProjectListQuery;
import com.wunding.learn.project.api.query.ProjectStatisticQueryDTO;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @title: ProjectFeign
 * @projectName: mlearn
 * @description：
 * @date 2022/8/9 16:49
 */
@FeignClient(url = "${learn.service.learn-project-service}", path = "/project", name = "learn-project-service")
public interface ProjectFeign {

    /**
     * 根据项目名称获取项目idList
     *
     * @param projectName
     * @return
     */
    @GetMapping("/project/getProjectIdListByName")
    List<String> getProjectIdListByName(@RequestParam("projectName") String projectName);

    /**
     * 获取有效项目id列表（未删除，结束时间大于当前时间）
     *
     * @return
     */
    @GetMapping("/project/getEffectiveProjectIds")
    List<String> getEffectiveProjectIds();

    /**
     * 根据项目idSet获取项目信息
     *
     * @param idSet 项目主键set
     * @return
     */
    @PostMapping("/project/getProjectInfoByIdList")
    Map<String, ProjectApiDTO> getProjectInfoMapByIdList(@RequestBody Set<String> idSet);

    /**
     * 根据项目idSet获取项目信息
     *
     * @param abilityId  能力id
     * @param categoryId 学习形式id
     * @param isJoin
     * @return {@link PageInfo}
     */
    @GetMapping("/project/getProjectPageInfoMapByIdList")
    PageInfo<ProjectApiDTO> getProjectPageInfoMapByIdList(@RequestParam("abilityId") String abilityId,
        @RequestParam("categoryId") String categoryId,
        @RequestParam(value = "isJoin", required = false) Integer isJoin, @RequestParam("pageNo") Integer pageNo,
        @RequestParam("pageSize") Integer pageSize);

    /**
     * 通过id列表获取项目名 不过滤被删除数据，认证模块使用
     *
     * @param batchIds 项目表ids
     * @return {@link Map}<{@link String}, {@link String}> id与项目名称映射
     */
    @GetMapping("/project/getNameBatchIds")
    Map<String, String> getNameBatchIds(@RequestParam("batchIds") Collection<String> batchIds);

    /**
     * 通过id列表获取项目信息 不过滤被删除数据
     *
     * @param batchIds 项目表ids
     * @return {@link Map}<{@link String}, {@link ResourceMemberDTO}> id与项目信息映射
     */
    @GetMapping("/exam/getResourceMemberBatchIds")
    Map<String, ResourceMemberDTO> getResourceMemberBatchIds(@RequestParam("batchIds") Collection<String> batchIds);

    /**
     * 通过团队id获取团队名字
     *
     * @param teamIds 团队id
     * @return {@link Map}<{@link String}, {@link String}>
     */
    @GetMapping("/project/getTeamNameByIds")
    Map<String, String> getTeamNameByIds(@RequestParam("teamIds") Collection<String> teamIds);

    /**
     * 检查下发范围
     *
     * @param userId     用户id
     * @param resourceId 资源id
     * @return {@link Boolean}
     */
    @GetMapping("/project/checkViewLimit")
    Boolean checkViewLimit(@RequestParam("userId") String userId, @RequestParam("resourceId") String resourceId);

    /**
     * 根据渠道id 获取一个渠道信息
     *
     * @param contentId 内容识别
     * @return {@link CerDitchDTO}
     */
    @GetMapping("/project/getDitch")
    CerDitchDTO getDitch(@RequestParam("contentId") String contentId);

    /**
     * 获取一个学习项目
     *
     * @return
     */
    @GetMapping("/project/getProjectById")
    ProjectApiDTO getProjectById(@RequestParam("projectId") String projectId);

    /**
     * 获取该用户已完成学习项目id
     *
     * @param userId 用户id
     * @param ids    学习项目id集合
     * @return {@link List}<{@link String}>
     */
    @GetMapping("/project/getOverProjectIds")
    List<String> getOverProjectIds(@RequestParam("userId") String userId, @RequestParam("ids") Collection<String> ids);

    /**
     * 校验用户是否参加项目
     *
     * @param projectId
     * @param userId
     * @return
     */
    @GetMapping("/project/verifyIsJoinProject")
    Integer verifyIsJoinProject(@RequestParam("projectId") String projectId, @RequestParam("userId") String userId);

    /**
     * 过滤获取已被删除的资源id
     *
     * @param projectIdList
     * @return
     */
    @GetMapping("/project/getInvalidProjectId")
    List<String> getInvalidProjectId(@RequestParam("projectIdList") Collection<String> projectIdList);


    /**
     * 其他项目模块保存项目
     *
     * @param projectSaveDTO
     */
    @PostMapping("/project/saveProject")
    String saveProject(@RequestBody ProjectSaveDTO projectSaveDTO);


    /**
     * 其他项目模块更新项目
     *
     * @param projectUpdateDTO
     */
    @PostMapping("/project/updateProject")
    void updateProject(@RequestBody ProjectUpdateDTO projectUpdateDTO);


    /**
     * 查询引用的学习项目列表
     *
     * @param projectListQuery
     * @return
     */
    @PostMapping("/project/queryReferencedProjectPage")
    PageInfo<ProjectListDTO> queryReferencedProjectPage(@RequestBody ProjectListQuery projectListQuery);


    /**
     * 根据学习项目ID 查询学习项目
     *
     * @param projectId
     * @return
     */
    @GetMapping("/project/findProjectDetailById")
    ProjectDetailDTO findProjectDetailById(@RequestParam("projectId") String projectId);

    /**
     * 根据引用id获取所有的学习项目信息
     *
     * @param referenceId
     * @return
     */
    @GetMapping("/project/queryProjectListByReferenceId")
    List<ProjectListDTO> queryProjectListByReferenceId(@RequestParam("referenceId") String referenceId);

    /**
     * 发布取消发布学习项目
     *
     * @param id
     * @param publishType
     */
    @PostMapping("/project/publishOrUnPublishProject")
    void publishOrUnPublishProject(@RequestParam("id") String id, @RequestParam("publishType") Integer publishType);


    /**
     * 发布学习项目
     *
     * @param publishDTO
     */
    @PostMapping("/project/publish")
    void publish(@RequestBody PublishDTO publishDTO);


    /**
     * 删除相关项目
     *
     * @param ids
     */
    @GetMapping("/project/remove")
    void remove(@RequestParam("ids") String ids);

    /**
     * 更新项目归属部门
     *
     * @param projectUpdateDTO
     */
    @PostMapping("/project/updateProjectOrgId")
    void updateProjectOrgId(@RequestBody ProjectUpdateDTO projectUpdateDTO);

    /**
     * 获取学习项目完成状态
     *
     * @param resourceUserQuery
     * @return
     */
    @GetMapping("/project/getProjectFinish")
    Map<String, Integer> getProjectFinish(@RequestBody ResourceUserQuery resourceUserQuery);

    /**
     * 获取当前项目的下发范围Id
     *
     * @param projectId
     * @return
     */
    @GetMapping("/project/getProjectViewLimitId")
    Long getProjectViewLimitId(@RequestParam("projectId") String projectId);

    /**
     * 获取基本信息
     *
     * @param resourceBaseQuery
     * @return
     */
    @PostMapping("/project/getProjectBaseInfo")
    Map<String, ResourceBaseDTO> getProjectBaseInfo(@RequestBody ResourceBaseQuery resourceBaseQuery);

    /**
     * 获取全面岗位发展
     *
     * @return
     */
    @GetMapping("/project/getAllPosition")
    List<PositionDTO> getAllPosition();

    /**
     * 获取用户某个能力开班参与情况
     *
     * @param userId 用户id
     * @param ids    能力id
     * @param type   查看状态 0-进行中 1-未开始
     * @return 能力是否计划开班
     */
    @GetMapping("/project/getUserAbilityProject")
    Map<String, List<String>> getUserAbilityProject(@RequestParam("userId") String userId,
        @RequestParam("ids") Collection<String> ids, @RequestParam("type") Integer type);

    /**
     * 获取用户一批学习项目的完成状态
     *
     * @param userId 用户id
     * @param ids    能力id
     * @return 能力是否计划开班
     */
    @GetMapping("/project/getUserProjectFinish")
    Map<String, Integer> getUserProjectFinish(@RequestParam("userId") String userId,
        @RequestParam("ids") Collection<String> ids);

    /**
     * 创建日期项目
     *
     * @param projectArrangeDTO {@link ProjectArrangeDTO}
     * @return 项目id
     */
    @PostMapping("/project/arrangeCreateProject")
    String arrangeCreateProject(@RequestBody ProjectArrangeDTO projectArrangeDTO);

    /**
     * 获取用户面授信息
     *
     * @param categoryIds
     * @param currentUserId
     * @return
     */
    @GetMapping("/project/getFaceProjectId")
    Map<String, FaceProjectApiDTO> getFaceProjectId(@RequestBody Collection<String> categoryIds,
        @RequestParam("currentUserId") String currentUserId);

    /**
     * 获取项目基本信息
     *
     * @param batchIds
     * @return
     */
    @GetMapping("/project/getCertificationContentList")
    Map<String, CertificationContentDTO> getCertificationContentList(
        @RequestParam("batchIds") Collection<String> batchIds);

    /**
     * 获取学习项目
     *
     * @param proNo
     * @return
     */
    @GetMapping("/project/getProjectByProNo")
    ProjectApiDTO getProjectByProNo(@RequestParam("proNo") String proNo);

    /**
     * 获取用户能力计划中的人数
     *
     * @param ability 查询对象
     * @return {@link Integer}
     */
    @GetMapping("/project/getProjectAbilityPlanList")
    Integer getProjectAbilityPlanList(@RequestBody ProjectAbilityQuery ability);

    /**
     * 根据学习项目id获取项目关联的能力id
     *
     * @param projectId 学习项目id
     * @return {@link List}<{@link String}>
     */
    @GetMapping("/project/getProjectAbilityIdListByProjectId")
    List<String> getProjectAbilityIdListByProjectId(@RequestParam("projectId") String projectId);

    /**
     * 获取学习项目是否有过进行结业的集合
     *
     * @param projectIds
     * @return
     */
    @PostMapping("/project/getProjectCompletionMap")
    Map<String, Integer> getProjectCompletionMap(@RequestBody Collection<String> projectIds);

    /**
     * 学习项目中是否存在某个类型的任务
     *
     * @param projectIdList 学习项目id列表
     * @return {@link Integer}
     */
    @GetMapping("/project/getProjectTaskTypeByProjectIdListAndTaskType")
    Integer getProjectTaskTypeByProjectIdListAndTaskType(@RequestParam("projectIdList") List<String> projectIdList);

    /**
     * 获取当前用户参与的评价项目数量
     *
     * @return {@link Integer}
     */
    @GetMapping("/project/getProjectTaskSupervisionCount")
    Integer getProjectTaskSupervisionCount(@RequestParam("abilityId") String abilityId);

    @GetMapping("/project/getTrainProjectInfo")
    Map<String, ProjectApiDTO> getTrainProjectInfo(@RequestBody ProjectInfoQuery projectInfoQuery);

    /**
     * 获取学习项目的删除信息
     *
     * @param resourceId 学习项目id
     * @return ResourceDeleteInfoDTO
     */
    @GetMapping("/project/getProjectIsDelById")
    ResourceDeleteInfoDTO getProjectIsDelById(@RequestParam("resourceId") String resourceId);

    /**
     * 项目信息统计列表
     *
     * @param projectStatisticQueryDTO
     * @return
     */
    @PostMapping("/project/statisticList")
    PageInfo<ProjectStatisticDTO> statisticList(@RequestBody ProjectStatisticQueryDTO projectStatisticQueryDTO);

    /**
     * 修改学习项目引用资源名称
     *
     * @param projectIdSet   项目id集合
     * @param referencedName 引用资源名称
     */
    @PostMapping("/project/updateReferencedName")
    void updateReferencedName(@RequestBody Collection<String> projectIdSet,
        @RequestParam("referencedName") String referencedName);
}
