package com.wunding.learn.project.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 用户实操信息对象DTO
 *
 * <AUTHOR>
 * @date 2024/3/27
 */
@Data
@Schema(name = "ProjectPracticalOperationUserInfoDTO", description = "用户实操信息对象DTO")
public class ProjectPracticalOperationUserInfoDTO {
    @Schema(description = "项目Id")
    private String proId;

    @Schema(description = "项目名称")
    private String proName;

    @Schema(description = "实操id")
    private String practicalId;

    @Schema(description = "用户id")
    private String userId;

    @Schema(description = "实操名称")
    private String practicalName;

    @Schema(description = "开始时间")
    private Date startTime;

    @Schema(description = "结束时间")
    private Date endTime;

    @Schema(description = "是否通过 0-否 1-是")
    private Integer status;

    @Schema(description = "得分分数")
    private BigDecimal score;

}
