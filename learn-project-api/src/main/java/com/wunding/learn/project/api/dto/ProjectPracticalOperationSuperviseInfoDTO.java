package com.wunding.learn.project.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;

/**
 * 用户监督实操信息对象DTO
 *
 * <AUTHOR>
 * @date 2024/3/27
 */
@Data
@Schema(name = "ProjectPracticalOperationSuperviseInfoDTO", description = "用户监督实操信息对象DTO")
public class ProjectPracticalOperationSuperviseInfoDTO {
    @Schema(description = "项目Id")
    private String proId;

    @Schema(description = "项目名称")
    private String proName;

    @Schema(description = "实操id")
    private String practicalId;

    @Schema(description = "用户id")
    private String userId;

    @Schema(description = "实操名称")
    private String practicalName;

    @Schema(description = "开始时间")
    private Date startTime;

    @Schema(description = "结束时间")
    private Date endTime;

    @Schema(description = "完成数量")
    private Long finished;

    @Schema(description = "评价总数")
    private Long count;

}
