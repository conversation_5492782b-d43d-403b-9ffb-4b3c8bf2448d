package com.wunding.learn.project.api.query;

import com.wunding.learn.common.bean.BaseEntity;
import io.swagger.v3.oas.annotations.Parameter;
import java.util.Date;
import java.util.Set;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <p> 
 *
 * <AUTHOR> href="mailto:<EMAIL>">liangzm</a>
 * @since 2025/2/12
 */
@Data
public class ProjectStatisticQueryDTO  extends BaseEntity {

    private static final long serialVersionUID = -4963423377389790782L;

    @Parameter(description = "开始时间")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private Date startTime;

    @Parameter(description = "结束时间")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private Date endTime;

    @Parameter(description = "归属部门（部门id）")
    private String orgId;

    @Parameter(description = "项目名称")
    @Length(max = 80, message = "项目名称长度不能超过80")
    private String proName;

    @Parameter(description = "班主任id")
    private String leaderId;

    @Parameter(description = "管辖范围id", hidden = true)
    private Set<String> userManageAreaOrgId;

    @Parameter(description = "项目类型 0-学习项目 3-面授项目")
    private Integer projectType;

    @Parameter(description = "是否班级（培训项目创建的班级） 0-否 1-是 默认0" ,hidden = true)
    private Integer isClass;
}
