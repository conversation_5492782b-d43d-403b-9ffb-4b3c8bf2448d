package com.wunding.learn.project.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @title ProjectApiDTO
 * @projectName mlearn
 * @date 2022/8/10 10:27
 */
@Data
@Schema(name = "ProjectApiDTO", description = "项目对象")
public class ProjectApiDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "项目id")
    private String id;

    @Schema(description = "项目名称")
    private String proName;

    @Schema(description = "项目编号")
    private String proNo;

    @Schema(description = "项目开始时间")
    private Date startTime;

    @Schema(description = "项目结束时间")
    private Date endTime;

    @Schema(description = "创建人部门id")
    private String orgId;

    @Schema(description = "是否发布")
    private Integer isPublish;

    @Schema(description = "计划类型 0-固定日期 1-固定周期")
    private Integer type;

    @Schema(description = "可见范围方案id")
    private Long programmeId;

    @Schema(description = "地点")
    private String address;

    /**
     * 周期项目的天数
     */
    @Schema(description = "周期项目的天数")
    private Integer period;

    /**
     * 是否可以直接进入任务: 0不可以, 1可以
     */
    @Schema(description = "是否可以直接进入任务: 0不可以, 1可以")
    private Integer isOperation;

    @Schema(description = "是否增加激励兑换支持")
    private Integer isExchange;
    /**
     * 完成度百分比
     */
    @Schema(description = "完成度百分比")
    private BigDecimal progress;
}
