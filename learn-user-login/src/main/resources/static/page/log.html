<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width" />
    <meta http-equiv="X-UA-Compatible" content="IE=11;IE=10;IE=9; IE=8; IE=7; IE=EDGE" />
    <title>修改日志级别</title>
    <style>
        .logTable td {
            border: solid 1px red;
        }
        .name {
            width: 100px;

        }

        .level {

        }
        .action{

        }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/jquery/dist/jquery.min.js"></script>
    <script type="text/javascript" >
        $(function (){
            $.get(getContextPath()+"/actuator/loggers",function(res){
                console.log(res);
                console.log(res.loggers);
                var html="<table class='logTable'><td class='name'>name</td><td class='level'>配置level</td><td class='level'>继承level</td><td class='action'>action</td></th>";

                $.each(res.loggers,function (key,value){
                    html += "<tr>";
                    html += "<td>"+key+"</td>";
                    var level = value.configuredLevel?value.configuredLevel:value.effectiveLevel;
                    html += "<td>"+(value.configuredLevel?value.configuredLevel:'')+"</td>";
                    html += "<td>"+value.effectiveLevel+"</td>";

                    var select = "<select class='select' name='"+key+"' onchange='change(this.name,this.value)'>";
                    $.each(res.levels,function(index,value){
                        select += "<option value='"+value+"' "+(value == level?"selected":"")+">"+value+"</option>";
                    });
                    select += "</select>";

                    html += "<td>"+select+"</td>";
                    html += "</tr>";
                });
                html += "</table>"
                $("#log").html(html);
                console.log(html);
            });
        });

        function change(name,value){
            console.log("change",name,value)
            $.ajax({
                url:getContextPath()+"/actuator/loggers/"+name,
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                data: '{"configuredLevel": "'+value+'"}',
                success: function(data) {
                    console.log(data);
                },
                error: function(res) {
                    console.log("error",res);
                }
            });
        }

        function getContextPath(){
            var pathName = document.location.pathname;
            var index = pathName.substr(1).indexOf("/");
            var result = pathName.substr(0,index+1);
            if(result=='/page') {
                return "";
            }
            return result;
        }

    </script>
</head>

<body>

<div id="log">
</div>
</body>
</html>