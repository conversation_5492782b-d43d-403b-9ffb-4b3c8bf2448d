# 应用服务 WEB 访问端口
server:
  servlet:
    context-path: /login/
  tomcat:
    accept-count: 2000
    max-connections: 81920
    threads:
      max: 2000
      min-spare: 100


# 应用名称
spring:
  profiles:
    active: dev
  main:
    allow-bean-definition-overriding: true
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher

  application:
    name: learn-user-login



  #redis
  # Redis服务器地址
  data:
    redis:

      # Lettuce
      # 连接池最大连接数（使用负值表示没有限制）
      lettuce:
        # 关闭超时时间
        shutdown-timeout: 100
        pool:
          max-active: 1000
          # 连接池最大阻塞等待时间（使用负值表示没有限制）
          max-wait: 10000
          # 连接池中的最大空闲连接
          max-idle: 8
          # 连接池中的最小空闲连接
          min-idle: 0


  #rabbitmq 配置
  rabbitmq:
    publisher-returns: true
    publisher-confirm-type: correlated
    connection-timeout: 15000
    listener:
      simple:
        # 消费端有几个纯种
        concurrency: 10
        # 手动ACK
        acknowledge-mode: manual
        # 消费端的监听最大个数
        max-concurrency: 10
        # 每次拉取消息数量
        prefetch: 1
    template:
      exchange: login



feign:
  client:
    config:
      default:
        logger-level: full
  httpclient:
    enabled: true # 开启feign对HttpClient的支持
    max-connections: 2000  # 最大的连接数
    max-connections-per-route: 500  # 每个路径的最大连接数


#开启acutor端点
management:
  jmx:
    metrics:
      export:
        enabled: false
  endpoints:
# 禁用所有JMX端点暴露
    jmx:
      exposure:
        exclude: '*'
    web:
      exposure:
        include: 'info,health,metrics,prometheus'
  endpoint:
    health:
      show-details: always
      probes:
        enabled: true
  metrics:
    tags:
      application: ${spring.application.name}

# 是否启用默认数据源
tenant:
  type: -1
  systemName: 问鼎云学习
