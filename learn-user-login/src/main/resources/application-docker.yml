# 应用服务 WEB 访问端口
server:
  port: 28002
# 应用名称
spring:

  #redis
  # Redis服务器地址
  data:
    redis:
      host: redis
      # Redis服务器连接端口
      port: 6379
      # Redis数据库索引（默认为0）
      database: 5
      # Redis服务器连接密码（默认为空）
      #    password: 123456
      password: M0eHdhs9kk4VKLjRAb7J41
      # 连接超时时间（毫秒）
      timeout: 10000


  #rabbitmq 配置
  rabbitmq:
    host: rabbitmq
    port: 5672
    virtual-host: /
    username: guest
    password: guest

cas:
  server:
    url: http://cas.wdxuexi.com:8181/cas
    login-url: ${cas.server.url}/login
    logout-url: ${cas.server.url}/logout
  client:
    host-url: http://*************:28002/admin/
    service-url: http://*************:28002/login/cas/casLogin
    validation-type: CAS3

management:
  tracing:
    sampling:
      probability: 1.0
    enabled: true
    propagation:
      type: B3
  otlp:
    metrics:
      export:
        enabled: true
  zipkin:
    tracing:
      endpoint: http://jaeger-collector.observability.svc:14250

opentelemetry:
  traces:
    exporter: otlp
  service:
    name: ${spring.application.name}
  jaeger:
    endpoint: http://jaeger-collector.observability.svc:4317
    protocol: grpc
  instrumentation:
    feign:
      enabled: true
    spring-webmvc:
      enabled: true


learn:
  service:
    learn-user-service: "http://user:8080"

app:
  signKey: bladexisapowerfulmicroservicearchitectureupgradedandoptimizedfromacommercialproject
  privateKey: MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAKsnfFIV7o9_KITb0_M4qZO1sf1bjp9Sbuiz50TGPFg_fx8Buny8WQflyjIHkO6Z6HRwhGmjoL1Kz5Eknn64pZLV7v3i988C6CHCYHSbT7rc_Xiy6z4v3bEi0WiMQC_yPQDIEVP6y07GDz6zdYkutOayJcEywcU0xXcnfth6CCpvAgMBAAECgYBsJnNEW193hV5RNadklXVyROnHsscYnbo_iQ6mQq13BgiJy0nP8CRB_U4a9vT6EH72tPK23hKACnnGuWD9qifU7JRY1Vl8Rud3wkMPmfl_ocowe43OegmuLWknjZXwuGH_z1wnxZ2rDOx4G5T2P7IapKaYaTXzjGHzph403-xZgQJBANOLpOY95sQOFQq0bhmQDJosaEXEhIEo-5TKiEdneVSyu261NGMlwdRY3rEFyteT5GJOawkj8bqm0mq8ReX4E9ECQQDPHvGRXxMLt7ZCvqIoYMry6XsDprNNxknyq6CBZOCQHwczjWKd3nfiqBONi3OJZaMYa4UL4UCVXBcZkwD4yyo_AkAYYvnAfRRUN5dfY4tpsPxy7XmbyVrJFPNjpLFvIdOP2wbWbVc7sseUdiY93AAVN_xVBNs784PTU5XgLhIUv7NxAkBAkpUdjVaSwKPCC0zi6cpcEQ6ZBM_B36AOWBOiQ6D_Ta0hFWM5dLJLp7rw1hkfLOC8LEk-eut3pU5OWtZiunRhAkBySxnRjxbHx7N6TrNvwt1DDtiiDMBRIZlrqNvnk0-aQ-qc45anyjE4-EOCC6inAVgWSGqpweBXmyN-c8Sdttn1
  publicKey: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCrJ3xSFe6PfyiE29PzOKmTtbH9W46fUm7os-dExjxYP38fAbp8vFkH5coyB5Dumeh0cIRpo6C9Ss-RJJ5-uKWS1e794vfPAughwmB0m0-63P14sus-L92xItFojEAv8j0AyBFT-stOxg8-s3WJLrTmsiXBMsHFNMV3J37YeggqbwIDAQAB
  #jwt 有效时长 单位秒
  jwtExpire: 18000
  single:
    - api


debug: false
############# springDoc配置 #############
springdoc:
  version: '@springdoc.version@'
  api-docs:
    groups:
      enabled: true
    enabled: true
  swagger-ui:
    operationsSorter: function
    tagsSorter: alpha
    docExpansion: none
    path: /swagger-ui.html
  group-configs:
    - group: rest
      packages-to-scan: com.wunding.learn.user.login.rest
  # 不配置下面 get请求为对象会变为 json
  default-flat-param-object: true
  disable-i18n: true