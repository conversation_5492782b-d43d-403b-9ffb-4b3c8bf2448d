package com.wunding.learn.user.login.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 登录请求参数对象
 *
 * <AUTHOR>
 * @date 2022/2/16
 */
@Data
@Schema(name = "LoginRequestDTO", description = "登录请求参数dto对象")
public class LoginRequestDTO {

    @Schema(description = "登录类型：password-账号密码 sms-短信验证码 oauth2-第三方 external-外部登录 cas-CAS统一验证登陆", example = "password")
    @NotNull
    private String type;

    @Schema(description = "用户名", example = "admin")
    private String userName;

    @Schema(description = "密码", example = "10ADC3949BA59ABBE56E057F20F883")
    private String password;

    @Schema(description = "登录验证码")
    private String captcha;

    @Schema(description = "验证码唯一id")
    private String uuid;


    @Schema(description = "入口类型：0-企业微信 1-微信公众号 2-微信小程序 3-钉钉 4-飞书")
    private Integer entryType;

    @Schema(description = "授权Code")
    private String code;

    @Schema(description = "用户唯一标识")
    private String openId;

    @Schema(description = "自定义参数")
    private String state;

    @Schema(description = "加密token,给外部登录使用")
    private String encryptToken;

    @Schema(description = "企业微信 0-澳美点击 1-澳美领航 （这个字段影响范围太大就不改名字，实际就是 multiChannelType 多渠道类型这个字段）")
    private String weChatType;

}
