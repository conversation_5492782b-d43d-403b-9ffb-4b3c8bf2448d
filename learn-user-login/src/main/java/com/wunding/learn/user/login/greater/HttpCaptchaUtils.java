package com.wunding.learn.user.login.greater;

import com.wunding.learn.user.login.exception.InvalidCaptchaException;
import jakarta.servlet.http.HttpSession;
import nl.captcha.Captcha;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

/**
 * @Author: GYong
 * @Date: 2023/4/10 10:31
 */
public class HttpCaptchaUtils {


    private static final Logger log = LoggerFactory.getLogger(HttpCaptchaUtils.class);
    private static final String CAPTCHA_ATTR = "com.wunding.captcha";
    public static final Object LOCK_OBJECT = new Object();

    /**
     * 检查验证码
     *
     * @param captchaString
     * @param session
     */
    public static void checkCaptcha(String captchaString, HttpSession session) {

        // captcha.isCorrect maybe cause NullPointException,so must check the captchaString first.
        if (StringUtils.isBlank(captchaString)) {
            throw new InvalidCaptchaException("Blank captcha string!");
        }
        Assert.notNull(session, "None session!");

        Captcha captcha = null;
        try {
            captcha = (Captcha) session.getAttribute(CAPTCHA_ATTR);
        } catch (IllegalStateException e) {
            throw new InvalidCaptchaException("同一账号多次登录，或Session超时");
        } catch (Exception e) {
            log.error("", e);
        }
        if (null == captcha) {
            throw new InvalidCaptchaException("Blank captcha in session!");
        }

        if (!captcha.isCorrect(captchaString)) {
            throw new InvalidCaptchaException("验证码错误");
        }

        synchronized (LOCK_OBJECT) {
            session.removeAttribute(CAPTCHA_ATTR);
        }
    }

    /**
     * @param captcha
     * @param session
     */
    public static void storeCaptcha(Captcha captcha, HttpSession session) {
        synchronized (LOCK_OBJECT) {
            session.setAttribute(CAPTCHA_ATTR, captcha);
        }
    }

    private HttpCaptchaUtils() {
    }

}
