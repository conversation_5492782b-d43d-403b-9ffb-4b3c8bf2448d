package com.wunding.learn.user.login.service.impl;


import static com.wunding.learn.common.util.string.StringUtil.newId;

import com.wunding.learn.common.constant.other.ParaConstant;
import com.wunding.learn.common.constant.user.UserErrorNoEnum;
import com.wunding.learn.common.enums.other.SystemConfigCodeEnum;
import com.wunding.learn.common.enums.redis.UserRedisKeyEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.user.api.service.ParaFeign;
import com.wunding.learn.user.login.dto.CaptchaRespDto;
import com.wunding.learn.user.login.service.CaptchaService;
import java.awt.Color;
import java.awt.Font;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.concurrent.TimeUnit;
import jakarta.annotation.Resource;
import javax.imageio.ImageIO;
import lombok.extern.slf4j.Slf4j;
import nl.captcha.Captcha;
import nl.captcha.text.renderer.DefaultWordRenderer;
import nl.captcha.text.renderer.WordRenderer;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Base64Utils;
import org.springframework.util.FastByteArrayOutputStream;

/**
 * <AUTHOR>
 */
@Service("captchaService")
@Slf4j
public class CaptchaServiceImpl implements CaptchaService {

    /**
     * 验证码
     */
    private static final List<Font> FONTS = new ArrayList<>(4);
    private static final List<Color> COLORS = new ArrayList<>(1);
    private static final WordRenderer RENDERER;

    static {
        FONTS.add(new Font("Courier New", Font.ITALIC, 24));
        FONTS.add(new Font("Arial", Font.ITALIC, 24));
        FONTS.add(new Font("Times New Roman", Font.ITALIC, 24));
        FONTS.add(new Font("Verdana", Font.ITALIC, 24));

        COLORS.add(Color.BLACK);
        RENDERER = new DefaultWordRenderer(COLORS, FONTS);
    }

    /**
     * 验证码过期时间
     */
    private static final long LOGIN_CODE_EXPIRE_TIME = 60;

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @Resource
    private ParaFeign paraFeign;

    @Override
    public CaptchaRespDto createCaptcha() {
        CaptchaRespDto respDto = new CaptchaRespDto();
        String uuid = newId();
        // 生成验证码
        Captcha captcha = new Captcha.Builder(130, 40).addText(RENDERER).addNoise().build();
        log.info("验证码: " + captcha);
        FastByteArrayOutputStream out = new FastByteArrayOutputStream();
        try {
            ImageIO.write(captcha.getImage(), "png", out);
        } catch (IOException e) {
            log.error("生成验证码错误：", e);
            throw new BusinessException(UserErrorNoEnum.GEN_CODE_ERROR);
        }
        // 将图片转换为Base64
        String imgBase64 = Base64.getEncoder().encodeToString(out.toByteArray());;
        respDto.setImgBase64(imgBase64);
        respDto.setUuid(uuid);

        // 保存登录验证码到redis
        String key = UserRedisKeyEnum.LOGIN_USER_LOGIN_CODE.getKey() + uuid;
        if (log.isDebugEnabled()) {
            log.info("LOGIN_USER_LOGIN_CODE ---> " + key);
        }
        redisTemplate.opsForValue().set(key, captcha.getAnswer(), LOGIN_CODE_EXPIRE_TIME, TimeUnit.SECONDS);
        if (log.isDebugEnabled()) {
            log.info("LOGIN_USER_LOGIN_CODE_VALUE ---> " + captcha.getAnswer());
        }
        return respDto;
    }

    @Override
    public void checkCaptcha(String key, String value) {
        if (StringUtils.isBlank(value)) {
            throw new BusinessException(UserErrorNoEnum.LOGIN_CODE_NULL);
        }
        if (StringUtils.isBlank(key)) {
            throw new BusinessException(UserErrorNoEnum.ERR_PARAMS);
        }
        String redisKey = UserRedisKeyEnum.LOGIN_USER_LOGIN_CODE.getKey() + key;
        if (log.isDebugEnabled()) {
            log.debug("LOGIN_USER_LOGIN_CODE_KEY ---> " + redisKey);
        }
        String code = redisTemplate.opsForValue().get(redisKey);
        if (log.isDebugEnabled()) {
            log.info("LOGIN_USER_LOGIN_CODE_VALUE ---> " + code);
        }
        if (StringUtils.isBlank(code)) {
            throw new BusinessException(UserErrorNoEnum.LOGIN_CODE_EXPIRE);
        }
        // 校验登录验证码
        if (!StringUtils.equalsIgnoreCase(value, code)) {
            throw new BusinessException(UserErrorNoEnum.LOGIN_CODE_ERROR);
        }
        redisTemplate.delete(redisKey);
    }

    @Override
    public Boolean captchaFlag() {
        String code = paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_100.getCode());
        return StringUtils.equals(code, ParaConstant.ON);
    }

    @Override
    public Boolean clientCaptchaFlag() {
        String code = paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_108.getCode());
        return StringUtils.equals(code, ParaConstant.ON);
    }
}
