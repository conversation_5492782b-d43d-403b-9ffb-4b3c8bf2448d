package com.wunding.learn.user.login.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p>
 * 访问TOKEN DTO
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">wgr</a>
 * @date 2023/8/16 14:21
 */
@Data
@Schema(name = "AccessTokenDTO", description = "访问TOKEN返回对象")
public class AccessTokenDTO {

    @Schema(description = "访问TOKEN")
    private String access_token;

    @Schema(description = "失效时间")
    private Integer expires_in;

}
