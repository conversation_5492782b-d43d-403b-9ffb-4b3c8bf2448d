package com.wunding.learn.user.login.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 注册用户dto
 *
 * <AUTHOR>
 * @date 2024/03/10
 */
@Data
@Accessors(chain = true)
@Schema(name = "RefreshTokenDTO", description = "刷新令牌参数对象")
public class RegisterUserDTO {

    /**
     * 账号
     */
    @Schema(description = "账号")
    private String refreshToken;

    /**
     * 密码
     */
    @Schema(description = "密码")
    private String passWord;

    /**
     * 手机号
     */
    @Schema(description = "手机号")
    private String phone;

    /**
     * 验证码
     */
    @Schema(description = "验证码")
    private String identifying;

    /**
     * 邮箱
     */
    @Schema(description = "email")
    private String email;


}
