package com.wunding.learn.user.login.rest;

import com.wunding.learn.common.bean.Result;
import com.wunding.learn.user.login.dto.CaptchaRespDto;
import com.wunding.learn.user.login.service.CaptchaService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: GYong
 * @Date: 2023/4/10 10:56
 */
@RestController
@Slf4j
@RequestMapping("${module.login.contentPath:/}captcha")
@Tag(name = "CaptchaRest", description = "登录验证码")
public class CaptchaRest {

    @Resource
    private CaptchaService captchaService;

    /**
     * 登录验证码
     */
    @GetMapping()
    @Operation(operationId = "captcha", summary = "登录验证码", description = "登录验证码")
    public Result<CaptchaRespDto> captcha() {
        return Result.success(captchaService.createCaptcha());
    }

    /**
     * 是否开启登录验证码
     */
    @GetMapping(value = "/captchaFlag")
    @Operation(operationId = "get_captchaFlag", summary = "是否开启登录验证码", description = "true 开启 false 关闭")
    public Result<Boolean> captchaFlag() {
        return Result.success(captchaService.captchaFlag());
    }

    /**
     * 学员端是否开启验证码
     * @return
     */
    @GetMapping(value = "/clientCaptchaFlag")
    @Operation(operationId = "clientCaptchaFlag_CaptchaRest", summary = "客户端是否开启登录验证码", description = "true 开启 false 关闭")
    public Result<Boolean> clientCaptchaFlag() {
        return Result.success(captchaService.clientCaptchaFlag());
    }

}
