package com.wunding.learn.user.login.service;

import com.wunding.learn.user.login.dto.CaptchaRespDto;

/**
 * <AUTHOR>
 */
public interface CaptchaService {

    /**
     * 生成登录验证码
     * @return
     */
    CaptchaRespDto createCaptcha();

    /**
     * 检查登录验证码
     * @param key key
     * @param value 验证码
     */
    void checkCaptcha(String key, String value);

    /**
     * 登录验证码是否开启
     * @return  true 开启 false 关闭
     */
    Boolean captchaFlag();

    /**
     * 客户端是否开启登录验证码
     * @return
     */
    Boolean clientCaptchaFlag();
}
