package com.wunding.learn.user.login.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p> 第三方登录-绑定系统账号请求参数对象
 *
 * <AUTHOR> href="mailto:<EMAIL>">Herilo</a>
 * @since 2023-05-24
 */
@Data
@Accessors(chain = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "BindReqDTO", description = "第三方登录-绑定系统账号请求参数对象")
public class BindUserReqDTO {

    @Schema(description = "入口类型：0-企业微信 1-微信公众号 2-微信小程序 3-钉钉 4-飞书")
    @NotNull
    private Integer entryType;

    @Schema(description = "系统账号，登录请求用户名")
    @NotNull
    private String userName;

    @Schema(description = "第三方账号，登录请求响应2215错误码（用户未绑定）时的data数据")
    @NotNull
    private String thirdId;

    @Schema(description = "企业微信类型（支持配置多个企业微信）：0-澳美点击 1-奥美领航")
    private String weChatType;

}
