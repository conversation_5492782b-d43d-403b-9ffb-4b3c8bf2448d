package com.wunding.learn.user.login.greater;

import com.wunding.learn.common.constant.other.AvailableEnum;
import com.wunding.learn.common.constant.other.JudgeEnum;
import com.wunding.learn.common.constant.other.Oauth2EntryTypeEnum;
import com.wunding.learn.common.constant.other.ParaConstant;
import com.wunding.learn.common.constant.user.UserErrorNoEnum;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.enums.other.SystemConfigCodeEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.util.crypto.RSAUtil;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.user.api.dto.LoginUserInfo;
import com.wunding.learn.user.api.dto.RoleDTO;
import com.wunding.learn.user.api.dto.ThirdAppConfigDTO;
import com.wunding.learn.user.api.dto.UserDTO;
import com.wunding.learn.user.api.dto.external.ExternalRedirectUrlDTO;
import com.wunding.learn.user.api.dto.external.ExternalSaveUser;
import com.wunding.learn.user.api.dto.external.ExternalSaveUser.ExternalOrgInfo;
import com.wunding.learn.user.api.dto.external.ExternalSaveUser.ExternalUserInfo;
import com.wunding.learn.user.api.dto.third.UserInfoDTO;
import com.wunding.learn.user.api.enums.DingTalkLoginSceneEnum;
import com.wunding.learn.user.api.service.ExternalFeign;
import com.wunding.learn.user.api.service.OrgFeign;
import com.wunding.learn.user.api.service.ParaFeign;
import com.wunding.learn.user.api.service.RoleFeign;
import com.wunding.learn.user.api.service.ThirdFeign;
import com.wunding.learn.user.api.service.UserFeign;
import com.wunding.learn.user.login.biz.ILoginBiz;
import com.wunding.learn.user.login.config.LoginSysConfig;
import com.wunding.learn.user.login.dto.ExternalResultDTO;
import com.wunding.learn.user.login.service.CaptchaService;
import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

/**
 * 用户服务
 *
 * <AUTHOR>
 * @date 2022/02/17
 */
@Slf4j
@Service
public class LoginService {

    @Resource
    private UserFeign userFeign;
    @Resource
    private RoleFeign roleFeign;
    @Resource
    private ParaFeign paraFeign;
    @Resource
    private CaptchaService captchaService;
    @Resource
    private ThirdFeign thirdFeign;
    @Resource
    @Lazy
    private ILoginBiz loginBiz;
    @Resource
    private LoginSysConfig loginSysConfig;
    @Resource
    private ExternalFeign externalFeign;
    @Resource
    private OrgFeign orgFeign;

    /**
     * 通过账号密码验证获取登录用户信息
     *
     * @param clientType 登录端类型
     * @param userName   用户名
     * @param password   密码
     * @param captcha    登录验证码
     * @param captchaId  验证码唯一id
     * @return {@link LoginUserInfo}
     */
    public LoginUserInfo getLoginUserInfoByPassword(LoginClientTypeEnum clientType, String userName, String password,
        String captcha, String captchaId) {
        // 管理端-系统管理-参数配置开启登录验证码验证
        checkCode(LoginClientTypeEnum.ADMIN, clientType, SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_100, captcha,
            captchaId);
        // 管理端-系统管理-参数配置开启学员端登录验证码验证
        checkCode(LoginClientTypeEnum.API, clientType, SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_108, captcha,
            captchaId);
        // 账号密码不为空时
        if (StringUtils.isNoneBlank(userName, password)) {
            // 查询用户信息
            UserDTO user = userFeign.getUserByLoginNameAndPassword(userName, password);
            if (user != null && Objects.equals(user.getIsAvailable(), AvailableEnum.NOT_AVAILABLE.getValue())) {
                throw new BusinessException(UserErrorNoEnum.ERR_USER_DISABLED);
            }
            return userToLoginUserInfo(user);
        }

        throw new BusinessException(UserErrorNoEnum.ERR_USERNAME_PWD);
    }

    private void checkCode(LoginClientTypeEnum api, LoginClientTypeEnum clientType,
        SystemConfigCodeEnum systemConfigCodeEnum, String captcha, String captchaId) {
        if (api.equals(clientType)) {
            String code = paraFeign.getParaValue(systemConfigCodeEnum.getCode());
            if (StringUtils.equals(code, ParaConstant.ON)) {
                if (StringUtils.isBlank(captcha)) {
                    throw new BusinessException(UserErrorNoEnum.LOGIN_CODE_NULL);
                }
                // 校验登录验证码
                captchaService.checkCaptcha(captchaId, captcha);
            }
        }
    }

    /**
     * 通过第三方验证获取登录用户信息
     *
     * @param entryType 入口类型：0-企业微信 1-微信公众号 2-微信小程序 3-钉钉 4-飞书
     * @param code      授权Code
     * @param openId    用户唯一标识
     * @param state     自定义参数
     * @return {@link LoginUserInfo}
     */
    public LoginUserInfo getLoginUserInfoByOauth2(Integer entryType, String code, String openId,
        String multiChannelType, String state) {
        LoginUserInfo loginUserInfo = null;
        Oauth2EntryTypeEnum oAuth2EntryTypeEnum = Oauth2EntryTypeEnum.get(entryType);
        if (null == oAuth2EntryTypeEnum) {
            throw new BusinessException(UserErrorNoEnum.ERR_OAUTH2_ILLEGAL_ENTRY_TYPE);
        }
        switch (oAuth2EntryTypeEnum) {
            case WECOM:
                loginUserInfo = weComOauth2Verify(code, multiChannelType);
                break;
            case WECHAT_OFFICIAL_ACCOUNTS:
                loginUserInfo = wechatOfficialAccountsVerify(code);
                break;
            case WECHAT_MINI_PROGRAMS:
                loginUserInfo = wechatMiniProgramsVerify(code, openId);
                break;
            case DING_TALK:
                loginUserInfo = dingTalkVerify(code, state);
                break;
            case FEISHU:
                break;
            default:
        }
        return loginUserInfo;
    }

    /**
     * 企业微信用户登录验证
     *
     * @param code 授权Code
     * @return {@link LoginUserInfo}
     */
    private LoginUserInfo weComOauth2Verify(String code, String multiChannelType) {

        // 获取访问用户身份
        UserInfoDTO userInfoDTO = thirdFeign.getWeComUserInfo(UserThreadContext.getTenantId(), code);
        log.info("Login By OAuth2 WeCom Get User Info DTO：{}", JsonUtil.objToJson(userInfoDTO));

        // 第三方用户授权Code无效
        if (null != userInfoDTO.getErrCode() && userInfoDTO.getErrCode() != 0) {
            log.error(userInfoDTO.getErrMsg());
            throw new BusinessException(UserErrorNoEnum.ERR_OAUTH2_INVALID_CODE);
        }

        // 校验单点登录用户 分两种情况判断
        if (StringUtils.isNotBlank(userInfoDTO.getUserId())) {
            // a) 情况一：当用户为企业成员时
            return verify(userInfoDTO.getUserId(), Oauth2EntryTypeEnum.WECOM, multiChannelType);
        } else {
            // b) 情况二：非企业成员时
            // 查询第三方应用-企业微信配置
            ThirdAppConfigDTO thirdAppConfigDTO = userFeign.getThirdAppConfig(Oauth2EntryTypeEnum.WECOM.getType(),
                multiChannelType);
            // 允许账号密码登录，允许为true，否则为false。不允许时响应错误码2216，允许时根据具体业务情况响应。
            boolean enableAccountPasswordLoginFlag = JudgeEnum.CONFIRM.getValue()
                .equals(thirdAppConfigDTO.getEnableAccountPasswordLogin());

            // 禁止账户密码登录（优先具体错误响应）
            if (!enableAccountPasswordLoginFlag) {
                throw new BusinessException(UserErrorNoEnum.ERR_FORBID_USERNAME_PWD_LOGIN);
            }
            // 第三方外部用户，允许账户密码登录
            throw new BusinessException(UserErrorNoEnum.ERR_OAUTH2_EXTERNAL_USER);
        }
    }

    /**
     * 微信公众号用户登录验证
     *
     * @param code 授权Code
     * @return {@link LoginUserInfo}
     */
    private LoginUserInfo wechatOfficialAccountsVerify(String code) {
        // 登录凭证校验
        UserInfoDTO userInfoDTO = thirdFeign.getWeChatOfficialAccountsUserInfo(UserThreadContext.getTenantId(), code);
        log.info("Login By OAuth2 WeChatOfficialAccounts Get User Info DTO：{}", JsonUtil.objToJson(userInfoDTO));

        // 第三方用户授权Code无效
        if (null != userInfoDTO.getErrCode() && userInfoDTO.getErrCode() != 0) {
            log.error(userInfoDTO.getErrMsg());
            throw new BusinessException(UserErrorNoEnum.ERR_OAUTH2_INVALID_CODE);
        }
        return verify(userInfoDTO.getOpenId(), Oauth2EntryTypeEnum.WECHAT_OFFICIAL_ACCOUNTS);
    }

    /**
     * 微信小程序用户登录验证
     *
     * @param code   授权Code
     * @param openId 用户唯一标识
     * @return {@link LoginUserInfo}
     */
    private LoginUserInfo wechatMiniProgramsVerify(String code, String openId) {
        // 登录凭证校验
        if (StringUtils.isNotBlank(code)) {
            UserInfoDTO userInfoDTO = thirdFeign.getWeChatMiniProgramsUserInfo(UserThreadContext.getTenantId(), code);
            log.info("Login By OAuth2 WeChatMiniPrograms Get User Info DTO：{}", JsonUtil.objToJson(userInfoDTO));

            // 第三方用户授权Code无效
            if (null != userInfoDTO.getErrCode() && userInfoDTO.getErrCode() != 0) {
                log.error(userInfoDTO.getErrMsg());
                throw new BusinessException(UserErrorNoEnum.ERR_OAUTH2_INVALID_CODE);
            }
            throw new BusinessException(UserErrorNoEnum.OAUTH2_GET_OPEN_ID, userInfoDTO.getOpenId());
        }
        return verify(openId, Oauth2EntryTypeEnum.WECHAT_MINI_PROGRAMS);
    }

    /**
     * 钉钉用户登录验证【企业内部应用免登/应用管理后台免登/登录第三方网站】
     *
     * @param code 免登授权码
     * @return {@link LoginUserInfo}
     */
    private LoginUserInfo dingTalkVerify(String code, String state) {
        // 验证钉钉身份验证支持的免登场景
        DingTalkLoginSceneEnum dingTalkLoginSceneEnum = DingTalkLoginSceneEnum.get(state);
        if (null == dingTalkLoginSceneEnum) {
            throw new BusinessException(UserErrorNoEnum.ERR_PARAMS);
        }

        // 登录凭证校验
        UserInfoDTO userInfoDTO = thirdFeign.getDingTalkUserInfo(UserThreadContext.getTenantId(), code,
            dingTalkLoginSceneEnum);
        log.info("Login By OAuth2 DingTalk Get User Info DTO：{}", JsonUtil.objToJson(userInfoDTO));

        // 第三方用户授权Code无效
        if (null != userInfoDTO.getErrCode() && userInfoDTO.getErrCode() != 0) {
            log.error(userInfoDTO.getErrMsg());
            throw new BusinessException(UserErrorNoEnum.ERR_OAUTH2_INVALID_CODE);
        }
        return verify(userInfoDTO.getUserId(), Oauth2EntryTypeEnum.DING_TALK);
    }

    /**
     * 第三方登录用户按同步配置统一校验
     *
     * @param thirdId             第三方用户ID
     * @param oauth2EntryTypeEnum {@link Oauth2EntryTypeEnum}
     * @return {@link LoginUserInfo}
     */
    private LoginUserInfo verify(String thirdId, Oauth2EntryTypeEnum oauth2EntryTypeEnum) {
        //  weChatType 默认值 0
        return verify(thirdId, oauth2EntryTypeEnum, "0");
    }

    /**
     * 第三方登录用户按同步配置统一校验
     *
     * @param thirdId             第三方用户ID
     * @param oauth2EntryTypeEnum {@link Oauth2EntryTypeEnum}
     * @return {@link LoginUserInfo}
     */
    private LoginUserInfo verify(String thirdId, Oauth2EntryTypeEnum oauth2EntryTypeEnum, String multiChannelType) {
        // 查询第三方应用-企业微信配置
        ThirdAppConfigDTO thirdAppConfigDTO = userFeign.getThirdAppConfig(oauth2EntryTypeEnum.getType(),
            multiChannelType);
        // 允许账号密码登录，允许为true，否则为false。不允许时响应错误码2216，允许时根据具体业务情况响应。
        boolean enableAccountPasswordLoginFlag = JudgeEnum.CONFIRM.getValue()
            .equals(thirdAppConfigDTO.getEnableAccountPasswordLogin());
        // 允许自行绑定，允许为true，否则为false。响应错误码2215，当允许时响应第三方账号数据，用于绑定业务。
        boolean enableSelfBindFlag = JudgeEnum.CONFIRM.getValue().equals(thirdAppConfigDTO.getEnableSelfBind());
        UserDTO user;
        // 按第三方同步用户id和用户来源查询用户
        if (oauth2EntryTypeEnum.equals(Oauth2EntryTypeEnum.WECOM)) {
            // 兼容多企微,多企微的 user_type 是横向扩展拼接的数据
            user = userFeign.getUserByThirdUserIdAndUserType(thirdId,
                oauth2EntryTypeEnum.getType().toString() + multiChannelType);
        } else {
            user = userFeign.getUserByThirdUserIdAndUserType(thirdId, oauth2EntryTypeEnum.getType().toString());

        }
        // 用户未绑定
        if (null == user) {
            // 禁止账户密码登录（优先具体错误响应）
            if (oauth2EntryTypeEnum.getAllowSync() == 1 && !enableAccountPasswordLoginFlag) {
                throw new BusinessException(UserErrorNoEnum.ERR_FORBID_USERNAME_PWD_LOGIN);
            }
            // 是否允许自行绑定，允许为1，否则为0。
            if (enableSelfBindFlag) {
                throw new BusinessException(UserErrorNoEnum.ERR_OAUTH2_USER_NOT_BIND, thirdId);
            }
            throw new BusinessException(UserErrorNoEnum.ERR_OAUTH2_USER_NOT_BIND);
        }
        // 用户已绑定 被禁用
        if (Objects.equals(user.getIsAvailable(), AvailableEnum.NOT_AVAILABLE.getValue())) {
            // 禁止账户密码登录（企业微信优先具体错误响应）
            if (oauth2EntryTypeEnum.getAllowSync() == 1 && !enableAccountPasswordLoginFlag) {
                throw new BusinessException(UserErrorNoEnum.ERR_FORBID_USERNAME_PWD_LOGIN,
                    "该用户已被禁用，请联系管理员启用");
            }
            throw new BusinessException(UserErrorNoEnum.ERR_USER_DISABLED);
        }
        return userToLoginUserInfo(user);
    }

    /**
     * 用户转登陆用户对象
     *
     * @param user 用户对象
     * @return 登陆用户对象
     */
    public LoginUserInfo userToLoginUserInfo(UserDTO user) {
        if (user == null) {
            throw new BusinessException(UserErrorNoEnum.ERR_USERNAME_PWD);
        }

        LoginUserInfo loginUserInfo = new LoginUserInfo();
        loginUserInfo.setUser(user);

        List<RoleDTO> roleList = roleFeign.getRoleByUserId(user.getId());

        loginUserInfo.setRoles(roleList.stream().map(RoleDTO::getId).collect(Collectors.toList()));

        return loginUserInfo;
    }

    public LoginUserInfo getLoginUserInfoByUserId(String userId) {
        return userToLoginUserInfo(userFeign.getUserById(userId));
    }

    public LoginUserInfo getLoginUserInfoByLoginName(String loginName) {
        return userToLoginUserInfo(userFeign.getUserByLoginName(loginName));
    }


    /**
    * 根据手机获取用户
    * @param phone
    * @return
    */
    public LoginUserInfo getLoginUserInfoByPhone(String phone){
        UserDTO userDTO = userFeign.getUserByPhone(phone);
        if(null == userDTO){
            return null;
        }
        return userToLoginUserInfo(userDTO);
    }

    /**
    * 根据手机号和微信openId创建用户信息
    * @param phone
    * @return
    */
    public UserDTO createUserByPhoneAndOpenId(String phone,String openId) {
        UserDTO result = userFeign.createUserByPhoneAndOpenId(phone,openId);
        return result;
    }

    /**
    * 绑定微信openId
    * @param userId
     * @param openId
    * @return
    */
    public void bindUserOpenId(String userId, String openId) {
        userFeign.bindUserOpenId(userId,openId, StringUtils.EMPTY);
    }

    /**
    * 绑定微信openId
    * @param userId
     * @param openId
    * @return
    */
    public void bindUserOpenId(String userId, String openId, String phone) {
        userFeign.bindUserOpenId(userId,openId, phone);
    }

    /**
    * 绑定微信openId
    * @param userId
    * @return
    */
    public void syncUserPhone(String userId, String phone) {
        userFeign.syncUserPhone(userId, phone);
    }

    public LoginUserInfo getLoginUserInfoByEncryptToken(String encryptToken) {

        ExternalResultDTO resultDTO;
        try {
            resultDTO = decryptToken(encryptToken);
        } catch (NoSuchAlgorithmException | InvalidKeySpecException e) {
            log.error("decryptTokenError", e);
            throw new BusinessException(UserErrorNoEnum.ERR_LOGIN_KEY_INVALID);
        }
        // 账号密码不为空时
        if (Objects.isNull(resultDTO) || (StringUtils.isEmpty(resultDTO.getUserId()) && StringUtils.isEmpty(
            resultDTO.getUserName()))) {
            throw new BusinessException(UserErrorNoEnum.ERR_USERNAME_NOT_EXIST);
        }
        log.info("getUserDTO:{}", resultDTO);
        UserDTO user;
        // 2 表示种树森林参考,具体联系中台相关负责人
        if (resultDTO.getActivitySchemeId() == 2 && resultDTO.getIsSupportPwd() == 0) {
            user = getUserDTOByScheme2(resultDTO);
        } else {
            user = getUserDTOByDefaultScheme(resultDTO);
        }

        return userToLoginUserInfo(user);
    }


    /**
     * 按默认方案获取用户数据
     *
     * @param resultDTO 结果dto
     * @return {@link UserDTO }
     */
    private UserDTO getUserDTOByDefaultScheme(ExternalResultDTO resultDTO) {
        ExternalOrgInfo orgInfo = new ExternalOrgInfo();
        orgInfo.setOrgCode(resultDTO.getOrgCode());
        orgInfo.setOrgName(resultDTO.getOrgName());

        ExternalUserInfo userInfo = new ExternalUserInfo();
        userInfo.setUserId(resultDTO.getUserId());
        userInfo.setLoginName(resultDTO.getUserName());
        userInfo.setFullName(resultDTO.getFullName());

        ExternalSaveUser saveUser = new ExternalSaveUser();
        saveUser.setUserInfo(userInfo);
        saveUser.setOrgInfo(orgInfo);
        externalFeign.saveUserAndOrg(saveUser);

        return userFeign.getUserById(resultDTO.getUserId());
    }

    /**
     * 通过模式2(种树森林)获取用户数据
     *
     * @param resultDTO 结果dto
     * @return {@link UserDTO }
     */
    private UserDTO getUserDTOByScheme2(ExternalResultDTO resultDTO) {
        // 查询用户信息
        UserDTO user;
        if (StringUtils.isEmpty(resultDTO.getUserId())) {
            user = userFeign.getUserByLoginName(resultDTO.getUserName());
        } else {
            user = userFeign.getUserById(resultDTO.getUserId());
        }
        if (user != null && Objects.equals(user.getIsAvailable(), AvailableEnum.NOT_AVAILABLE.getValue())) {
            throw new BusinessException(UserErrorNoEnum.ERR_USER_DISABLED);
        }
        return user;
    }

    private ExternalResultDTO decryptToken(String encryptToken)
        throws NoSuchAlgorithmException, InvalidKeySpecException {
        log.info("decryptToken:{}", encryptToken);
        String result = RSAUtil.privateDecrypt(encryptToken, RSAUtil.getPrivateKey(loginSysConfig.getPrivateKey()));
        ExternalResultDTO resultDTO = JsonUtil.jsonToObj(result, ExternalResultDTO.class);
        if (Objects.isNull(resultDTO)) {
            return null;
        }
        ExternalRedirectUrlDTO externalRedirectUrlDTO = new ExternalRedirectUrlDTO().setActivityId(
                resultDTO.getActivityId())
            .setCustomerId(resultDTO.getCustomerId())
            .setRedirectUrl(resultDTO.getRedirectUrl());
        userFeign.externalRedirectUrl(externalRedirectUrlDTO);
        return resultDTO;
    }
}
