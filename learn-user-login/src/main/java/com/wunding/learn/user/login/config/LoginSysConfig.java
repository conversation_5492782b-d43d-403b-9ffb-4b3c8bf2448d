package com.wunding.learn.user.login.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 配置文件读取
 *
 * <AUTHOR>
 * @date 2022/2/28
 */
@Configuration
@ConfigurationProperties("app")
@Data
@Slf4j
public class LoginSysConfig {

    /**
     * jwt 有效时长 单位秒
     */
    private Integer jwtExpire;

    /**
     * RSA 公钥
     */
    private String publicKey;

    /**
     * RSA 私钥钥
     */
    private String privateKey;
}
