package com.wunding.learn.user.login.greater;

import com.wunding.learn.common.util.bean.SpringUtil;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2022/2/16
 */
public class GrantBuilder {

    private GrantBuilder() {
    }

    private static final Map<GrantTypeEnum, ITokenGranter> GRANTER_POOL = new ConcurrentHashMap<>();

    static {
        init();
    }

    private static void init() {
        GRANTER_POOL.put(GrantTypeEnum.PASSWORD, SpringUtil.getBean(PasswordTokenGranter.class));
        GRANTER_POOL.put(GrantTypeEnum.OAUTH2, SpringUtil.getBean(Oauth2TokenGranter.class));
        GRANTER_POOL.put(GrantTypeEnum.EXTERNAL, SpringUtil.getBean(ExternalTokenGranter.class));
        GRANTER_POOL.put(GrantTypeEnum.CAS, SpringUtil.getBean(CasTokenGranter.class));
    }

    public static ITokenGranter getGranter(GrantTypeEnum type) {
        ITokenGranter granter = GRANTER_POOL.get(type);
        if (granter == null) {
            init();
            granter = GRANTER_POOL.get(type);
        }
        return granter;
    }
}
