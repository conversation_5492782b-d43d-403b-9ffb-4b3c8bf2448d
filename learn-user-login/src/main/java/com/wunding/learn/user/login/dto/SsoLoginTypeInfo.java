package com.wunding.learn.user.login.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class SsoLoginTypeInfo {

    @Schema(description = "单点登录类型名称 cas、oauth2、ldap")
    private String type;

    @Schema(description = "单点登录服务url")
    private String casUrl;

    @Schema(description = "单点登录默认登录url")
    private String defaultCasLoginUrl;

    @Schema(description = "单点登录默认登出url")
    private String casLogoutUrl;

    @Schema(description = "单点登录是否启用登出")
    private Integer enableLogout;
}
