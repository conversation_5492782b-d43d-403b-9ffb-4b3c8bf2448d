package com.wunding.learn.user.login.filter;

import com.wunding.learn.user.login.config.DynamicServicePropertiesFactory;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.cas.ServiceProperties;
import org.springframework.security.cas.web.CasAuthenticationFilter;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 自定义CAS认证过滤器，支持额外参数
 */
@Slf4j
public class CustomCasAuthenticationFilter extends CasAuthenticationFilter {

    private static final String CUSTOM_PARAMETERS_ATTRIBUTE = "customParameters";
    
    // 移除自动注入，避免循环依赖
    // @Autowired
    private DynamicServicePropertiesFactory servicePropertiesFactory;
    

    /**
     * 设置DynamicServicePropertiesFactory
     */
    public void setDynamicServicePropertiesFactory(DynamicServicePropertiesFactory servicePropertiesFactory) {
        this.servicePropertiesFactory = servicePropertiesFactory;
    }

    @Override
    public Authentication attemptAuthentication(HttpServletRequest request, HttpServletResponse response) 
            throws AuthenticationException, IOException {
        
        // 收集所有请求参数（除了ticket参数外的所有参数）
        Map<String, String> customParameters = new HashMap<>();
        request.getParameterMap().forEach((key, values) -> {
            if (values != null && values.length > 0 && !key.equals(ServiceProperties.DEFAULT_CAS_ARTIFACT_PARAMETER)) {
                customParameters.put(key, values[0]);
                log.info("收集自定义参数: {} = {}", key, values[0]);
            }
        });
        
        // 将自定义参数存储到请求属性中，以便后续处理器访问
        request.setAttribute(CUSTOM_PARAMETERS_ATTRIBUTE, customParameters);
        
        // 获取完整请求URL（包含所有参数）
        String fullUrl = request.getRequestURL().toString();
        String queryString = request.getQueryString();
        if (queryString != null && !queryString.isEmpty()) {
            fullUrl += "?" + queryString;
        }
        log.info("原始请求完整URL: {}", fullUrl);
        
        // 动态创建ServiceProperties，确保URL包含所有参数
        if (servicePropertiesFactory != null) {
            ServiceProperties dynamicServiceProperties = servicePropertiesFactory.createServiceProperties(request);
            this.setServiceProperties(dynamicServiceProperties);
            log.info("设置动态ServiceProperties，URL: {}", dynamicServiceProperties.getService());
        } else {
            log.warn("servicePropertiesFactory未设置，无法创建动态ServiceProperties");
        }
        
        // 检查是否有ticket参数
        String ticket = request.getParameter(ServiceProperties.DEFAULT_CAS_ARTIFACT_PARAMETER);
        if (ticket == null || ticket.isEmpty()) {
            log.error("缺少ticket参数，无法进行CAS认证");
            response.sendError(HttpServletResponse.SC_UNAUTHORIZED, "Missing CAS ticket");
            return null;
        }
        
        log.info("CAS认证开始，票据: {}", ticket);
        
        // 调用父类方法继续处理认证
        return super.attemptAuthentication(request, response);
    }
    
    /**
     * 获取请求中的自定义参数
     */
    public static Map<String, String> getCustomParameters(HttpServletRequest request) {
        @SuppressWarnings("unchecked")
        Map<String, String> customParameters = (Map<String, String>) request.getAttribute(CUSTOM_PARAMETERS_ATTRIBUTE);
        return customParameters != null ? customParameters : new HashMap<>();
    }
} 