package com.wunding.learn.user.login.handler;

import com.wunding.learn.common.constant.http.TokenConstant;
import com.wunding.learn.user.login.filter.CustomCasAuthenticationFilter;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.SavedRequestAwareAuthenticationSuccessHandler;
import org.springframework.util.StringUtils;

/**
 * CAS认证成功处理器
 * 在CAS认证成功后生成JWT令牌
 */
@Slf4j
public class CasAuthSuccessHandler1 extends SavedRequestAwareAuthenticationSuccessHandler {


    @Override
    @SuppressWarnings("java:S3776")
    public void onAuthenticationSuccess(HttpServletRequest request, 
                                      HttpServletResponse response, 
                                      Authentication authentication) throws IOException, ServletException {
        log.info("CAS认证成功，准备重定向回前端页面，让前端调登录接口获取JWT");

        // 记录请求信息便于调试
        if (log.isDebugEnabled()) {
            request.getParameterNames().asIterator()
                .forEachRemaining(name -> log.debug("请求参数：{} = {}", name, request.getParameter(name)));
            request.getHeaderNames().asIterator()
                .forEachRemaining(name -> log.debug("请求头：{} = {}", name, request.getHeader(name)));
        }

        // 获取认证用户ID
        String userId = authentication.getName();
        log.info("认证成功的用户ID: {}", userId);

        // 获取设备ID和客户端类型
        String deviceId = request.getHeader(TokenConstant.DEVICE_ID);
        if (!StringUtils.hasText(deviceId)) {
            deviceId = UUID.randomUUID().toString();
        }

        String clientType = request.getHeader(TokenConstant.CLIENT_TYPE);
        if (org.apache.commons.lang3.StringUtils.isBlank(clientType)) {
            clientType = request.getParameter(TokenConstant.CLIENT_TYPE);
        }
        if (!StringUtils.hasText(clientType)) {
            clientType = "admin";
        }

        String redirect = request.getParameter("redirect");

        // 获取自定义参数
        Map<String, String> customParameters = CustomCasAuthenticationFilter.getCustomParameters(request);
        
        // 构建基本重定向URL
        StringBuilder redirectUrl = new StringBuilder();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(redirect)) {
            if(redirect.contains("casType=cas")){
                redirectUrl.append(redirect);
            }else if(redirect.contains("?")){
                redirectUrl.append(redirect);
                redirectUrl.append("?casType=cas");
            }else{
                redirectUrl.append(redirect);
                redirectUrl.append("&casType=cas");
            }
        }else{
            redirectUrl.append("/../");
            if(Objects.equals(clientType,"admin")){
                redirectUrl.append(clientType);
            }else if(Objects.equals(clientType, "client") || Objects.equals(clientType, "api")){
                redirectUrl.append("clientcn");
            }
            redirectUrl.append("/login");
            redirectUrl.append("?casType=cas");
            if(!customParameters.containsKey("userId")){
                redirectUrl.append("&userId=").append(userId);
            }
            if(!customParameters.containsKey(TokenConstant.DEVICE_ID)){
                redirectUrl.append("&"+TokenConstant.DEVICE_ID+"=").append(deviceId);
            }
            if(!customParameters.containsKey(TokenConstant.CLIENT_TYPE)){
                redirectUrl.append("&"+TokenConstant.CLIENT_TYPE+"=").append(clientType);
            }
            if(!customParameters.containsKey("t")){
                String t = System.currentTimeMillis()+"";
                redirectUrl.append("&t=").append(t);
            }

        }

        // 添加所有自定义参数
        if (!customParameters.isEmpty()) {
            customParameters.remove("t");
            customParameters.forEach(
                (key, value) -> redirectUrl.append("&").append(key).append("=").append(value)
            );
        }
        
        log.info("重定向URL: {}", redirectUrl.toString());
        getRedirectStrategy().sendRedirect(request, response, redirectUrl.toString());
    }
} 