package com.wunding.learn.user.login.greater;

import com.wunding.learn.user.api.dto.LoginUserInfo;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * 账号密码类型授权
 *
 * <AUTHOR>
 * @date 2022/2/16
 */
@Component
public class PasswordTokenGranter implements ITokenGranter {

    @Resource
    private LoginService loginService;

    @Override
    public LoginUserInfo grant(GrantParameter grantParameter) {
        return loginService.getLoginUserInfoByPassword(grantParameter.getClientType(), grantParameter.getUserName(),
            grantParameter.getPassword(), grantParameter.getCaptcha(), grantParameter.getCaptchaId());
    }

}
