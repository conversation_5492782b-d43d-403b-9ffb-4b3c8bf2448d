package com.wunding.learn.user.login.biz;

import com.wunding.learn.common.bean.Result;
import com.wunding.learn.user.api.dto.AccountSyncDTO;
import com.wunding.learn.user.api.dto.AccountSyncRespDTO;
import com.wunding.learn.user.api.dto.LoginUserInfo;
import com.wunding.learn.user.login.dto.AccessTokenDTO;
import com.wunding.learn.user.login.dto.AppInfoListDTO;
import com.wunding.learn.user.login.dto.BindUserReqDTO;
import com.wunding.learn.user.login.dto.ClaimAccountDTO;
import com.wunding.learn.user.login.dto.ClaimAccountRespDTO;
import com.wunding.learn.user.login.dto.JsapiTicketReqDTO;
import com.wunding.learn.user.login.dto.LoginRequestDTO;
import com.wunding.learn.user.login.dto.LoginRespDto;
import com.wunding.learn.user.login.dto.LoginSkipDTO;
import com.wunding.learn.user.login.dto.RefreshTokenDTO;

/**
 * <p> 登录 业务服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">Herilo</a>
 * @since 2023-05-23
 */
public interface ILoginBiz {

    /**
     * 登录业务接口
     *
     * @param loginRequestDTO {@link LoginRequestDTO}
     * @return {@link Result}<{@link LoginRespDto}>
     */
    Result<LoginRespDto> login(LoginRequestDTO loginRequestDTO);

    /**
     * 刷新令牌业务接口
     *
     * @param refreshTokenDTO {@link RefreshTokenDTO}
     * @return {@link Result}<{@link LoginRespDto}>
     */
    Result<LoginRespDto> refreshToken(RefreshTokenDTO refreshTokenDTO);

    /**
     * 获取企业jsapi_ticket
     *
     * @param jsapiTicketReqDTO {@link JsapiTicketReqDTO}
     * @return 企业jsapi_ticket
     */
    String getJsapiTicket(JsapiTicketReqDTO jsapiTicketReqDTO);

    /**
     * 绑定系统用户业务接口
     *
     * @param bindUserReqDTO {@link BindUserReqDTO}
     */
    void bindSysUser(BindUserReqDTO bindUserReqDTO);

    /**
     * 获取当前系统第三方企业应用信息
     *
     * @return 企业应用信息
     */
    AppInfoListDTO getAppInfo();

    /**
     * 获取当前登陆的用户信息业务接口
     *
     * @return {@link Result}<{@link LoginUserInfo}>
     */
    Result<LoginUserInfo> getLoginUserInfo();

    /**
     * 获取端跳转的登录key
     *
     * @return
     */
    String getLoginKey();

    /**
     * 跳转接口
     *
     * @param loginSkipDTO
     * @return
     */
    LoginRespDto loginSkip(LoginSkipDTO loginSkipDTO);

    /**
     * 获取访问TOKEN
     *
     * @param appId
     * @param secret
     * @return
     */
    AccessTokenDTO getTokenByAppIdAndSecret(String appId, String secret);


    /**
     * 处理登录用户信息
     *
     * @param loginUserInfo 登录用户信息
     * @return {@link Result}<{@link LoginRespDto}>
     */
    Result<LoginRespDto> processLoginUserInfo(LoginUserInfo loginUserInfo, String deviceId, String clientType);
    
    /**
     * 根据用户ID获取用户信息
     * 主要用于 CAS 认证成功后获取用户详细信息
     *
     * @param userId 用户ID
     * @return {@link LoginUserInfo} 用户信息
     */
    LoginUserInfo getUserInfo(String userId);

    /**
     * CAS账户同步
     *
     * @param accountSyncDTO 账户同步请求
     * @return 同步结果
     */
    AccountSyncRespDTO syncAccount(AccountSyncDTO accountSyncDTO);
    
    /**
     * 账号认领
     *
     * @param claimAccountDTO 账号认领请求参数
     * @return 认领结果
     */
    ClaimAccountRespDTO claimAccount(ClaimAccountDTO claimAccountDTO);
}
