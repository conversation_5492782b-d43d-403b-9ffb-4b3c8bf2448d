package com.wunding.learn.user.login.greater;

import com.wunding.learn.user.api.dto.LoginUserInfo;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * 第三方类型授权
 *
 * <AUTHOR> href="mailto:<EMAIL>">Herilo</a>
 * @date 2023/5/22
 */
@Component
public class Oauth2TokenGranter implements ITokenGranter {

    @Resource
    private LoginService loginService;

    @Override
    public LoginUserInfo grant(GrantParameter grantParameter) {
        return loginService.getLoginUserInfoByOauth2(grantParameter.getEntryType(), grantParameter.getCode(),
            grantParameter.getOpenId(), grantParameter.getWeChatType(), grantParameter.getState());
    }

}
