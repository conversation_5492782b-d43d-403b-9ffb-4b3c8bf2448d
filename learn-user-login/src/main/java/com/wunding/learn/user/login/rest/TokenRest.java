package com.wunding.learn.user.login.rest;

import com.wunding.learn.common.bean.Result;
import com.wunding.learn.user.login.biz.ILoginBiz;
import com.wunding.learn.user.login.dto.AccessTokenDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 登录相关接口
 *
 * <AUTHOR>
 * @date 2022/2/16
 */
@Slf4j
@RestController
@RequestMapping("${module.login.contentPath:/}")
@Tag(description = "Token控制器", name = "TokenRest")
public class TokenRest {

    @Resource
    private ILoginBiz loginBiz;

    @GetMapping("/token")
    @Operation(operationId = "getTokenByAppIdAndSecret", summary = "根据appId和secret获取token", description = "根据appId和secret获取token")
    public Result<AccessTokenDTO> rightIdList(
        @Parameter(description = "appId", required = true) @RequestParam(value = "appId") String appId,
        @Parameter(description = "秘钥", required = true) @RequestParam(value = "secret") String secret) {
        return Result.success(loginBiz.getTokenByAppIdAndSecret(appId,secret));
    }

}
