package com.wunding.learn.user.login.dto;

import com.wunding.learn.common.util.http.WebUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.servlet.http.HttpServletRequest;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/2/22
 */
@Data
@Schema(name = "LoginRespDto", description = "用户登陆返回信息")
public class LoginRespDto {

    /**
     * 访问令牌值
     */
    @Schema(description = "访问令牌值")
    private String token;

    /**
     * 刷新令牌值
     */
    @Schema(description = "刷新令牌值")
    private String refreshToken;

    /**
     * 过期秒数
     */
    @Schema(description = "令牌过期时间")
    private Integer expire;

    /**
     * 用户id
     */
    @Schema(description = "用户id")
    private String userId;

    /**
     * 用户名
     */
    @Schema(description = "用户名")
    private String userName;

    /**
     * 用户类型
     */
    @Schema(description = "用户类型[0：内部用户，1：外部用户，2：机构用户，默认0]")
    private Integer userType;

    /**
     * 用户手机号
     */
    @Schema(description = "用户手机号")
    private String phone;

    /**
     * 微信用户openID
     */
    @Schema(description = "微信用户openID")
    private String openId;

    /**
     * 是否修改密码 0 不需要，1 需要
     */
    @Schema(description = "是否修改密码 0不需要，1需要")
    private Integer changePassword;

    /**
     * 防挂机相关配置
     */
    @Schema(description = "防挂机相关配置")
    private HangupInfoDTO hangupInfo;

    /**
     * 推送相关信息
     */
    @Schema(description = "推送相关信息")
    private PushInfoDTO pushInfo;

    /**
     * 当前登陆设备id，如果无法取到自动生成随机数
     */
    @Schema(description = "当前登陆设备id，如果无法取到自动生成随机数")
    private String deviceId;

    /**
     * 格力appId
     */
    @Schema(description = "格力appId")
    private String appId;

    /**
     * 格力appKey
     */
    @Schema(description = "格力appKey")
    private String appKey;

    /**
     * 当前登陆会话id
     */
    @Schema(description = "企业对象")
    private EnterPriseDTO enterprise = new EnterPriseDTO();

    /**
     * 接口地址
     */
    @Schema(description = "接口地址")
    private String url = getDomainUrl() + "/javaxieyi/api.do";

    /**
     * 是否是管理员
     */
    @Schema(description = "是否是管理员  0否 1是")
    private Integer isAdmin;

    private static String getDomainUrl() {
        HttpServletRequest request = WebUtil.getRequest();
        if (request == null) {
            return "";
        }
        // 这里添加了连接符号，比较的时候需要注意
        String scheme = request.getScheme() + "://";
        String serverName = request.getServerName();
        // http 80 端口返回url不需要带80，https 443端口，返回url不需要带443
        String serverPort =
            (request.getServerPort() == 80 && "http://".equalsIgnoreCase(scheme)) || (request.getServerPort() == 443
                && "https://".equalsIgnoreCase(scheme)) ? "" : ":" + request.getServerPort();

        return scheme + serverName + serverPort;
    }
}
