package com.wunding.learn.user.login.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p> 获取当前系统第三方企业应用信息
 *
 * <AUTHOR> href="mailto:<EMAIL>">Herilo</a>
 * @since 2023-05-25
 */
@Data
@Accessors(chain = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "AppInfoListDTO", description = "获取当前系统第三方应用信息dto对象")
public class AppInfoListDTO {

    @Schema(description = "允许账号密码登录 0否 1是")
    private Integer enableAccountPasswordLogin;

    @Schema(description = "第三方企业应用信息列表")
    private List<AppInfoDTO> appInfoDTOList;

}
