package com.wunding.learn.user.login.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p> 第三方登录-获取企业jsapi_ticket请求参数对象
 *
 * <AUTHOR> href="mailto:<EMAIL>">Herilo</a>
 * @since 2023-05-23
 */
@Data
@Accessors(chain = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "JsapiTicketReqDTO", description = "第三方登录-获取企业jsapi_ticket请求参数对象")
public class JsapiTicketReqDTO {

    @Schema(description = "入口类型：0-企业微信 1-微信公众号 2-微信小程序 3-钉钉 4-飞书")
    @NotNull
    private Integer entryType;

}
