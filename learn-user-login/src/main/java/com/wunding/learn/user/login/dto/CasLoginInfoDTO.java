package com.wunding.learn.user.login.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * CAS登录信息DTO
 * 用于前端自行处理CAS登录流程
 * <AUTHOR>
 */
@Data
@Schema(description = "CAS登录信息")
public class CasLoginInfoDTO {

    @Schema(description = "CAS服务器登录URL")
    private String casLoginUrl;
    
    @Schema(description = "服务URL（回调地址）")
    private String serviceUrl;
    
    @Schema(description = "应用内部的票据验证路径")
    private String callbackUrl;
} 