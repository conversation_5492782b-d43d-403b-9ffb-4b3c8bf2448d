package com.wunding.learn.user.login.rest;


import com.wunding.learn.common.bean.Result;
import com.wunding.learn.user.api.dto.third.wechat.GetUserPhoneDTO;
import com.wunding.learn.user.login.dto.LoginRespDto;
import com.wunding.learn.user.login.service.UserClientRedisService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 报名控制层
 *
 * <AUTHOR>
 * @date 2022/07/13
 */
@RestController
@RequestMapping("${module.login.contentPath:/}enroll/api")
@Tag(name = "EnrollController", description = "小程序报名相关接口")
public class EnrollController {

    @Resource
    private UserClientRedisService userClientRedisService;

    @Operation(operationId = "EnrollController_getWxUserPhone", summary = "获取小程序用户的手机号码", description = "此接口用于获取用户的手机号码")
    @GetMapping("/getWxUserPhone")
    public Result<GetUserPhoneDTO> getWxUserPhone(
        @Parameter(description = "点击小程序获取的code", required = true) String code) {
        return userClientRedisService.getWxUserPhoneNumAndLogin(code, false);
    }

    @Operation(operationId = "EnrollController_registerWxUserPhone", summary = "根据获取的小程序手机号进行注册", description = "根据获取的小程序手机号进行注册 (直接获取登录token)")
    @PostMapping("/register")
    public Result<LoginRespDto> registerWxUserPhone(
        @Parameter(description = "小程序获取的用户手机号", required = true)
        @RequestParam String phone,
        @Parameter(description = "授权码", required = true)
        @RequestParam String code) {
        //当前仅支持小程序注册用户
        return userClientRedisService.registerWxUserPhone(phone, code);
    }


}
