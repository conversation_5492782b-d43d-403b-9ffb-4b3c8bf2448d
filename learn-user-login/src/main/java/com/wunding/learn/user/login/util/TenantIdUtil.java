package com.wunding.learn.user.login.util;

import com.wunding.learn.common.constant.http.TokenConstant;
import com.wunding.learn.common.context.user.UserThreadContext;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */
@Slf4j
public class TenantIdUtil {

    private static final String DEFAULT_TENANT_ID = "default";

    private TenantIdUtil(){

    }

    public static String getTenantId(int tenantType,HttpServletRequest request){
        if(request == null){
            UserThreadContext.setTenantId(DEFAULT_TENANT_ID);
            return DEFAULT_TENANT_ID;
        }
        String tenantId = request.getHeader(TokenConstant.TENANT_ID);
        // 默认数据源给默认的租户id
        switch (tenantType) {
            case -1:
                // 单租户部署
            case 1:
                tenantId = DEFAULT_TENANT_ID;
                break;
            case 2:
                // TODO 不同租户部署方式
                break;
            default:
                break;
        }
        if (StringUtils.isBlank(tenantId)) {
            int index = request.getServerName().indexOf(".");
            if (index > 0) {
                String serverNameTenantId = request.getServerName().substring(0, index);
                log.debug("serverNameTenantId:{}", serverNameTenantId);
                tenantId = serverNameTenantId;
            }
        }
        if (StringUtils.isBlank(tenantId)) {
            tenantId = "1";
        }
        UserThreadContext.setTenantId(tenantId);
        return tenantId;
    }
}
