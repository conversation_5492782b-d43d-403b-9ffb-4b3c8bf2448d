package com.wunding.learn.user.login.service.impl;

import com.wunding.learn.common.bean.Result;
import com.wunding.learn.common.constant.http.TokenConstant;
import com.wunding.learn.common.constant.other.Oauth2EntryTypeEnum;
import com.wunding.learn.common.constant.user.UserErrorNoEnum;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.redis.util.RedisLockUtil;
import com.wunding.learn.common.util.http.WebUtil;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.user.api.dto.LoginUserInfo;
import com.wunding.learn.user.api.dto.UserDTO;
import com.wunding.learn.user.api.dto.third.UserInfoDTO;
import com.wunding.learn.user.api.dto.third.wechat.GetUserPhoneDTO;
import com.wunding.learn.user.api.service.ThirdFeign;
import com.wunding.learn.user.api.service.UserFeign;
import com.wunding.learn.user.login.biz.ILoginBiz;
import com.wunding.learn.user.login.dto.LoginRespDto;
import com.wunding.learn.user.login.greater.LoginClientTypeEnum;
import com.wunding.learn.user.login.greater.LoginService;
import com.wunding.learn.user.login.service.UserClientRedisService;
import java.util.Objects;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 用户登录
 *
 * <AUTHOR>
 * @date 2020/11/05
 */
@Service("userClientRedisService")
@Slf4j
public class UserClientRedisServiceImpl implements UserClientRedisService {

    @Resource
    private ThirdFeign thirdFeign;
    @Resource
    private UserFeign userFeign;
    @Resource
    private LoginService loginService;
    @Resource
    private ILoginBiz loginBiz;

    @Override
    public Result<GetUserPhoneDTO> getWxUserPhoneNumAndLogin(String code, boolean isForceRefreshToken) {
        GetUserPhoneDTO userPhoneDTO = thirdFeign.getWeChatMiniProgramsPhoneNum(
            UserThreadContext.getTenantId(), code, isForceRefreshToken);
        if (Objects.nonNull(userFeign.getUserByLoginName(userPhoneDTO.getPhoneNum()))) {
            userPhoneDTO.setIsRegistered(true);
        }
        return Result.success(userPhoneDTO);
    }

    @Override
    public Result<LoginRespDto> registerWxUserPhone(String phone, String code) {
        String deviceId = Objects.toString(WebUtil.getHeader(TokenConstant.DEVICE_ID), StringUtil.newId());
        String clientType = WebUtil.getHeader(TokenConstant.CLIENT_TYPE);
        if (StringUtils.isBlank(clientType)) {
            throw new BusinessException(UserErrorNoEnum.CLIENT_TYPE_NULL);
        }
        // 登录端类型
        LoginClientTypeEnum loginClientType = LoginClientTypeEnum.get(clientType);
        if (null == loginClientType) {
            throw new BusinessException(UserErrorNoEnum.ERR_PARAMS);
        }
        String lockKey = "registerWxUserPhone:" + UserThreadContext.getTenantId() + ":" + phone;
        UserDTO userDTO = null;
        try {
            RedisLockUtil.acquire(lockKey, 10L);
            // 判断当前手机用户是否存在,不存在则根据手机号新建用户
            userDTO = userFeign.saveUserByPhoneNum(phone, GeneralJudgeEnum.NEGATIVE.getValue());
        } catch (Exception e) {
            log.error("registerWxUserPhone error", e);
        } finally {
            RedisLockUtil.release(lockKey);
        }
        if (Objects.isNull(userDTO)) {
            throw new BusinessException(UserErrorNoEnum.USER_NOT_EXISTS);
        }
        LoginUserInfo loginUserInfo = loginService.userToLoginUserInfo(userDTO);
        try {
            // 小程序/公众号手机号绑定用户
            UserInfoDTO miniProgramsUserInfo = thirdFeign.getWeChatMiniProgramsUserInfo(
                UserThreadContext.getTenantId(), code);

            userFeign.bindSysUser(Oauth2EntryTypeEnum.WECHAT_MINI_PROGRAMS.getType().toString(), userDTO.getId(),
                miniProgramsUserInfo.getOpenId());
        } catch (Exception e) {
            log.error("未绑定到系统用户,phone:{}", phone, e);
        }

        return loginBiz.processLoginUserInfo(loginUserInfo, deviceId, clientType);

    }
}
