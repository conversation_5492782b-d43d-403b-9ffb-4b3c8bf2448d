package com.wunding.learn.user.login.greater;

import java.util.Objects;
import lombok.Getter;

/**
 * 登录端类型
 *
 * <AUTHOR> href="mailto:<EMAIL>">Herilo</a>
 * @date 2023/5/22
 */
@Getter
public enum LoginClientTypeEnum {

    /**
     * 管理端
     */
    ADMIN("admin"),

    /**
     * 学员端
     */
    API("api"),

    /**
     * open_api访问
     */
    OPEN_API("open_api");

    private final String type;

    LoginClientTypeEnum(String type) {
        this.type = type;
    }

    /**
     * 获取授权登录方式
     *
     * @param type 登录类型
     * @return {@link LoginClientTypeEnum}
     */
    public static LoginClientTypeEnum get(String type) {
        for (LoginClientTypeEnum value : values()) {
            if (Objects.equals(value.type, type)) {
                return value;
            }
        }
        return null;
    }

}
