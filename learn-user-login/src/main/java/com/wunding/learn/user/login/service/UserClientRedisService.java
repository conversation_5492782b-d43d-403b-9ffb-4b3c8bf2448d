package com.wunding.learn.user.login.service;

import com.wunding.learn.common.bean.Result;
import com.wunding.learn.user.api.dto.third.wechat.GetUserPhoneDTO;
import com.wunding.learn.user.login.dto.LoginRespDto;

/**
 * 用户缓存相关接口
 *
 * <AUTHOR>
 */
public interface UserClientRedisService {


    /**
     * 获取wx用户电话号码并登录
     *
     * @param code                授权码
     * @param isForceRefreshToken 是强制刷新令牌
     * @return {@link Result}<{@link LoginRespDto}>
     */
    Result<GetUserPhoneDTO> getWxUserPhoneNumAndLogin(String code, boolean isForceRefreshToken);

    Result<LoginRespDto> registerWxUserPhone(String phone, String code);
}
