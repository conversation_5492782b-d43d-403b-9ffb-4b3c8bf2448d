package com.wunding.learn.user.login.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 微信登录信息对象
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON><PERSON><PERSON><PERSON></a>
 * @date 2023/8/17 14:34
 */
@Accessors(chain = true)
@Data
@Schema(name = "WeChatLoginDTO", description = "微信登录信息对象")
public class WeChatLoginDTO {

    @Schema(description = "微信手机号")
    @NotNull(message = "微信手机号不能为空")
    private String phone;

    @Schema(description = "用户id,为空时则会创建一个新的用户类型为外部个人的系统用户")
    private String userId;

    @Schema(description = "微信openId")
    @NotNull(message = "微信openId不能为空")
    private String openId;
}
