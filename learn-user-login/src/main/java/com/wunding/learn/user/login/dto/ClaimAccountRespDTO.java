package com.wunding.learn.user.login.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 账号认领响应结果
 *
 * <AUTHOR>
 * @date 2025/05/28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "账号认领响应结果")
public class ClaimAccountRespDTO {

    /**
     * 返回结果
     */
    @Schema(description = "返回结果", example = "true: 认领成功, false: 认领失败")
    private Boolean success;

    /**
     * 返回消息
     */
    @Schema(description = "返回消息", example = "认领成功")
    private String message;

    /**
     * 扩展信息
     */
    @Schema(description = "扩展信息")
    private Object extend;

    /**
     * 成功结果
     *
     * @return 成功结果
     */
    public static ClaimAccountRespDTO success() {
        return ClaimAccountRespDTO.builder()
                .success(true)
                .message("认领成功")
                .build();
    }

    /**
     * 失败结果
     *
     * @param message 失败消息
     * @return 失败结果
     */
    public static ClaimAccountRespDTO fail(String message) {
        return ClaimAccountRespDTO.builder()
                .success(false)
                .message(message)
                .build();
    }
}
