FROM registry.cn-shenzhen.aliyuncs.com/wunding-base/arthas:4.0.5-no-jdk

FROM registry.cn-shenzhen.aliyuncs.com/wunding-base/amazoncorretto:21.0.7.6-1-debian12 AS builder
WORKDIR /app/
COPY ./target/learn-user-login-*-fat.jar /app/login.jar
RUN java -Djarmode=layertools -jar login.jar extract

FROM registry.cn-shenzhen.aliyuncs.com/wunding-base/amazoncorretto:21.0.7.6-1-debian12

COPY ./docker-entrypoint.sh /app/docker-entrypoint.sh
COPY --from=builder app/dependencies/ /app
COPY --from=builder app/snapshot-dependencies/ /app
COPY --from=builder app/spring-boot-loader/ /app
COPY --from=0 /opt/arthas /app/arthas
# 保证几乎只更新代码层
COPY --from=builder app/application/ /app

WORKDIR /app/

# 仅arthas运行需要，相关jdk命令运行需要有实际的用户存在
RUN groupadd -r myuser -g 1000 && useradd -d /home/<USER>/bin/bash -g myuser myuser && chmod -R 755 /app  && mkdir /app/BOOT-INF/classes/i18n/ && chown -R 1000:1000 /app/BOOT-INF/classes/i18n/

ENTRYPOINT ["/app/docker-entrypoint.sh"]
# ENTRYPOINT ["java","-Duser.timezone=GMT+08","org.springframework.boot.loader.JarLauncher"]

EXPOSE 28002