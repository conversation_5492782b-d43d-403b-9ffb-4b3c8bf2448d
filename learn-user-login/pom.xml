<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.wunding</groupId>
        <artifactId>learn-user</artifactId>

        <version>${revision}</version>
    </parent>
    <artifactId>learn-user-login</artifactId>
    <name>learn-user-login</name>
    <description>learn-user-login</description>

    <dependencies>

        <dependency>
            <groupId>com.wunding</groupId>
            <artifactId>learn-common-base</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.seata</groupId>
                    <artifactId>seata-spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.seata</groupId>
                    <artifactId>seata-serializer-kryo</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wunding</groupId>
                    <artifactId>learn-common-datasource</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>druid-spring-boot-3-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wunding</groupId>
                    <artifactId>learn-common-mybatis</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-jdbc</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
        </dependency>

        <!-- CAS Client -->
        <dependency>
            <groupId>org.jasig.cas.client</groupId>
            <artifactId>cas-client-core</artifactId>
        </dependency>

        <!-- Spring Security CAS -->
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-cas</artifactId>
        </dependency>

        <!-- Spring Boot Starter Security -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>

        <dependency>
            <groupId>nl.captcha</groupId>
            <artifactId>captcha</artifactId>
            <version>1.2.1</version>
        </dependency>

        <dependency>
            <groupId>com.wunding</groupId>
            <artifactId>learn-user-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.wunding</groupId>
                    <artifactId>learn-common-category</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wunding</groupId>
            <artifactId>learn-common-swagger</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <dependency>
            <groupId>io.opentelemetry</groupId>
            <artifactId>opentelemetry-exporter-otlp</artifactId>
        </dependency>



        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>


        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>io.fabric8</groupId>
                <artifactId>docker-maven-plugin</artifactId>
                <version>0.45.0</version>
                <configuration>
                    <skip>${skipDockerImage}</skip>
                    <!-- Docker 推送镜像仓库地址-->
                    <pushRegistry>${image.registry}</pushRegistry>
                    <images>
                        <image>
                            <!--由于推送到私有镜像仓库，镜像名需要添加仓库地址-->
                            <name>${image.registry}${image.registry.path}login:${image.tag}</name>
                            <!--定义镜像构建行为-->
                            <build>
                                <buildx>
                                    <builderName>mybuilder</builderName>
                                    <platforms>
                                        <platform>${docker.platforms}</platform>
                                    </platforms>
                                </buildx>
                                <dockerFileDir>${project.basedir}</dockerFileDir>
                                <dockerFile>${docker.file}</dockerFile>
                            </build>
                        </image>
                    </images>
                </configuration>
                <executions>
                    <execution>
                        <!--在哪个生命周期阶段执行-->
                        <phase>install</phase>
                        <!--执行别名-->
                        <id>build-image</id>
                        <goals>
                            <!--插件目标-->
                            <goal>build</goal>
                            <goal>push</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${java.version}</source>
                    <release>${java.version}</release>
                    <target>${java.version}</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <configuration>
                    <!--开启分层编译支持-->
                    <layers>
                        <enabled>true</enabled>
                    </layers>
                    <skip>${spring-boot.repackage.skip}</skip>
                    <finalName>${project.name}-${revision}-fat</finalName>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
